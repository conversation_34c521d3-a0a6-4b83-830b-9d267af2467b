@echo off
chcp 65001 >nul
title نظام مرجب - وضع التطوير

REM Set colors for development mode
color 0E

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                  نظام مرجب - وضع التطوير                    ║
echo ║                    للمطورين والاختبار                       ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔧 تشغيل النظام في وضع التطوير...
echo    • إعادة التحميل التلقائي
echo    • رسائل تفصيلية للأخطاء
echo    • أدوات التطوير مفعلة
echo.

REM Clean up ports
echo [1/3] 🧹 تنظيف المنافذ...
for /f "tokens=5" %%a in ('netstat -aon 2^>nul ^| find ":4001" ^| find "LISTENING"') do taskkill /f /pid %%a >nul 2>&1
for /f "tokens=5" %%a in ('netstat -aon 2^>nul ^| find ":4002" ^| find "LISTENING"') do taskkill /f /pid %%a >nul 2>&1
for /f "tokens=5" %%a in ('netstat -aon 2^>nul ^| find ":3001" ^| find "LISTENING"') do taskkill /f /pid %%a >nul 2>&1
for /f "tokens=5" %%a in ('netstat -aon 2^>nul ^| find ":3002" ^| find "LISTENING"') do taskkill /f /pid %%a >nul 2>&1
echo       ✅ تم تنظيف المنافذ

REM Quick dependency check
echo.
echo [2/3] 📦 فحص سريع للـ dependencies...
if not exist "apps\personnel-frontend\node_modules" (
    echo       تثبيت Personnel Frontend dependencies...
    cd apps\personnel-frontend
    call npm install --silent
    cd ..\..
)
if not exist "apps\assets-frontend\node_modules" (
    echo       تثبيت Assets Frontend dependencies...
    cd apps\assets-frontend
    call npm install --silent
    cd ..\..
)
echo       ✅ Dependencies جاهزة

REM Start services in development mode
echo.
echo [3/3] 🚀 تشغيل الخدمات في وضع التطوير...

echo       تشغيل Personnel API (وضع المراقبة)...
start "DEV: Personnel API" cmd /c "cd apps\personnel-api && npm run dev"
timeout /t 2 >nul

echo       تشغيل Assets API (وضع المراقبة)...
start "DEV: Assets API" cmd /c "cd apps\assets-api && npm run dev"
timeout /t 2 >nul

echo       تشغيل Personnel Frontend (وضع التطوير)...
start "DEV: Personnel Frontend" cmd /c "cd apps\personnel-frontend && npm run dev"
timeout /t 2 >nul

echo       تشغيل Assets Frontend (وضع التطوير)...
start "DEV: Assets Frontend" cmd /c "cd apps\assets-frontend && npm run dev"

echo.
echo ⏳ انتظار تشغيل الخدمات...
timeout /t 10 >nul

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🔧 وضع التطوير نشط! 🔧                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🌐 الروابط (وضع التطوير):
echo    ┌─ Personnel System:       http://localhost:3001
echo    └─ Assets System:         http://localhost:3002
echo.
echo 🔌 APIs (وضع المراقبة):
echo    ┌─ Personnel API:         http://localhost:4001
echo    └─ Assets API:           http://localhost:4002
echo.
echo 🔧 ميزات وضع التطوير:
echo    • Hot Reload مفعل
echo    • Source Maps متاحة
echo    • DevTools مفتوحة
echo    • Error Overlay مفعل
echo    • Fast Refresh للـ React
echo.
echo 💡 نصائح للتطوير:
echo    • استخدم F12 لفتح أدوات المطور
echo    • التغييرات ستظهر تلقائياً
echo    • راقب نوافذ الخدمات للأخطاء
echo    • استخدم stop-system.bat لإيقاف الخدمات
echo.
echo فتح المتصفحات...
start http://localhost:3001
timeout /t 2 >nul
start http://localhost:3002

echo.
echo اضغط أي مفتاح لإنهاء هذه النافذة (الخدمات ستبقى تعمل)...
pause >nul

@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');

:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary: #3b82f6;
  --primary-foreground: #ffffff;
  --secondary: #f1f5f9;
  --secondary-foreground: #0f172a;
  --muted: #f8fafc;
  --muted-foreground: #64748b;
  --accent: #0ea5e9;
  --accent-foreground: #ffffff;
  --border: #e2e8f0;
  --input: #ffffff;
  --ring: #3b82f6;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: 'Noto Sans Arabic', var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --primary: #3b82f6;
    --primary-foreground: #ffffff;
    --secondary: #1e293b;
    --secondary-foreground: #f1f5f9;
    --muted: #0f172a;
    --muted-foreground: #94a3b8;
    --accent: #0ea5e9;
    --accent-foreground: #ffffff;
    --border: #334155;
    --input: #1e293b;
    --ring: #3b82f6;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Noto Sans Arabic', Arial, sans-serif;
  direction: rtl;
  font-feature-settings: 'rlig' 1, 'calt' 1;
}

/* Modern scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--muted-foreground);
  border-radius: 4px;
  opacity: 0.5;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary);
  opacity: 0.8;
}

/* Custom focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-white;
}

/* Modern button styles */
.btn-primary {
  @apply bg-primary-600 hover:bg-primary-700 focus:bg-primary-700 text-white font-medium py-2.5 px-4 rounded-xl transition-all duration-200 shadow-soft hover:shadow-medium focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
}

.btn-secondary {
  @apply bg-secondary-100 hover:bg-secondary-200 focus:bg-secondary-200 text-secondary-900 font-medium py-2.5 px-4 rounded-xl transition-all duration-200 shadow-soft hover:shadow-medium focus:outline-none focus:ring-2 focus:ring-secondary-500 focus:ring-offset-2;
}

.btn-success {
  @apply bg-success-600 hover:bg-success-700 focus:bg-success-700 text-white font-medium py-2.5 px-4 rounded-xl transition-all duration-200 shadow-soft hover:shadow-medium focus:outline-none focus:ring-2 focus:ring-success-500 focus:ring-offset-2;
}

.btn-danger {
  @apply bg-danger-600 hover:bg-danger-700 focus:bg-danger-700 text-white font-medium py-2.5 px-4 rounded-xl transition-all duration-200 shadow-soft hover:shadow-medium focus:outline-none focus:ring-2 focus:ring-danger-500 focus:ring-offset-2;
}

/* Modern card styles */
.card {
  @apply bg-white rounded-2xl shadow-soft border border-secondary-200 overflow-hidden;
}

.card-header {
  @apply px-6 py-4 border-b border-secondary-200 bg-secondary-50;
}

.card-body {
  @apply px-6 py-4;
}

.card-footer {
  @apply px-6 py-4 border-t border-secondary-200 bg-secondary-50;
}

/* Modern form styles */
.form-input {
  @apply block w-full rounded-xl border-secondary-300 shadow-soft focus:border-primary-500 focus:ring-primary-500 focus:ring-1 transition-all duration-200;
}

.form-select {
  @apply block w-full rounded-xl border-secondary-300 shadow-soft focus:border-primary-500 focus:ring-primary-500 focus:ring-1 transition-all duration-200;
}

.form-textarea {
  @apply block w-full rounded-xl border-secondary-300 shadow-soft focus:border-primary-500 focus:ring-primary-500 focus:ring-1 transition-all duration-200 resize-none;
}

/* Modern navigation */
.nav-link {
  @apply text-secondary-600 hover:text-primary-600 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:bg-primary-50;
}

.nav-link-active {
  @apply text-primary-600 bg-primary-50 px-3 py-2 rounded-lg text-sm font-medium;
}

/* Status badges */
.badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-success {
  @apply bg-success-100 text-success-800;
}

.badge-warning {
  @apply bg-warning-100 text-warning-800;
}

.badge-danger {
  @apply bg-danger-100 text-danger-800;
}

.badge-primary {
  @apply bg-primary-100 text-primary-800;
}

.badge-secondary {
  @apply bg-secondary-100 text-secondary-800;
}

/* Print styles for A4 with 2cm margins */
@media print {
  @page {
    size: A4;
    margin: 2cm;
  }

  body {
    font-size: 12pt;
    line-height: 1.4;
  }

  .no-print {
    display: none !important;
  }

  .print-only {
    display: block !important;
  }
}

.print-only {
  display: none;
}

/* Loading animations */
.loading-spinner {
  @apply animate-spin rounded-full h-6 w-6 border-2 border-secondary-300 border-t-primary-600;
}

.loading-pulse {
  @apply animate-pulse-soft;
}

/* Modern table styles */
.table {
  @apply w-full divide-y divide-secondary-200;
}

.table-header {
  @apply bg-secondary-50;
}

.table-header-cell {
  @apply px-6 py-3 text-right text-xs font-medium text-secondary-500 uppercase tracking-wider;
}

.table-body {
  @apply bg-white divide-y divide-secondary-200;
}

.table-row {
  @apply hover:bg-secondary-50 transition-colors duration-150;
}

.table-cell {
  @apply px-6 py-4 whitespace-nowrap text-sm text-secondary-900;
}

/* Modern alerts */
.alert {
  @apply p-4 rounded-xl border;
}

.alert-success {
  @apply bg-success-50 border-success-200 text-success-800;
}

.alert-warning {
  @apply bg-warning-50 border-warning-200 text-warning-800;
}

.alert-danger {
  @apply bg-danger-50 border-danger-200 text-danger-800;
}

.alert-info {
  @apply bg-primary-50 border-primary-200 text-primary-800;
}

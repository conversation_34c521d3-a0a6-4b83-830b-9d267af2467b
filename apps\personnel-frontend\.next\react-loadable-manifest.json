{"..\\..\\..\\node_modules\\jspdf\\dist\\jspdf.es.min.js -> canvg": {"id": "..\\..\\..\\node_modules\\jspdf\\dist\\jspdf.es.min.js -> canvg", "files": ["static/chunks/_app-pages-browser_node_modules_canvg_lib_index_es_js.js"]}, "..\\..\\..\\node_modules\\jspdf\\dist\\jspdf.es.min.js -> dompurify": {"id": "..\\..\\..\\node_modules\\jspdf\\dist\\jspdf.es.min.js -> dompurify", "files": ["static/chunks/_app-pages-browser_node_modules_dompurify_dist_purify_es_mjs.js"]}, "..\\..\\..\\node_modules\\jspdf\\dist\\jspdf.es.min.js -> html2canvas": {"id": "..\\..\\..\\node_modules\\jspdf\\dist\\jspdf.es.min.js -> html2canvas", "files": []}, "..\\..\\..\\node_modules\\next\\dist\\client\\dev\\hot-reloader\\app\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts": {"id": "..\\..\\..\\node_modules\\next\\dist\\client\\dev\\hot-reloader\\app\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts", "files": ["static/chunks/_app-pages-browser_node_modules_next_dist_client_dev_noop-turbopack-hmr_js.js"]}}
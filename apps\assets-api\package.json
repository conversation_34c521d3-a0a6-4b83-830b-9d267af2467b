{"name": "@mergeb/assets-api", "version": "0.1.0", "type": "module", "scripts": {"dev": "node --watch dist/index.js", "build": "tsc -p tsconfig.json", "start": "node dist/index.js"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "helmet": "^7.1.0", "ioredis": "^5.4.1", "jsonwebtoken": "^9.0.2", "pg": "^8.11.5", "zod": "^3.23.8"}, "devDependencies": {"@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.11.30", "ts-node": "^10.9.2", "typescript": "^5.4.5"}}
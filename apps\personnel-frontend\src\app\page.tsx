import Link from "next/link";

export default function Home() {
  return (
    <div className="min-h-screen flex items-center justify-center p-4 relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse-soft"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-success-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse-soft animation-delay-2000"></div>
        <div className="absolute top-40 left-40 w-80 h-80 bg-secondary-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse-soft animation-delay-4000"></div>
      </div>

      <div className="relative z-10 max-w-lg w-full">
        <div className="card backdrop-blur-sm bg-white/90 border-white/20 shadow-large">
          <div className="card-body text-center">
            {/* Logo/Icon */}
            <div className="mb-8">
              <div className="w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-600 rounded-3xl flex items-center justify-center mx-auto mb-4 shadow-medium">
                <span className="text-white text-3xl font-bold">م</span>
              </div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-primary-600 to-primary-800 bg-clip-text text-transparent mb-3">
                نظام إدارة الموارد البشرية
              </h1>
              <p className="text-secondary-600 text-lg">
                مرحباً بك في نظام مرجب الشامل لإدارة القوة العمومية
              </p>
            </div>

            {/* Main Actions */}
            <div className="space-y-4 mb-8">
              <Link
                href="/login"
                className="btn-primary w-full text-lg py-4 group relative overflow-hidden"
              >
                <span className="relative z-10 flex items-center justify-center">
                  <span className="ml-2">🔐</span>
                  تسجيل الدخول
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-primary-600 to-primary-700 transform scale-x-0 group-hover:scale-x-100 transition-transform origin-left duration-300"></div>
              </Link>

              {/* Quick Access Grid */}
              <div className="grid grid-cols-2 gap-4 mt-8">
                <Link
                  href="/employees"
                  className="group relative bg-gradient-to-br from-success-500 to-success-600 text-white py-4 px-4 rounded-xl hover:from-success-600 hover:to-success-700 transition-all duration-300 shadow-soft hover:shadow-medium transform hover:scale-105"
                >
                  <div className="text-center">
                    <span className="text-2xl mb-2 block">👥</span>
                    <span className="text-sm font-medium">الموظفون</span>
                  </div>
                </Link>
                <Link
                  href="/dashboard"
                  className="group relative bg-gradient-to-br from-secondary-500 to-secondary-600 text-white py-4 px-4 rounded-xl hover:from-secondary-600 hover:to-secondary-700 transition-all duration-300 shadow-soft hover:shadow-medium transform hover:scale-105"
                >
                  <div className="text-center">
                    <span className="text-2xl mb-2 block">📊</span>
                    <span className="text-sm font-medium">لوحة التحكم</span>
                  </div>
                </Link>
              </div>
            </div>

            {/* System Info */}
            <div className="border-t border-secondary-200 pt-6">
              <div className="flex items-center justify-between text-sm text-secondary-500">
                <div className="flex items-center space-x-2 space-x-reverse">
                  <span className="w-2 h-2 bg-success-500 rounded-full animate-pulse"></span>
                  <span>النظام متاح</span>
                </div>
                <div className="text-left">
                  <p>نظام مرجب</p>
                  <p className="text-xs">الإصدار 2.0</p>
                </div>
              </div>
            </div>

            {/* External Links */}
            <div className="mt-6 pt-6 border-t border-secondary-200">
              <div className="flex justify-center">
                <a
                  href="http://127.0.0.1:3002"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-sm text-secondary-600 hover:text-primary-600 transition-colors duration-200 group"
                >
                  <span className="ml-2">🔗</span>
                  <span className="group-hover:underline">نظام إدارة القوة العمومية</span>
                  <span className="mr-1 transform group-hover:translate-x-1 transition-transform duration-200">←</span>
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-8">
          <p className="text-secondary-500 text-sm">
            © 2024 نظام مرجب لإدارة الموارد البشرية. جميع الحقوق محفوظة.
          </p>
        </div>
      </div>
    </div>
  );
}

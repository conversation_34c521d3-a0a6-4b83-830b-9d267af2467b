'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

interface Employee {
  id: string;
  name: string;
  rankKey: string;
  militaryNumber: string;
  nationalId: string;
  unitId?: number;
  unitName?: string;
  bankName?: string;
  bankBranch?: string;
  bankAccount?: string;
}

interface ReportStats {
  totalEmployees: number;
  byRank: { [key: string]: number };
  byCategory: { officer: number; nco: number; employee: number };
  byUnit: { [key: string]: number };
  onLeave: number;
}

interface Rank {
  key: string;
  label: string;
  category: 'officer' | 'nco' | 'employee';
  order: number;
}

export default function ReportsPage() {
  const [stats, setStats] = useState<ReportStats>({
    totalEmployees: 0,
    byRank: {},
    byCategory: { officer: 0, nco: 0, employee: 0 },
    byUnit: {},
    onLeave: 0
  });
  const [ranks, setRanks] = useState<Rank[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedReport, setSelectedReport] = useState<string>('');
  const [editingReport, setEditingReport] = useState<string>('');
  const [reportData, setReportData] = useState<Employee[]>([]);
  const [showBankReport, setShowBankReport] = useState<boolean>(false);
  const router = useRouter();

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (!token) {
      router.push('/login');
      return;
    }

    fetchData(token);
  }, [router]);

  const fetchData = async (token: string) => {
    try {
      // Fetch ranks
      const ranksResponse = await fetch('http://127.0.0.1:4001/ranks', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (ranksResponse.ok) {
        const ranksData = await ranksResponse.json();
        setRanks(ranksData);
      }

      // Fetch employees from API
      const employeesResponse = await fetch('http://127.0.0.1:4001/employees', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      let employeesData: Employee[] = [];
      if (employeesResponse.ok) {
        const rawEmployees = await employeesResponse.json();
        employeesData = rawEmployees.map((emp: any) => ({
          id: emp.id,
          name: emp.name,
          rankKey: emp.rank_key,
          militaryNumber: emp.military_number,
          nationalId: emp.national_id,
          unitId: emp.unit_id,
          unitName: emp.unit_name || 'غير محدد',
          bankName: emp.bank_name,
          bankBranch: emp.bank_branch,
          bankAccount: emp.bank_account
        }));
      }

      setEmployees(employeesData);

      // Calculate statistics from real data
      const byRank: { [key: string]: number } = {};
      const byUnit: { [key: string]: number } = {};
      let officers = 0, ncos = 0, employees = 0;

      employeesData.forEach(emp => {
        // Count by rank
        byRank[emp.rankKey] = (byRank[emp.rankKey] || 0) + 1;

        // Count by unit
        if (emp.unitName) {
          byUnit[emp.unitName] = (byUnit[emp.unitName] || 0) + 1;
        }

        // Count by category
        const rank = ranks.find((r: Rank) => r.key === emp.rankKey);
        if (rank) {
          if (rank.category === 'officer') officers++;
          else if (rank.category === 'nco') ncos++;
          else if (rank.category === 'employee') employees++;
        }
      });

      setStats({
        totalEmployees: employeesData.length,
        byRank,
        byCategory: { officer: officers, nco: ncos, employee: employees },
        byUnit,
        onLeave: 0 // TODO: Calculate from leaves table
      });

      setLoading(false);
    } catch (err) {
      setError('خطأ في تحميل البيانات');
      setLoading(false);
    }
  };

  const getRankLabel = (rankKey: string) => {
    const rank = ranks.find(r => r.key === rankKey);
    return rank ? rank.label : rankKey;
  };

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'officer': return 'الضباط';
      case 'nco': return 'ضباط الصف';
      case 'employee': return 'الموظفون';
      default: return category;
    }
  };

  const sortEmployeesByRank = (employees: Employee[]) => {
    const rankOrder = new Map(ranks.map(r => [r.key, r.order]));
    return employees.sort((a, b) => (rankOrder.get(a.rankKey) || 999) - (rankOrder.get(b.rankKey) || 999));
  };

  const getEmployeesByRank = (rankKey: string) => {
    return employees.filter(emp => emp.rankKey === rankKey);
  };

  const exportToExcel = (reportType: string) => {
    let data: Employee[] = [];
    let filename = '';
    let worksheetData: any[] = [];

    switch (reportType) {
      case 'bank':
        data = employees.filter(emp => emp.bankName || emp.bankAccount);
        filename = 'البيانات-المصرفية';
        worksheetData = sortEmployeesByRank(data).map(emp => ({
          'الرقم العسكري': emp.militaryNumber,
          'الرتبة': getRankLabel(emp.rankKey),
          'الاسم': emp.name,
          'الرقم الوطني': emp.nationalId,
          'اسم المصرف': emp.bankName || 'غير محدد',
          'فرع المصرف': emp.bankBranch || 'غير محدد',
          'رقم الحساب': emp.bankAccount || 'غير محدد'
        }));
        break;
      case 'rank':
        data = employees;
        filename = 'تقرير-حسب-الرتبة';
        worksheetData = sortEmployeesByRank(data).map(emp => ({
          'الاسم': emp.name,
          'الرتبة': getRankLabel(emp.rankKey),
          'الرقم العسكري': emp.militaryNumber,
          'الرقم الوطني': emp.nationalId,
          'الوحدة': emp.unitName || 'غير محدد'
        }));
        break;
      default:
        data = employees;
        filename = 'تقرير-عام';
        worksheetData = sortEmployeesByRank(data).map(emp => ({
          'الاسم': emp.name,
          'الرتبة': getRankLabel(emp.rankKey),
          'الرقم العسكري': emp.militaryNumber,
          'الرقم الوطني': emp.nationalId,
          'الوحدة': emp.unitName || 'غير محدد'
        }));
    }

    const worksheet = XLSX.utils.json_to_sheet(worksheetData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'التقرير');

    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    const data_blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    saveAs(data_blob, `${filename}.xlsx`);
  };

  const exportToPDF = async (reportType: string) => {
    const element = document.getElementById(`report-${reportType}`);
    if (!element) return;

    const canvas = await html2canvas(element);
    const imgData = canvas.toDataURL('image/png');

    const pdf = new jsPDF();
    const imgWidth = 210;
    const pageHeight = 295;
    const imgHeight = (canvas.height * imgWidth) / canvas.width;
    let heightLeft = imgHeight;

    let position = 0;

    pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
    heightLeft -= pageHeight;

    while (heightLeft >= 0) {
      position = heightLeft - imgHeight;
      pdf.addPage();
      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;
    }

    pdf.save(`تقرير-${reportType}.pdf`);
  };

  const handlePrintReport = (reportType: string) => {
    const printWindow = window.open('', '_blank');
    if (!printWindow) return;

    let reportContent = '';
    const currentDate = new Date().toLocaleDateString('ar-SA');

    switch (reportType) {
      case 'rank':
        reportContent = generateRankReport();
        break;
      case 'category':
        reportContent = generateCategoryReport();
        break;
      case 'unit':
        reportContent = generateUnitReport();
        break;
      case 'leave':
        reportContent = generateLeaveReport();
        break;
      case 'comprehensive':
        reportContent = generateComprehensiveReport();
        break;
      case 'bank':
        reportContent = generateBankReport();
        break;
    }

    printWindow.document.write(`
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <title>تقرير - نظام الموارد البشرية</title>
        <style>
          @page { size: A4; margin: 2cm; }
          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: rtl; }
          .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px; }
          .header h1 { margin: 0; font-size: 24px; }
          .header h2 { margin: 5px 0; font-size: 18px; color: #666; }
          .header p { margin: 5px 0; color: #888; }
          table { width: 100%; border-collapse: collapse; margin: 20px 0; }
          th, td { border: 1px solid #ddd; padding: 12px; text-align: right; }
          th { background-color: #f5f5f5; font-weight: bold; }
          .total-row { background-color: #e8f4f8; font-weight: bold; }
          .section { margin: 30px 0; }
          .section h3 { color: #333; border-bottom: 1px solid #ddd; padding-bottom: 10px; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>الجمهورية الليبية</h1>
          <h2>وزارة الداخلية</h2>
          <p>تاريخ التقرير: ${currentDate}</p>
        </div>
        ${reportContent}
      </body>
      </html>
    `);
    
    printWindow.document.close();
    printWindow.print();
  };

  const generateRankReport = () => {
    // Sort ranks by order
    const sortedRanks = ranks
      .filter(rank => stats.byRank[rank.key] > 0)
      .sort((a, b) => a.order - b.order);

    return `
      <div class="section">
        <h3>تقرير حسب الرتبة</h3>
        <table>
          <thead>
            <tr>
              <th>الرتبة</th>
              <th>العدد</th>
              <th>النسبة المئوية</th>
            </tr>
          </thead>
          <tbody>
            ${sortedRanks.map(rank => `
              <tr>
                <td>${rank.label}</td>
                <td>${stats.byRank[rank.key]}</td>
                <td>${((stats.byRank[rank.key] / stats.totalEmployees) * 100).toFixed(1)}%</td>
              </tr>
            `).join('')}
            <tr class="total-row">
              <td>المجموع</td>
              <td>${stats.totalEmployees}</td>
              <td>100%</td>
            </tr>
          </tbody>
        </table>
      </div>
    `;
  };

  const generateCategoryReport = () => {
    return `
      <div class="section">
        <h3>تقرير حسب الفئة</h3>
        <table>
          <thead>
            <tr>
              <th>الفئة</th>
              <th>العدد</th>
              <th>النسبة المئوية</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>الضباط</td>
              <td>${stats.byCategory.officer}</td>
              <td>${((stats.byCategory.officer / stats.totalEmployees) * 100).toFixed(1)}%</td>
            </tr>
            <tr>
              <td>ضباط الصف</td>
              <td>${stats.byCategory.nco}</td>
              <td>${((stats.byCategory.nco / stats.totalEmployees) * 100).toFixed(1)}%</td>
            </tr>
            <tr>
              <td>الموظفون</td>
              <td>${stats.byCategory.employee}</td>
              <td>${((stats.byCategory.employee / stats.totalEmployees) * 100).toFixed(1)}%</td>
            </tr>
            <tr class="total-row">
              <td>المجموع</td>
              <td>${stats.totalEmployees}</td>
              <td>100%</td>
            </tr>
          </tbody>
        </table>
      </div>
    `;
  };

  const generateUnitReport = () => {
    return `
      <div class="section">
        <h3>تقرير حسب الوحدة</h3>
        <table>
          <thead>
            <tr>
              <th>الوحدة</th>
              <th>العدد</th>
              <th>النسبة المئوية</th>
            </tr>
          </thead>
          <tbody>
            ${Object.entries(stats.byUnit).map(([unit, count]) => `
              <tr>
                <td>${unit}</td>
                <td>${count}</td>
                <td>${((count / stats.totalEmployees) * 100).toFixed(1)}%</td>
              </tr>
            `).join('')}
            <tr class="total-row">
              <td>المجموع</td>
              <td>${stats.totalEmployees}</td>
              <td>100%</td>
            </tr>
          </tbody>
        </table>
      </div>
    `;
  };

  const generateLeaveReport = () => {
    return `
      <div class="section">
        <h3>تقرير الإجازات</h3>
        <table>
          <thead>
            <tr>
              <th>الحالة</th>
              <th>العدد</th>
              <th>النسبة المئوية</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>في إجازة</td>
              <td>${stats.onLeave}</td>
              <td>${((stats.onLeave / stats.totalEmployees) * 100).toFixed(1)}%</td>
            </tr>
            <tr>
              <td>في الخدمة</td>
              <td>${stats.totalEmployees - stats.onLeave}</td>
              <td>${(((stats.totalEmployees - stats.onLeave) / stats.totalEmployees) * 100).toFixed(1)}%</td>
            </tr>
            <tr class="total-row">
              <td>المجموع</td>
              <td>${stats.totalEmployees}</td>
              <td>100%</td>
            </tr>
          </tbody>
        </table>
      </div>
    `;
  };

  const generateBankReport = () => {
    const bankEmployees = employees.filter(emp => emp.bankName || emp.bankAccount);
    const sortedBankEmployees = sortEmployeesByRank(bankEmployees);

    return `
      <div class="section">
        <h3>تقرير البيانات المصرفية</h3>
        <table>
          <thead>
            <tr>
              <th>الرقم العسكري</th>
              <th>الرتبة</th>
              <th>الاسم</th>
              <th>الرقم الوطني</th>
              <th>اسم المصرف</th>
              <th>فرع المصرف</th>
              <th>رقم الحساب</th>
            </tr>
          </thead>
          <tbody>
            ${sortedBankEmployees.map(emp => `
              <tr>
                <td>${emp.militaryNumber}</td>
                <td>${getRankLabel(emp.rankKey)}</td>
                <td>${emp.name}</td>
                <td>${emp.nationalId}</td>
                <td>${emp.bankName || 'غير محدد'}</td>
                <td>${emp.bankBranch || 'غير محدد'}</td>
                <td>${emp.bankAccount || 'غير محدد'}</td>
              </tr>
            `).join('')}
            <tr class="total-row">
              <td colspan="7">المجموع: ${bankEmployees.length} موظف</td>
            </tr>
          </tbody>
        </table>
      </div>
    `;
  };

  const generateComprehensiveReport = () => {
    return `
      ${generateCategoryReport()}
      ${generateRankReport()}
      ${generateUnitReport()}
      ${generateLeaveReport()}
    `;
  };

  const handleLogout = () => {
    localStorage.removeItem('token');
    router.push('/login');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري تحميل البيانات...</p>
        </div>
      </div>
    );
  }

  // Add effect to update progress bar widths after render
  useEffect(() => {
    const bars = document.querySelectorAll<HTMLDivElement>('[data-width]');
    bars.forEach(bar => {
      const width = bar.getAttribute('data-width');
      if (width) {
        bar.style.width = `${width}%`;
      }
    });
  }, [stats.byRank, stats.byUnit]);

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4 space-x-reverse">
              <h1 className="text-xl font-semibold text-gray-900">التقارير والإحصائيات</h1>
              <Link
                href="/dashboard"
                className="text-blue-600 hover:text-blue-800 text-sm"
              >
                ← العودة للوحة التحكم
              </Link>
            </div>
            <button
              type="button"
              onClick={handleLogout}
              className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors text-sm"
            >
              تسجيل الخروج
            </button>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
            {error}
          </div>
        )}

        {/* Summary Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600">إجمالي الموظفين</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalEmployees}</p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <span className="text-blue-600 text-xl">👥</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600">الضباط</p>
                <p className="text-2xl font-bold text-gray-900">{stats.byCategory.officer}</p>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <span className="text-green-600 text-xl">⭐</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600">ضباط الصف</p>
                <p className="text-2xl font-bold text-gray-900">{stats.byCategory.nco}</p>
              </div>
              <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                <span className="text-yellow-600 text-xl">🎖️</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600">في إجازة</p>
                <p className="text-2xl font-bold text-gray-900">{stats.onLeave}</p>
              </div>
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                <span className="text-red-600 text-xl">🏖️</span>
              </div>
            </div>
          </div>
        </div>

        {/* Report Types Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center mb-4">
              <span className="text-2xl ml-3">📊</span>
              <h3 className="text-lg font-semibold text-gray-900">تقرير حسب الرتبة</h3>
            </div>
            <p className="text-gray-600 mb-4">عرض توزيع الموظفين حسب الرتب العسكرية مع التفاصيل</p>
            <div className="space-y-2">
              <button
                type="button"
                onClick={() => setEditingReport(editingReport === 'rank' ? '' : 'rank')}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
              >
                {editingReport === 'rank' ? 'إخفاء التفاصيل' : 'عرض التفاصيل'}
              </button>
              <div className="flex space-x-2 space-x-reverse">
                <button
                  type="button"
                  onClick={() => exportToExcel('rank')}
                  className="flex-1 bg-green-600 text-white py-1 px-2 rounded text-sm hover:bg-green-700 transition-colors"
                >
                  Excel
                </button>
                <button
                  type="button"
                  onClick={() => handlePrintReport('rank')}
                  className="flex-1 bg-red-600 text-white py-1 px-2 rounded text-sm hover:bg-red-700 transition-colors"
                >
                  PDF
                </button>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center mb-4">
              <span className="text-2xl ml-3">👥</span>
              <h3 className="text-lg font-semibold text-gray-900">تقرير حسب الفئة</h3>
            </div>
            <p className="text-gray-600 mb-4">عرض توزيع الموظفين حسب الفئات (ضباط، ضباط صف، موظفون)</p>
            <div className="flex space-x-2 space-x-reverse">
              <button
                type="button"
                onClick={() => exportToExcel('category')}
                className="flex-1 bg-green-600 text-white py-1 px-2 rounded text-sm hover:bg-green-700 transition-colors"
              >
                Excel
              </button>
              <button
                type="button"
                onClick={() => handlePrintReport('category')}
                className="flex-1 bg-red-600 text-white py-1 px-2 rounded text-sm hover:bg-red-700 transition-colors"
              >
                PDF
              </button>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center mb-4">
              <span className="text-2xl ml-3">🏢</span>
              <h3 className="text-lg font-semibold text-gray-900">تقرير حسب الوحدة</h3>
            </div>
            <p className="text-gray-600 mb-4">عرض توزيع الموظفين حسب الوحدات الإدارية</p>
            <div className="flex space-x-2 space-x-reverse">
              <button
                type="button"
                onClick={() => exportToExcel('unit')}
                className="flex-1 bg-green-600 text-white py-1 px-2 rounded text-sm hover:bg-green-700 transition-colors"
              >
                Excel
              </button>
              <button
                type="button"
                onClick={() => handlePrintReport('unit')}
                className="flex-1 bg-red-600 text-white py-1 px-2 rounded text-sm hover:bg-red-700 transition-colors"
              >
                PDF
              </button>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center mb-4">
              <span className="text-2xl ml-3">🏖️</span>
              <h3 className="text-lg font-semibold text-gray-900">تقرير الإجازات</h3>
            </div>
            <p className="text-gray-600 mb-4">عرض حالة الموظفين من ناحية الإجازات</p>
            <div className="flex space-x-2 space-x-reverse">
              <button
                type="button"
                onClick={() => exportToExcel('leave')}
                className="flex-1 bg-green-600 text-white py-1 px-2 rounded text-sm hover:bg-green-700 transition-colors"
              >
                Excel
              </button>
              <button
                type="button"
                onClick={() => handlePrintReport('leave')}
                className="flex-1 bg-red-600 text-white py-1 px-2 rounded text-sm hover:bg-red-700 transition-colors"
              >
                PDF
              </button>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center mb-4">
              <span className="text-2xl ml-3">📋</span>
              <h3 className="text-lg font-semibold text-gray-900">التقرير الشامل</h3>
            </div>
            <p className="text-gray-600 mb-4">تقرير شامل يحتوي على جميع الإحصائيات</p>
            <div className="flex space-x-2 space-x-reverse">
              <button
                type="button"
                onClick={() => exportToExcel('comprehensive')}
                className="flex-1 bg-green-600 text-white py-1 px-2 rounded text-sm hover:bg-green-700 transition-colors"
              >
                Excel
              </button>
              <button
                type="button"
                onClick={() => handlePrintReport('comprehensive')}
                className="flex-1 bg-red-600 text-white py-1 px-2 rounded text-sm hover:bg-red-700 transition-colors"
              >
                PDF
              </button>
            </div>
          </div>

          {/* Bank Report Card */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center mb-4">
              <span className="text-2xl ml-3">🏦</span>
              <h3 className="text-lg font-semibold text-gray-900">البيانات المصرفية</h3>
            </div>
            <p className="text-gray-600 mb-4">تقرير بالبيانات المصرفية للموظفين</p>
            <div className="space-y-2">
              <button
                type="button"
                onClick={() => setShowBankReport(!showBankReport)}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
              >
                {showBankReport ? 'إخفاء التفاصيل' : 'عرض التفاصيل'}
              </button>
              <div className="flex space-x-2 space-x-reverse">
                <button
                  type="button"
                  onClick={() => exportToExcel('bank')}
                  className="flex-1 bg-green-600 text-white py-1 px-2 rounded text-sm hover:bg-green-700 transition-colors"
                >
                  Excel
                </button>
                <button
                  type="button"
                  onClick={() => handlePrintReport('bank')}
                  className="flex-1 bg-red-600 text-white py-1 px-2 rounded text-sm hover:bg-red-700 transition-colors"
                >
                  PDF
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Detailed Report View */}
        {editingReport === 'rank' && (
          <div className="bg-white rounded-lg shadow p-6 mb-8" id="report-rank">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-semibold text-gray-900">تقرير مفصل حسب الرتبة</h3>
              <div className="flex space-x-2 space-x-reverse">
                <button
                  type="button"
                  onClick={() => exportToExcel('rank')}
                  className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-sm"
                >
                  تصدير Excel
                </button>
                <button
                  type="button"
                  onClick={() => exportToPDF('rank')}
                  className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors text-sm"
                >
                  تصدير PDF
                </button>
              </div>
            </div>

            <div className="space-y-6">
              {ranks
                .filter(rank => stats.byRank[rank.key] > 0)
                .sort((a, b) => a.order - b.order)
                .map(rank => {
                  const rankEmployees = getEmployeesByRank(rank.key);
                  return (
                    <div key={rank.key} className="border border-gray-200 rounded-lg p-4">
                      <h4 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                        <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm ml-3">
                          {rankEmployees.length}
                        </span>
                        {rank.label}
                      </h4>

                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">الاسم</th>
                              <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">الرقم العسكري</th>
                              <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">الرقم الوطني</th>
                              <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">الوحدة</th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {rankEmployees.map(employee => (
                              <tr key={employee.id} className="hover:bg-gray-50">
                                <td className="px-4 py-2 text-sm font-medium text-gray-900">{employee.name}</td>
                                <td className="px-4 py-2 text-sm text-gray-500 font-mono">{employee.militaryNumber}</td>
                                <td className="px-4 py-2 text-sm text-gray-500 font-mono">{employee.nationalId}</td>
                                <td className="px-4 py-2 text-sm text-gray-500">{employee.unitName || 'غير محدد'}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  );
                })}
            </div>
          </div>
        )}

        {/* Bank Report Details */}
        {showBankReport && (
          <div className="bg-white rounded-lg shadow p-6 mb-8" id="report-bank">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-semibold text-gray-900">تقرير البيانات المصرفية</h3>
              <div className="flex space-x-2 space-x-reverse">
                <button
                  type="button"
                  onClick={() => exportToExcel('bank')}
                  className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-sm"
                >
                  تصدير Excel
                </button>
                <button
                  type="button"
                  onClick={() => exportToPDF('bank')}
                  className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors text-sm"
                >
                  تصدير PDF
                </button>
              </div>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الرقم العسكري</th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الرتبة</th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الاسم</th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الرقم الوطني</th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">اسم المصرف</th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">فرع المصرف</th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">رقم الحساب</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {sortEmployeesByRank(employees.filter(emp => emp.bankName || emp.bankAccount)).map(employee => (
                    <tr key={employee.id} className="hover:bg-gray-50">
                      <td className="px-4 py-3 text-sm font-mono text-gray-900">{employee.militaryNumber}</td>
                      <td className="px-4 py-3 text-sm text-gray-500">{getRankLabel(employee.rankKey)}</td>
                      <td className="px-4 py-3 text-sm font-medium text-gray-900">{employee.name}</td>
                      <td className="px-4 py-3 text-sm font-mono text-gray-500">{employee.nationalId}</td>
                      <td className="px-4 py-3 text-sm text-gray-500">{employee.bankName || 'غير محدد'}</td>
                      <td className="px-4 py-3 text-sm text-gray-500">{employee.bankBranch || 'غير محدد'}</td>
                      <td className="px-4 py-3 text-sm font-mono text-gray-500">{employee.bankAccount || 'غير محدد'}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
              {employees.filter(emp => emp.bankName || emp.bankAccount).length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  لا توجد بيانات مصرفية مسجلة
                </div>
              )}
            </div>
          </div>
        )}

        {/* Quick Stats Tables */}
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        data-width={((count / Math.max(...Object.values(stats.byRank))) * 100)}
                      ></div>
            <div className="space-y-2">
              {Object.entries(stats.byRank).slice(0, 5).map(([rankKey, count]) => (
                <div key={rankKey} className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="text-gray-700">{getRankLabel(rankKey)}</span>
                  <div className="flex items-center">
                    <span className="text-gray-900 font-medium ml-2">{count}</span>
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        style={{ width: `${(count / Math.max(...Object.values(stats.byRank))) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              ))}
              <div className="pt-2">
                <button
                  type="button"
                  onClick={() => handlePrintReport('rank')}
                  className="text-blue-600 hover:text-blue-800 text-sm"
                >
                  عرض التقرير الكامل ←
                </button>
              </div>
            </div>
          </div>

          {/* Unit Distribution */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">توزيع الوحدات</h3>
            <div className="space-y-2">
              {Object.entries(stats.byUnit).map(([unit, count]) => (
                <div key={unit} className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="text-gray-700">{unit}</span>
                  <div className="flex items-center">
                    <span className="text-gray-900 font-medium ml-2">{count}</span>
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-green-600 h-2 rounded-full"
                        style={{ width: `${(count / Math.max(...Object.values(stats.byUnit))) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              ))}
              <div className="pt-2">
                <button
                  type="button"
                  onClick={() => handlePrintReport('unit')}
                  className="text-green-600 hover:text-green-800 text-sm"
                >
                  عرض التقرير الكامل ←
                </button>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

{"name": "personnel-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 3001", "build": "next build", "start": "next start --port 3001", "lint": "eslint"}, "dependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/file-saver": "^2.0.7", "@types/qrcode": "^1.5.5", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "jspdf": "^3.0.2", "next": "15.5.2", "qrcode": "^1.5.4", "react": "19.1.0", "react-dom": "19.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.2", "tailwindcss": "^4", "typescript": "^5"}}
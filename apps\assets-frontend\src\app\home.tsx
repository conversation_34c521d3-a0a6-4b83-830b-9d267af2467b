'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

interface AssetStats {
  total: number;
  weapons: number;
  vehicles: number;
  radios: number;
  available: number;
  assigned: number;
  maintenance: number;
}

export default function AssetsHomePage() {
  const [stats, setStats] = useState<AssetStats>({
    total: 0,
    weapons: 0,
    vehicles: 0,
    radios: 0,
    available: 0,
    assigned: 0,
    maintenance: 0
  });
  const [loading, setLoading] = useState(true);
  const [showDashboard, setShowDashboard] = useState(false);

  useEffect(() => {
    // Check if user is logged in
    const token = localStorage.getItem('token');
    if (!token) {
      setShowDashboard(false);
      setLoading(false);
      return;
    }

    setShowDashboard(true);
    fetchStats();
  }, []);

  const fetchStats = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://127.0.0.1:4002/stats', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLogin = async () => {
    try {
      const response = await fetch('http://127.0.0.1:4002/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username: 'admin', password: 'admin' }),
      });

      if (response.ok) {
        const data = await response.json();
        localStorage.setItem('token', data.token);
        setShowDashboard(true);
        fetchStats();
      }
    } catch (error) {
      console.error('Login error:', error);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('token');
    setShowDashboard(false);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="loading-spinner w-12 h-12"></div>
      </div>
    );
  }

  // Login Interface
  if (!showDashboard) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4 relative overflow-hidden">
        {/* Background decorations */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-asset-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse-soft"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-primary-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse-soft animation-delay-2000"></div>
          <div className="absolute top-40 left-40 w-80 h-80 bg-secondary-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse-soft animation-delay-4000"></div>
        </div>

        <div className="relative z-10 max-w-lg w-full">
          <div className="card backdrop-blur-sm bg-white/90 border-white/20 shadow-large">
            <div className="card-body text-center">
              {/* Logo/Icon */}
              <div className="mb-8">
                <div className="w-20 h-20 bg-gradient-to-br from-asset-500 to-asset-600 rounded-3xl flex items-center justify-center mx-auto mb-4 shadow-medium">
                  <span className="text-white text-3xl font-bold">ق</span>
                </div>
                <h1 className="text-4xl font-bold bg-gradient-to-r from-asset-600 to-asset-800 bg-clip-text text-transparent mb-3">
                  نظام إدارة القوة العمومية
                </h1>
                <p className="text-secondary-600 text-lg">
                  إدارة الأسلحة والمركبات وأجهزة اللاسلكي
                </p>
              </div>

              {/* Login Button */}
              <div className="space-y-4 mb-8">
                <button
                  onClick={handleLogin}
                  className="btn-primary w-full text-lg py-4 group relative overflow-hidden"
                >
                  <span className="relative z-10 flex items-center justify-center">
                    <span className="ml-2">🔐</span>
                    تسجيل الدخول
                  </span>
                </button>

                {/* Quick Access Grid */}
                <div className="grid grid-cols-2 gap-4 mt-8">
                  <div className="group relative bg-gradient-to-br from-asset-500 to-asset-600 text-white py-4 px-4 rounded-xl shadow-soft">
                    <div className="text-center">
                      <span className="text-2xl mb-2 block">🔫</span>
                      <span className="text-sm font-medium">الأسلحة</span>
                    </div>
                  </div>
                  <div className="group relative bg-gradient-to-br from-secondary-500 to-secondary-600 text-white py-4 px-4 rounded-xl shadow-soft">
                    <div className="text-center">
                      <span className="text-2xl mb-2 block">🚗</span>
                      <span className="text-sm font-medium">المركبات</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* System Info */}
              <div className="border-t border-secondary-200 pt-6">
                <div className="flex items-center justify-between text-sm text-secondary-500">
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <span className="w-2 h-2 bg-success-500 rounded-full animate-pulse"></span>
                    <span>النظام متاح</span>
                  </div>
                  <div className="text-left">
                    <p>نظام مرجب</p>
                    <p className="text-xs">الإصدار 2.0</p>
                  </div>
                </div>
              </div>

              {/* External Links */}
              <div className="mt-6 pt-6 border-t border-secondary-200">
                <div className="flex justify-center">
                  <a
                    href="http://127.0.0.1:3001"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center text-sm text-secondary-600 hover:text-primary-600 transition-colors duration-200 group"
                  >
                    <span className="ml-2">🔗</span>
                    <span className="group-hover:underline">نظام إدارة الموارد البشرية</span>
                    <span className="mr-1 transform group-hover:translate-x-1 transition-transform duration-200">←</span>
                  </a>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="text-center mt-8">
            <p className="text-secondary-500 text-sm">
              © 2024 نظام مرجب لإدارة القوة العمومية. جميع الحقوق محفوظة.
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Dashboard Interface
  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Navigation */}
      <nav className="bg-white/80 backdrop-blur-md shadow-lg border-b border-asset-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <div className="flex items-center space-x-3 space-x-reverse">
                <div className="w-10 h-10 bg-gradient-to-r from-asset-600 to-asset-700 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-lg">ق</span>
                </div>
                <h1 className="text-xl font-bold bg-gradient-to-r from-asset-600 to-asset-800 bg-clip-text text-transparent">
                  إدارة القوة العمومية
                </h1>
              </div>
            </div>
            <div className="flex items-center space-x-4 space-x-reverse">
              <a
                href="http://127.0.0.1:3001"
                target="_blank"
                rel="noopener noreferrer"
                className="nav-link"
              >
                نظام الموارد البشرية
              </a>
              <button
                type="button"
                onClick={handleLogout}
                className="btn-danger text-sm"
              >
                تسجيل الخروج
              </button>
            </div>
          </div>
        </div>
      </nav>

      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Header */}
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold bg-gradient-to-r from-asset-600 to-asset-800 bg-clip-text text-transparent mb-2">
              نظام إدارة القوة العمومية
            </h2>
            <p className="text-secondary-600">
              إدارة الأسلحة والمركبات وأجهزة اللاسلكي
            </p>
          </div>

          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="card hover:shadow-medium transition-all duration-300 hover:scale-105">
              <div className="card-body">
                <div className="flex items-center">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-secondary-600">إجمالي الأصول</p>
                    <p className="text-3xl font-bold bg-gradient-to-r from-asset-600 to-asset-800 bg-clip-text text-transparent">{stats.total}</p>
                  </div>
                  <div className="w-12 h-12 bg-gradient-to-r from-asset-500 to-asset-600 rounded-lg flex items-center justify-center shadow-lg">
                    <span className="text-white text-xl">📦</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="card hover:shadow-medium transition-all duration-300 hover:scale-105">
              <div className="card-body">
                <div className="flex items-center">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-secondary-600">الأسلحة</p>
                    <p className="text-3xl font-bold bg-gradient-to-r from-danger-600 to-danger-800 bg-clip-text text-transparent">{stats.weapons}</p>
                  </div>
                  <div className="w-12 h-12 bg-gradient-to-r from-danger-500 to-danger-600 rounded-lg flex items-center justify-center shadow-lg">
                    <span className="text-white text-xl">🔫</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="card hover:shadow-medium transition-all duration-300 hover:scale-105">
              <div className="card-body">
                <div className="flex items-center">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-secondary-600">المركبات</p>
                    <p className="text-3xl font-bold bg-gradient-to-r from-success-600 to-success-800 bg-clip-text text-transparent">{stats.vehicles}</p>
                  </div>
                  <div className="w-12 h-12 bg-gradient-to-r from-success-500 to-success-600 rounded-lg flex items-center justify-center shadow-lg">
                    <span className="text-white text-xl">🚗</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="card hover:shadow-medium transition-all duration-300 hover:scale-105">
              <div className="card-body">
                <div className="flex items-center">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-secondary-600">أجهزة اللاسلكي</p>
                    <p className="text-3xl font-bold bg-gradient-to-r from-warning-600 to-warning-800 bg-clip-text text-transparent">{stats.radios}</p>
                  </div>
                  <div className="w-12 h-12 bg-gradient-to-r from-warning-500 to-warning-600 rounded-lg flex items-center justify-center shadow-lg">
                    <span className="text-white text-xl">📻</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Link
              href="/assets"
              className="card hover:shadow-medium transition-all duration-300 hover:scale-105 group"
            >
              <div className="card-body">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-asset-500 to-asset-600 rounded-lg flex items-center justify-center mr-3 group-hover:scale-110 transition-transform">
                    <span className="text-white text-xl">📋</span>
                  </div>
                  <h3 className="text-lg font-bold bg-gradient-to-r from-asset-600 to-asset-800 bg-clip-text text-transparent">إدارة الأصول</h3>
                </div>
                <p className="text-secondary-600">عرض وإدارة جميع الأصول</p>
              </div>
            </Link>

            <div className="card hover:shadow-medium transition-all duration-300 hover:scale-105 group">
              <div className="card-body">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-success-500 to-success-600 rounded-lg flex items-center justify-center mr-3 group-hover:scale-110 transition-transform">
                    <span className="text-white text-xl">📊</span>
                  </div>
                  <h3 className="text-lg font-bold bg-gradient-to-r from-success-600 to-success-800 bg-clip-text text-transparent">التقارير</h3>
                </div>
                <p className="text-secondary-600">تقارير شاملة عن الأصول</p>
              </div>
            </div>

            <div className="card hover:shadow-medium transition-all duration-300 hover:scale-105 group">
              <div className="card-body">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-warning-500 to-warning-600 rounded-lg flex items-center justify-center mr-3 group-hover:scale-110 transition-transform">
                    <span className="text-white text-xl">🔧</span>
                  </div>
                  <h3 className="text-lg font-bold bg-gradient-to-r from-warning-600 to-warning-800 bg-clip-text text-transparent">الصيانة</h3>
                </div>
                <p className="text-secondary-600">إدارة عمليات الصيانة</p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

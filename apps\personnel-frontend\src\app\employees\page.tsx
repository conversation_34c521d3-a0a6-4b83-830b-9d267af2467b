'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

interface Employee {
  id: string;
  name: string;
  rankKey: string;
  militaryNumber: string;
  nationalId: string;
  unitId?: number;
}

interface Rank {
  key: string;
  label: string;
  category: 'officer' | 'nco' | 'employee';
  order: number;
}

export default function EmployeesPage() {
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [ranks, setRanks] = useState<Rank[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);
  const [filters, setFilters] = useState({
    rank: '',
    unit: '',
    status: ''
  });
  const router = useRouter();

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (!token) {
      router.push('/login');
      return;
    }

    fetchData(token);
  }, [router, searchTerm, filters]);

  const fetchData = async (token: string) => {
    try {
      // Fetch ranks
      const ranksResponse = await fetch('http://127.0.0.1:4001/ranks', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (ranksResponse.ok) {
        const ranksData = await ranksResponse.json();
        setRanks(ranksData);
      }

      // Fetch employees from API with search and filters
      const params = new URLSearchParams();
      if (searchTerm) params.append('search', searchTerm);
      if (filters.rank) params.append('rank', filters.rank);
      if (filters.unit) params.append('unit', filters.unit);
      if (filters.status) params.append('status', filters.status);

      const employeesResponse = await fetch(`http://127.0.0.1:4001/employees?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (employeesResponse.ok) {
        const employeesData = await employeesResponse.json();
        setEmployees(employeesData);
      }

      setLoading(false);
    } catch (err) {
      setError('خطأ في تحميل البيانات');
      setLoading(false);
    }
  };

  const getRankLabel = (rankKey: string) => {
    const rank = ranks.find(r => r.key === rankKey);
    return rank ? rank.label : rankKey;
  };

  const getRankCategory = (rankKey: string) => {
    const rank = ranks.find(r => r.key === rankKey);
    if (!rank) return 'غير محدد';
    
    switch (rank.category) {
      case 'officer': return 'ضابط';
      case 'nco': return 'ضابط صف';
      case 'employee': return 'موظف';
      default: return 'غير محدد';
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm('هل أنت متأكد من حذف هذا الموظف؟ هذا الإجراء لا يمكن التراجع عنه.')) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://127.0.0.1:4001/employees/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        setEmployees(employees.filter(emp => emp.id !== id));
        alert('تم حذف الموظف بنجاح');
      } else {
        alert('خطأ في حذف الموظف');
      }
    } catch (error) {
      alert('خطأ في الاتصال بالخادم');
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('token');
    router.push('/login');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري تحميل البيانات...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50" dir="rtl">
      <header className="bg-white/80 backdrop-blur-md shadow-lg border-b border-blue-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3 space-x-reverse">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">م</span>
              </div>
              <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">إدارة الموظفين</h1>
              <Link
                href="/dashboard"
                className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors"
              >
                ← العودة للوحة التحكم
              </Link>
            </div>
            <button
              type="button"
              onClick={handleLogout}
              className="bg-gradient-to-r from-red-500 to-rose-600 hover:from-red-600 hover:to-rose-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg"
            >
              تسجيل الخروج
            </button>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
            {error}
          </div>
        )}

        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900">قائمة الموظفين</h2>
          <div className="flex space-x-3 space-x-reverse">
            <Link
              href="/employees/new"
              className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-4 py-2 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg"
            >
              إضافة موظف جديد
            </Link>
            <Link
              href="/employees/import"
              className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white px-4 py-2 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg"
            >
              استيراد من Excel
            </Link>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border border-blue-100 p-6 mb-6">
          <div className="space-y-4">
            {/* Basic Search */}
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="flex-1">
                <input
                  type="text"
                  placeholder="البحث بالاسم أو الرقم الوطني أو الرقم العسكري..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <button
                type="button"
                onClick={() => setShowAdvancedSearch(!showAdvancedSearch)}
                className="bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-2 rounded-lg transition-all duration-200"
              >
                {showAdvancedSearch ? 'إخفاء البحث المتقدم' : 'البحث المتقدم'}
              </button>
            </div>

            {/* Advanced Search */}
            {showAdvancedSearch && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t border-gray-200">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    الرتبة
                  </label>
                  <select
                    value={filters.rank}
                    onChange={(e) => setFilters({ ...filters, rank: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    aria-label="الرتبة"
                  >
                    <option value="">جميع الرتب</option>
                    {ranks.map((rank) => (
                      <option key={rank.key} value={rank.key}>
                        {rank.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    الوحدة
                  </label>
                  <select
                    value={filters.unit}
                    onChange={(e) => setFilters({ ...filters, unit: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    aria-label="الوحدة"
                  >
                    <option value="">جميع الوحدات</option>
                    <option value="1">الخمس</option>
                    <option value="2">سوق الخميس</option>
                    <option value="3">كعام</option>
                    <option value="4">الدوريات</option>
                    <option value="5">غنيمة</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    حالة الموظف
                  </label>
                  <select
                    value={filters.status}
                    onChange={(e) => setFilters({ ...filters, status: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    aria-label="حالة الموظف"
                  >
                    <option value="">جميع الحالات</option>
                    <option value="مستمر">مستمر</option>
                    <option value="اجازة">إجازة</option>
                    <option value="موقوف">موقوف</option>
                    <option value="غائب عن العمل">غائب عن العمل</option>
                  </select>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="bg-white shadow-sm rounded-lg overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الاسم
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الرتبة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الفئة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الرقم العسكري
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الرقم الوطني
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {employees.map((employee) => (
                <tr key={employee.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {employee.name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {getRankLabel(employee.rankKey)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      getRankCategory(employee.rankKey) === 'ضابط' ? 'bg-green-100 text-green-800' :
                      getRankCategory(employee.rankKey) === 'ضابط صف' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-purple-100 text-purple-800'
                    }`}>
                      {getRankCategory(employee.rankKey)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {employee.militaryNumber}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-mono">
                    {employee.nationalId}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2 space-x-reverse">
                      <Link
                        href={`/employees/${employee.id}`}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        عرض
                      </Link>
                      <Link
                        href={`/print/${employee.id}`}
                        className="text-green-600 hover:text-green-900"
                      >
                        طباعة
                      </Link>
                      <button
                        type="button"
                        onClick={() => handleDelete(employee.id)}
                        className="text-red-600 hover:text-red-900 transition-colors"
                      >
                        حذف
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {employees.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">لا توجد بيانات موظفين</p>
            <Link
              href="/employees/new"
              className="mt-4 inline-block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              إضافة أول موظف
            </Link>
          </div>
        )}
      </main>
    </div>
  );
}

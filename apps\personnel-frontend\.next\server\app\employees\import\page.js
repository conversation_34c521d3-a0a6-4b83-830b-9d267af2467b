(()=>{var a={};a.id=592,a.ids=[592],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},1115:()=>{},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9699:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(43560);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12445:(a,b,c)=>{Promise.resolve().then(c.bind(c,14447))},14276:()=>{},14447:(a,b,c)=>{"use strict";let d;c.r(b),c.d(b,{default:()=>hd});var e,f,g,h,i=c(78157),j=c(31768),k=c(71159),l={};l.version="0.18.5";var m=1200,n=1252,o=[874,932,936,949,950,1250,1251,1252,1253,1254,1255,1256,1257,1258,1e4],p={0:1252,1:65001,2:65001,77:1e4,128:932,129:949,130:1361,134:936,136:950,161:1253,162:1254,163:1258,177:1255,178:1256,186:1257,204:1251,222:874,238:1250,255:1252,69:6969},q=function(a){-1!=o.indexOf(a)&&(n=p[0]=a)},r=function(a){m=a,q(a)};function s(){r(1200),q(1252)}function t(a){for(var b=[],c=0,d=a.length;c<d;++c)b[c]=a.charCodeAt(c);return b}function u(a){for(var b=[],c=0;c<a.length>>1;++c)b[c]=String.fromCharCode(a.charCodeAt(2*c+1)+(a.charCodeAt(2*c)<<8));return b.join("")}var v=function(a){var b=a.charCodeAt(0),c=a.charCodeAt(1);if(255==b&&254==c){for(var d=a.slice(2),e=[],f=0;f<d.length>>1;++f)e[f]=String.fromCharCode(d.charCodeAt(2*f)+(d.charCodeAt(2*f+1)<<8));return e.join("")}return 254==b&&255==c?u(a.slice(2)):65279==b?a.slice(1):a},w=function(a){return String.fromCharCode(a)},x=function(a){return String.fromCharCode(a)},y="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function z(a){for(var b="",c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0;j<a.length;)f=(c=a.charCodeAt(j++))>>2,g=(3&c)<<4|(d=a.charCodeAt(j++))>>4,h=(15&d)<<2|(e=a.charCodeAt(j++))>>6,i=63&e,isNaN(d)?h=i=64:isNaN(e)&&(i=64),b+=y.charAt(f)+y.charAt(g)+y.charAt(h)+y.charAt(i);return b}function A(a){var b="",c=0,d=0,e=0,f=0,g=0,h=0;a=a.replace(/[^\w\+\/\=]/g,"");for(var i=0;i<a.length;)b+=String.fromCharCode((e=y.indexOf(a.charAt(i++)))<<2|(f=y.indexOf(a.charAt(i++)))>>4),c=(15&f)<<4|(g=y.indexOf(a.charAt(i++)))>>2,64!==g&&(b+=String.fromCharCode(c)),d=(3&g)<<6|(h=y.indexOf(a.charAt(i++))),64!==h&&(b+=String.fromCharCode(d));return b}var B="undefined"!=typeof Buffer&&"undefined"!=typeof process&&void 0!==process.versions&&!!process.versions.node,C=function(){if("undefined"!=typeof Buffer){var a=!Buffer.from;if(!a)try{Buffer.from("foo","utf8")}catch(b){a=!0}return a?function(a,b){return b?new Buffer(a,b):new Buffer(a)}:Buffer.from.bind(Buffer)}return function(){}}();function D(a){return B?Buffer.alloc?Buffer.alloc(a):new Buffer(a):"undefined"!=typeof Uint8Array?new Uint8Array(a):Array(a)}function E(a){return B?Buffer.allocUnsafe?Buffer.allocUnsafe(a):new Buffer(a):"undefined"!=typeof Uint8Array?new Uint8Array(a):Array(a)}var F=function(a){return B?C(a,"binary"):a.split("").map(function(a){return 255&a.charCodeAt(0)})};function G(a){if("undefined"==typeof ArrayBuffer)return F(a);for(var b=new ArrayBuffer(a.length),c=new Uint8Array(b),d=0;d!=a.length;++d)c[d]=255&a.charCodeAt(d);return b}function H(a){if(Array.isArray(a))return a.map(function(a){return String.fromCharCode(a)}).join("");for(var b=[],c=0;c<a.length;++c)b[c]=String.fromCharCode(a[c]);return b.join("")}function I(a){if("undefined"==typeof ArrayBuffer)throw Error("Unsupported");if(a instanceof ArrayBuffer)return I(new Uint8Array(a));for(var b=Array(a.length),c=0;c<a.length;++c)b[c]=a[c];return b}var J=B?function(a){return Buffer.concat(a.map(function(a){return Buffer.isBuffer(a)?a:C(a)}))}:function(a){if("undefined"!=typeof Uint8Array){var b=0,c=0;for(b=0;b<a.length;++b)c+=a[b].length;var d=new Uint8Array(c),e=0;for(b=0,c=0;b<a.length;c+=e,++b)if(e=a[b].length,a[b]instanceof Uint8Array)d.set(a[b],c);else if("string"==typeof a[b])throw"wtf";else d.set(new Uint8Array(a[b]),c);return d}return[].concat.apply([],a.map(function(a){return Array.isArray(a)?a:[].slice.call(a)}))},K=/\u0000/g,L=/[\u0001-\u0006]/g;function M(a){for(var b="",c=a.length-1;c>=0;)b+=a.charAt(c--);return b}function N(a,b){var c=""+a;return c.length>=b?c:aS("0",b-c.length)+c}function O(a,b){var c=""+a;return c.length>=b?c:aS(" ",b-c.length)+c}function P(a,b){var c=""+a;return c.length>=b?c:c+aS(" ",b-c.length)}function Q(a,b){var c,d;return a>0x100000000||a<-0x100000000?(c=""+Math.round(a)).length>=b?c:aS("0",b-c.length)+c:(d=""+Math.round(a)).length>=b?d:aS("0",b-d.length)+d}function R(a,b){return b=b||0,a.length>=7+b&&(32|a.charCodeAt(b))==103&&(32|a.charCodeAt(b+1))==101&&(32|a.charCodeAt(b+2))==110&&(32|a.charCodeAt(b+3))==101&&(32|a.charCodeAt(b+4))==114&&(32|a.charCodeAt(b+5))==97&&(32|a.charCodeAt(b+6))==108}var S=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],T=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]],U={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"上午/下午 "hh"時"mm"分"ss"秒 "'},V={5:37,6:38,7:39,8:40,23:0,24:0,25:0,26:0,27:14,28:14,29:14,30:14,31:14,50:14,51:14,52:14,53:14,54:14,55:14,56:14,57:14,58:14,59:1,60:2,61:3,62:4,67:9,68:10,69:12,70:13,71:14,72:14,73:15,74:16,75:17,76:20,77:21,78:22,79:45,80:46,81:47,82:0},W={5:'"$"#,##0_);\\("$"#,##0\\)',63:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',41:'_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)'};function X(a,b,c){for(var d=a<0?-1:1,e=a*d,f=0,g=1,h=0,i=1,j=0,k=0,l=Math.floor(e);j<b&&(h=(l=Math.floor(e))*g+f,k=l*j+i,!(e-l<5e-8));)e=1/(e-l),f=g,g=h,i=j,j=k;if(k>b&&(j>b?(k=i,h=f):(k=j,h=g)),!c)return[0,d*h,k];var m=Math.floor(d*h/k);return[m,d*h-m*k,k]}function Y(a,b,c){if(a>2958465||a<0)return null;var d=0|a,e=Math.floor(86400*(a-d)),f=0,g=[],h={D:d,T:e,u:86400*(a-d)-e,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(1e-6>Math.abs(h.u)&&(h.u=0),b&&b.date1904&&(d+=1462),h.u>.9999&&(h.u=0,86400==++e&&(h.T=e=0,++d,++h.D)),60===d)g=c?[1317,10,29]:[1900,2,29],f=3;else if(0===d)g=c?[1317,8,29]:[1900,1,0],f=6;else{d>60&&--d;var i,j,k,l=new Date(1900,0,1);l.setDate(l.getDate()+d-1),g=[l.getFullYear(),l.getMonth()+1,l.getDate()],f=l.getDay(),d<60&&(f=(f+6)%7),c&&(i=l,j=g,j[0]-=581,k=i.getDay(),i<60&&(k=(k+6)%7),f=k)}return h.y=g[0],h.m=g[1],h.d=g[2],h.S=e%60,h.M=(e=Math.floor(e/60))%60,h.H=e=Math.floor(e/60),h.q=f,h}var Z=new Date(1899,11,31,0,0,0),_=Z.getTime(),aa=new Date(1900,2,1,0,0,0);function ab(a,b){var c=a.getTime();return b?c-=1262304e5:a>=aa&&(c+=864e5),(c-(_+(a.getTimezoneOffset()-Z.getTimezoneOffset())*6e4))/864e5}function ac(a){return -1==a.indexOf(".")?a:a.replace(/(?:\.0*|(\.\d*[1-9])0+)$/,"$1")}function ad(a){var b,c,d,e,f,g=Math.floor(Math.log(Math.abs(a))*Math.LOG10E);return g>=-4&&g<=-1?f=a.toPrecision(10+g):9>=Math.abs(g)?(b=a<0?12:11,f=(c=ac(a.toFixed(12))).length<=b||(c=a.toPrecision(10)).length<=b?c:a.toExponential(5)):f=10===g?a.toFixed(10).substr(0,12):(d=ac(a.toFixed(11))).length>(a<0?12:11)||"0"===d||"-0"===d?a.toPrecision(6):d,ac(-1==(e=f.toUpperCase()).indexOf("E")?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,"$1E").replace(/(E[+-])(\d)$/,"$10$2"))}function ae(a,b){switch(typeof a){case"string":return a;case"boolean":return a?"TRUE":"FALSE";case"number":return(0|a)===a?a.toString(10):ad(a);case"undefined":return"";case"object":if(null==a)return"";if(a instanceof Date)return at(14,ab(a,b&&b.date1904),b)}throw Error("unsupported value in General format: "+a)}function af(a){if(a.length<=3)return a;for(var b=a.length%3,c=a.substr(0,b);b!=a.length;b+=3)c+=(c.length>0?",":"")+a.substr(b,3);return c}var ag=/%/g,ah=/# (\?+)( ?)\/( ?)(\d+)/,ai=/^#*0*\.([0#]+)/,aj=/\).*[0#]/,ak=/\(###\) ###\\?-####/;function al(a){for(var b,c="",d=0;d!=a.length;++d)switch(b=a.charCodeAt(d)){case 35:break;case 63:c+=" ";break;case 48:c+="0";break;default:c+=String.fromCharCode(b)}return c}function am(a,b){var c=Math.pow(10,b);return""+Math.round(a*c)/c}function an(a,b){var c=a-Math.floor(a),d=Math.pow(10,b);return b<(""+Math.round(c*d)).length?0:Math.round(c*d)}function ao(a,b,c){return(0|c)===c?function a(b,c,d){if(40===b.charCodeAt(0)&&!c.match(aj)){var e,f=c.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return d>=0?a("n",f,d):"("+a("n",f,-d)+")"}if(44===c.charCodeAt(c.length-1)){for(var g=c,h=g.length-1;44===g.charCodeAt(h-1);)--h;return ao(b,g.substr(0,h),d/Math.pow(10,3*(g.length-h)))}if(-1!==c.indexOf("%"))return j=(i=c).replace(ag,""),k=i.length-j.length,ao(b,j,d*Math.pow(10,2*k))+aS("%",k);if(-1!==c.indexOf("E"))return function a(b,c){var d,e=b.indexOf("E")-b.indexOf(".")-1;if(b.match(/^#+0.0E\+0$/)){if(0==c)return"0.0E+0";if(c<0)return"-"+a(b,-c);var f=b.indexOf(".");-1===f&&(f=b.indexOf("E"));var g=Math.floor(Math.log(c)*Math.LOG10E)%f;if(g<0&&(g+=f),!(d=(c/Math.pow(10,g)).toPrecision(e+1+(f+g)%f)).match(/[Ee]/)){var h=Math.floor(Math.log(c)*Math.LOG10E);-1===d.indexOf(".")?d=d.charAt(0)+"."+d.substr(1)+"E+"+(h-d.length+g):d+="E+"+(h-g),d=d.replace(/\+-/,"-")}d=d.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(a,b,c,d){return b+c+d.substr(0,(f+g)%f)+"."+d.substr(g)+"E"})}else d=c.toExponential(e);return b.match(/E\+00$/)&&d.match(/e[+-]\d$/)&&(d=d.substr(0,d.length-1)+"0"+d.charAt(d.length-1)),b.match(/E\-/)&&d.match(/e\+/)&&(d=d.replace(/e\+/,"e")),d.replace("e","E")}(c,d);if(36===c.charCodeAt(0))return"$"+a(b,c.substr(" "==c.charAt(1)?2:1),d);var i,j,k,l,m,n,o,p=Math.abs(d),q=d<0?"-":"";if(c.match(/^00+$/))return q+N(p,c.length);if(c.match(/^[#?]+$/))return l=""+d,0===d&&(l=""),l.length>c.length?l:al(c.substr(0,c.length-l.length))+l;if(m=c.match(ah))return q+(0===p?"":""+p)+aS(" ",(e=m)[1].length+2+e[4].length);if(c.match(/^#+0+$/))return q+N(p,c.length-c.indexOf("0"));if(m=c.match(ai))return l=(l=(""+d).replace(/^([^\.]+)$/,"$1."+al(m[1])).replace(/\.$/,"."+al(m[1]))).replace(/\.(\d*)$/,function(a,b){return"."+b+aS("0",al(m[1]).length-b.length)}),-1!==c.indexOf("0.")?l:l.replace(/^0\./,".");if(m=(c=c.replace(/^#+([0.])/,"$1")).match(/^(0*)\.(#*)$/))return q+(""+p).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,m[1].length?"0.":".");if(m=c.match(/^#{1,3},##0(\.?)$/))return q+af(""+p);if(m=c.match(/^#,##0\.([#0]*0)$/))return d<0?"-"+a(b,c,-d):af(""+d)+"."+aS("0",m[1].length);if(m=c.match(/^#,#*,#0/))return a(b,c.replace(/^#,#*,/,""),d);if(m=c.match(/^([0#]+)(\\?-([0#]+))+$/))return l=M(a(b,c.replace(/[\\-]/g,""),d)),n=0,M(M(c.replace(/\\/g,"")).replace(/[0#]/g,function(a){return n<l.length?l.charAt(n++):"0"===a?"0":""}));if(c.match(ak))return"("+(l=a(b,"##########",d)).substr(0,3)+") "+l.substr(3,3)+"-"+l.substr(6);var r="";if(m=c.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return o=X(p,Math.pow(10,n=Math.min(m[4].length,7))-1,!1),l=""+q," "==(r=ao("n",m[1],o[1])).charAt(r.length-1)&&(r=r.substr(0,r.length-1)+"0"),l+=r+m[2]+"/"+m[3],(r=P(o[2],n)).length<m[4].length&&(r=al(m[4].substr(m[4].length-r.length))+r),l+=r;if(m=c.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return q+((o=X(p,Math.pow(10,n=Math.min(Math.max(m[1].length,m[4].length),7))-1,!0))[0]||(o[1]?"":"0"))+" "+(o[1]?O(o[1],n)+m[2]+"/"+m[3]+P(o[2],n):aS(" ",2*n+1+m[2].length+m[3].length));if(m=c.match(/^[#0?]+$/))return(l=""+d,c.length<=l.length)?l:al(c.substr(0,c.length-l.length))+l;if(m=c.match(/^([#0]+)\.([#0]+)$/)){n=(l=""+d.toFixed(Math.min(m[2].length,10)).replace(/([^0])0+$/,"$1")).indexOf(".");var s=c.indexOf(".")-n,t=c.length-l.length-s;return al(c.substr(0,s)+l+c.substr(c.length-t))}if(m=c.match(/^00,000\.([#0]*0)$/))return d<0?"-"+a(b,c,-d):af(""+d).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(a){return"00,"+(a.length<3?N(0,3-a.length):"")+a})+"."+N(0,m[1].length);switch(c){case"###,###":case"##,###":case"#,###":var u=af(""+p);return"0"!==u?q+u:"";default:if(c.match(/\.[0#?]*$/))return a(b,c.slice(0,c.lastIndexOf(".")),d)+al(c.slice(c.lastIndexOf(".")))}throw Error("unsupported format |"+c+"|")}(a,b,c):function a(b,c,d){if(40===b.charCodeAt(0)&&!c.match(aj)){var e,f,g,h,i,j,k=c.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return d>=0?a("n",k,d):"("+a("n",k,-d)+")"}if(44===c.charCodeAt(c.length-1)){for(var l=c,m=l.length-1;44===l.charCodeAt(m-1);)--m;return ao(b,l.substr(0,m),d/Math.pow(10,3*(l.length-m)))}if(-1!==c.indexOf("%"))return o=(n=c).replace(ag,""),p=n.length-o.length,ao(b,o,d*Math.pow(10,2*p))+aS("%",p);if(-1!==c.indexOf("E"))return function a(b,c){var d,e=b.indexOf("E")-b.indexOf(".")-1;if(b.match(/^#+0.0E\+0$/)){if(0==c)return"0.0E+0";if(c<0)return"-"+a(b,-c);var f=b.indexOf(".");-1===f&&(f=b.indexOf("E"));var g=Math.floor(Math.log(c)*Math.LOG10E)%f;if(g<0&&(g+=f),-1===(d=(c/Math.pow(10,g)).toPrecision(e+1+(f+g)%f)).indexOf("e")){var h=Math.floor(Math.log(c)*Math.LOG10E);for(-1===d.indexOf(".")?d=d.charAt(0)+"."+d.substr(1)+"E+"+(h-d.length+g):d+="E+"+(h-g);"0."===d.substr(0,2);)d=(d=d.charAt(0)+d.substr(2,f)+"."+d.substr(2+f)).replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");d=d.replace(/\+-/,"-")}d=d.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(a,b,c,d){return b+c+d.substr(0,(f+g)%f)+"."+d.substr(g)+"E"})}else d=c.toExponential(e);return b.match(/E\+00$/)&&d.match(/e[+-]\d$/)&&(d=d.substr(0,d.length-1)+"0"+d.charAt(d.length-1)),b.match(/E\-/)&&d.match(/e\+/)&&(d=d.replace(/e\+/,"e")),d.replace("e","E")}(c,d);if(36===c.charCodeAt(0))return"$"+a(b,c.substr(" "==c.charAt(1)?2:1),d);var n,o,p,q,r,s,t,u=Math.abs(d),v=d<0?"-":"";if(c.match(/^00+$/))return v+Q(u,c.length);if(c.match(/^[#?]+$/))return"0"===(q=Q(d,0))&&(q=""),q.length>c.length?q:al(c.substr(0,c.length-q.length))+q;if(r=c.match(ah))return h=Math.floor((g=Math.round(u*(f=parseInt((e=r)[4],10))))/f),i=g-h*f,v+(0===h?"":""+h)+" "+(0===i?aS(" ",e[1].length+1+e[4].length):O(i,e[1].length)+e[2]+"/"+e[3]+N(f,e[4].length));if(c.match(/^#+0+$/))return v+Q(u,c.length-c.indexOf("0"));if(r=c.match(ai))return q=am(d,r[1].length).replace(/^([^\.]+)$/,"$1."+al(r[1])).replace(/\.$/,"."+al(r[1])).replace(/\.(\d*)$/,function(a,b){return"."+b+aS("0",al(r[1]).length-b.length)}),-1!==c.indexOf("0.")?q:q.replace(/^0\./,".");if(r=(c=c.replace(/^#+([0.])/,"$1")).match(/^(0*)\.(#*)$/))return v+am(u,r[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,r[1].length?"0.":".");if(r=c.match(/^#{1,3},##0(\.?)$/))return v+af(Q(u,0));if(r=c.match(/^#,##0\.([#0]*0)$/))return d<0?"-"+a(b,c,-d):af(""+(Math.floor(d)+ +((j=r[1].length)<(""+Math.round((d-Math.floor(d))*Math.pow(10,j))).length)))+"."+N(an(d,r[1].length),r[1].length);if(r=c.match(/^#,#*,#0/))return a(b,c.replace(/^#,#*,/,""),d);if(r=c.match(/^([0#]+)(\\?-([0#]+))+$/))return q=M(a(b,c.replace(/[\\-]/g,""),d)),s=0,M(M(c.replace(/\\/g,"")).replace(/[0#]/g,function(a){return s<q.length?q.charAt(s++):"0"===a?"0":""}));if(c.match(ak))return"("+(q=a(b,"##########",d)).substr(0,3)+") "+q.substr(3,3)+"-"+q.substr(6);var w="";if(r=c.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return t=X(u,Math.pow(10,s=Math.min(r[4].length,7))-1,!1),q=""+v," "==(w=ao("n",r[1],t[1])).charAt(w.length-1)&&(w=w.substr(0,w.length-1)+"0"),q+=w+r[2]+"/"+r[3],(w=P(t[2],s)).length<r[4].length&&(w=al(r[4].substr(r[4].length-w.length))+w),q+=w;if(r=c.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return v+((t=X(u,Math.pow(10,s=Math.min(Math.max(r[1].length,r[4].length),7))-1,!0))[0]||(t[1]?"":"0"))+" "+(t[1]?O(t[1],s)+r[2]+"/"+r[3]+P(t[2],s):aS(" ",2*s+1+r[2].length+r[3].length));if(r=c.match(/^[#0?]+$/))return(q=Q(d,0),c.length<=q.length)?q:al(c.substr(0,c.length-q.length))+q;if(r=c.match(/^([#0?]+)\.([#0]+)$/)){s=(q=""+d.toFixed(Math.min(r[2].length,10)).replace(/([^0])0+$/,"$1")).indexOf(".");var x=c.indexOf(".")-s,y=c.length-q.length-x;return al(c.substr(0,x)+q+c.substr(c.length-y))}if(r=c.match(/^00,000\.([#0]*0)$/))return s=an(d,r[1].length),d<0?"-"+a(b,c,-d):af(d<0x7fffffff&&d>-0x80000000?""+(d>=0?0|d:d-1|0):""+Math.floor(d)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(a){return"00,"+(a.length<3?N(0,3-a.length):"")+a})+"."+N(s,r[1].length);switch(c){case"###,##0.00":return a(b,"#,##0.00",d);case"###,###":case"##,###":case"#,###":var z=af(Q(u,0));return"0"!==z?v+z:"";case"###,###.00":return a(b,"###,##0.00",d).replace(/^0\./,".");case"#,###.00":return a(b,"#,##0.00",d).replace(/^0\./,".")}throw Error("unsupported format |"+c+"|")}(a,b,c)}var ap=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function aq(a){for(var b=0,c="",d="";b<a.length;)switch(c=a.charAt(b)){case"G":R(a,b)&&(b+=6),b++;break;case'"':for(;34!==a.charCodeAt(++b)&&b<a.length;);++b;break;case"\\":case"_":b+=2;break;case"@":++b;break;case"B":case"b":if("1"===a.charAt(b+1)||"2"===a.charAt(b+1))return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":case"上":if("A/P"===a.substr(b,3).toUpperCase()||"AM/PM"===a.substr(b,5).toUpperCase()||"上午/下午"===a.substr(b,5).toUpperCase())return!0;++b;break;case"[":for(d=c;"]"!==a.charAt(b++)&&b<a.length;)d+=a.charAt(b);if(d.match(ap))return!0;break;case".":case"0":case"#":for(;b<a.length&&("0#?.,E+-%".indexOf(c=a.charAt(++b))>-1||"\\"==c&&"-"==a.charAt(b+1)&&"0#".indexOf(a.charAt(b+2))>-1););break;case"?":for(;a.charAt(++b)===c;);break;case"*":++b,(" "==a.charAt(b)||"*"==a.charAt(b))&&++b;break;case"(":case")":case" ":default:++b;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(;b<a.length&&"0123456789".indexOf(a.charAt(++b))>-1;);}return!1}var ar=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function as(a,b){if(null==b)return!1;var c=parseFloat(b[2]);switch(b[1]){case"=":if(a==c)return!0;break;case">":if(a>c)return!0;break;case"<":if(a<c)return!0;break;case"<>":if(a!=c)return!0;break;case">=":if(a>=c)return!0;break;case"<=":if(a<=c)return!0}return!1}function at(a,b,c){null==c&&(c={});var d="";switch(typeof a){case"string":d="m/d/yy"==a&&c.dateNF?c.dateNF:a;break;case"number":null==(d=14==a&&c.dateNF?c.dateNF:(null!=c.table?c.table:U)[a])&&(d=c.table&&c.table[V[a]]||U[V[a]]),null==d&&(d=W[a]||"General")}if(R(d,0))return ae(b,c);b instanceof Date&&(b=ab(b,c.date1904));var e=function(a,b){var c=function(a){for(var b=[],c=!1,d=0,e=0;d<a.length;++d)switch(a.charCodeAt(d)){case 34:c=!c;break;case 95:case 42:case 92:++d;break;case 59:b[b.length]=a.substr(e,d-e),e=d+1}if(b[b.length]=a.substr(e),!0===c)throw Error("Format |"+a+"| unterminated string ");return b}(a),d=c.length,e=c[d-1].indexOf("@");if(d<4&&e>-1&&--d,c.length>4)throw Error("cannot find right format for |"+c.join("|")+"|");if("number"!=typeof b)return[4,4===c.length||e>-1?c[c.length-1]:"@"];switch(c.length){case 1:c=e>-1?["General","General","General",c[0]]:[c[0],c[0],c[0],"@"];break;case 2:c=e>-1?[c[0],c[0],c[0],c[1]]:[c[0],c[1],c[0],"@"];break;case 3:c=e>-1?[c[0],c[1],c[0],c[2]]:[c[0],c[1],c[2],"@"]}var f=b>0?c[0]:b<0?c[1]:c[2];if(-1===c[0].indexOf("[")&&-1===c[1].indexOf("["))return[d,f];if(null!=c[0].match(/\[[=<>]/)||null!=c[1].match(/\[[=<>]/)){var g=c[0].match(ar),h=c[1].match(ar);return as(b,g)?[d,c[0]]:as(b,h)?[d,c[1]]:[d,c[null!=g&&null!=h?2:1]]}return[d,f]}(d,b);if(R(e[1]))return ae(b,c);if(!0===b)b="TRUE";else if(!1===b)b="FALSE";else if(""===b||null==b)return"";return function(a,b,c,d){for(var e,f,g,h=[],i="",j=0,k="",l="t",m="H";j<a.length;)switch(k=a.charAt(j)){case"G":if(!R(a,j))throw Error("unrecognized character "+k+" in "+a);h[h.length]={t:"G",v:"General"},j+=7;break;case'"':for(i="";34!==(g=a.charCodeAt(++j))&&j<a.length;)i+=String.fromCharCode(g);h[h.length]={t:"t",v:i},++j;break;case"\\":var n=a.charAt(++j),o="("===n||")"===n?n:"t";h[h.length]={t:o,v:n},++j;break;case"_":h[h.length]={t:"t",v:" "},j+=2;break;case"@":h[h.length]={t:"T",v:b},++j;break;case"B":case"b":if("1"===a.charAt(j+1)||"2"===a.charAt(j+1)){if(null==e&&null==(e=Y(b,c,"2"===a.charAt(j+1))))return"";h[h.length]={t:"X",v:a.substr(j,2)},l=k,j+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":k=k.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(b<0||null==e&&null==(e=Y(b,c)))return"";for(i=k;++j<a.length&&a.charAt(j).toLowerCase()===k;)i+=k;"m"===k&&"h"===l.toLowerCase()&&(k="M"),"h"===k&&(k=m),h[h.length]={t:k,v:i},l=k;break;case"A":case"a":case"上":var p={t:k,v:k};if(null==e&&(e=Y(b,c)),"A/P"===a.substr(j,3).toUpperCase()?(null!=e&&(p.v=e.H>=12?"P":"A"),p.t="T",m="h",j+=3):"AM/PM"===a.substr(j,5).toUpperCase()?(null!=e&&(p.v=e.H>=12?"PM":"AM"),p.t="T",j+=5,m="h"):"上午/下午"===a.substr(j,5).toUpperCase()?(null!=e&&(p.v=e.H>=12?"下午":"上午"),p.t="T",j+=5,m="h"):(p.t="t",++j),null==e&&"T"===p.t)return"";h[h.length]=p,l=k;break;case"[":for(i=k;"]"!==a.charAt(j++)&&j<a.length;)i+=a.charAt(j);if("]"!==i.slice(-1))throw'unterminated "[" block: |'+i+"|";if(i.match(ap)){if(null==e&&null==(e=Y(b,c)))return"";h[h.length]={t:"Z",v:i.toLowerCase()},l=i.charAt(1)}else i.indexOf("$")>-1&&(i=(i.match(/\$([^-\[\]]*)/)||[])[1]||"$",aq(a)||(h[h.length]={t:"t",v:i}));break;case".":if(null!=e){for(i=k;++j<a.length&&"0"===(k=a.charAt(j));)i+=k;h[h.length]={t:"s",v:i};break}case"0":case"#":for(i=k;++j<a.length&&"0#?.,E+-%".indexOf(k=a.charAt(j))>-1;)i+=k;h[h.length]={t:"n",v:i};break;case"?":for(i=k;a.charAt(++j)===k;)i+=k;h[h.length]={t:k,v:i},l=k;break;case"*":++j,(" "==a.charAt(j)||"*"==a.charAt(j))&&++j;break;case"(":case")":h[h.length]={t:1===d?"t":k,v:k},++j;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(i=k;j<a.length&&"0123456789".indexOf(a.charAt(++j))>-1;)i+=a.charAt(j);h[h.length]={t:"D",v:i};break;case" ":h[h.length]={t:k,v:k},++j;break;case"$":h[h.length]={t:"t",v:"$"},++j;break;default:if(-1===",$-+/():!^&'~{}<>=€acfijklopqrtuvwxzP".indexOf(k))throw Error("unrecognized character "+k+" in "+a);h[h.length]={t:"t",v:k},++j}var q,r=0,s=0;for(j=h.length-1,l="t";j>=0;--j)switch(h[j].t){case"h":case"H":h[j].t=m,l="h",r<1&&(r=1);break;case"s":(q=h[j].v.match(/\.0+$/))&&(s=Math.max(s,q[0].length-1)),r<3&&(r=3);case"d":case"y":case"M":case"e":l=h[j].t;break;case"m":"s"===l&&(h[j].t="M",r<2&&(r=2));break;case"X":break;case"Z":r<1&&h[j].v.match(/[Hh]/)&&(r=1),r<2&&h[j].v.match(/[Mm]/)&&(r=2),r<3&&h[j].v.match(/[Ss]/)&&(r=3)}switch(r){case 0:break;case 1:e.u>=.5&&(e.u=0,++e.S),e.S>=60&&(e.S=0,++e.M),e.M>=60&&(e.M=0,++e.H);break;case 2:e.u>=.5&&(e.u=0,++e.S),e.S>=60&&(e.S=0,++e.M)}var t,u="";for(j=0;j<h.length;++j)switch(h[j].t){case"t":case"T":case" ":case"D":break;case"X":h[j].v="",h[j].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":h[j].v=function(a,b,c,d){var e,f="",g=0,h=0,i=c.y,j=0;switch(a){case 98:i=c.y+543;case 121:switch(b.length){case 1:case 2:e=i%100,j=2;break;default:e=i%1e4,j=4}break;case 109:switch(b.length){case 1:case 2:e=c.m,j=b.length;break;case 3:return T[c.m-1][1];case 5:return T[c.m-1][0];default:return T[c.m-1][2]}break;case 100:switch(b.length){case 1:case 2:e=c.d,j=b.length;break;case 3:return S[c.q][0];default:return S[c.q][1]}break;case 104:switch(b.length){case 1:case 2:e=1+(c.H+11)%12,j=b.length;break;default:throw"bad hour format: "+b}break;case 72:switch(b.length){case 1:case 2:e=c.H,j=b.length;break;default:throw"bad hour format: "+b}break;case 77:switch(b.length){case 1:case 2:e=c.M,j=b.length;break;default:throw"bad minute format: "+b}break;case 115:if("s"!=b&&"ss"!=b&&".0"!=b&&".00"!=b&&".000"!=b)throw"bad second format: "+b;if(0===c.u&&("s"==b||"ss"==b))return N(c.S,b.length);if((g=Math.round((h=d>=2?3===d?1e3:100:1===d?10:1)*(c.S+c.u)))>=60*h&&(g=0),"s"===b)return 0===g?"0":""+g/h;if(f=N(g,2+d),"ss"===b)return f.substr(0,2);return"."+f.substr(2,b.length-1);case 90:switch(b){case"[h]":case"[hh]":e=24*c.D+c.H;break;case"[m]":case"[mm]":e=(24*c.D+c.H)*60+c.M;break;case"[s]":case"[ss]":e=((24*c.D+c.H)*60+c.M)*60+Math.round(c.S+c.u);break;default:throw"bad abstime format: "+b}j=3===b.length?1:2;break;case 101:e=i,j=1}return j>0?N(e,j):""}(h[j].t.charCodeAt(0),h[j].v,e,s),h[j].t="t";break;case"n":case"?":for(t=j+1;null!=h[t]&&("?"===(k=h[t].t)||"D"===k||(" "===k||"t"===k)&&null!=h[t+1]&&("?"===h[t+1].t||"t"===h[t+1].t&&"/"===h[t+1].v)||"("===h[j].t&&(" "===k||"n"===k||")"===k)||"t"===k&&("/"===h[t].v||" "===h[t].v&&null!=h[t+1]&&"?"==h[t+1].t));)h[j].v+=h[t].v,h[t]={v:"",t:";"},++t;u+=h[j].v,j=t-1;break;case"G":h[j].t="t",h[j].v=ae(b,c)}var v,w,x="";if(u.length>0){40==u.charCodeAt(0)?(v=b<0&&45===u.charCodeAt(0)?-b:b,w=ao("n",u,v)):(w=ao("n",u,v=b<0&&d>1?-b:b),v<0&&h[0]&&"t"==h[0].t&&(w=w.substr(1),h[0].v="-"+h[0].v)),t=w.length-1;var y=h.length;for(j=0;j<h.length;++j)if(null!=h[j]&&"t"!=h[j].t&&h[j].v.indexOf(".")>-1){y=j;break}var z=h.length;if(y===h.length&&-1===w.indexOf("E")){for(j=h.length-1;j>=0;--j)null!=h[j]&&-1!=="n?".indexOf(h[j].t)&&(t>=h[j].v.length-1?(t-=h[j].v.length,h[j].v=w.substr(t+1,h[j].v.length)):t<0?h[j].v="":(h[j].v=w.substr(0,t+1),t=-1),h[j].t="t",z=j);t>=0&&z<h.length&&(h[z].v=w.substr(0,t+1)+h[z].v)}else if(y!==h.length&&-1===w.indexOf("E")){for(t=w.indexOf(".")-1,j=y;j>=0;--j)if(null!=h[j]&&-1!=="n?".indexOf(h[j].t)){for(f=h[j].v.indexOf(".")>-1&&j===y?h[j].v.indexOf(".")-1:h[j].v.length-1,x=h[j].v.substr(f+1);f>=0;--f)t>=0&&("0"===h[j].v.charAt(f)||"#"===h[j].v.charAt(f))&&(x=w.charAt(t--)+x);h[j].v=x,h[j].t="t",z=j}for(t>=0&&z<h.length&&(h[z].v=w.substr(0,t+1)+h[z].v),t=w.indexOf(".")+1,j=y;j<h.length;++j)if(null!=h[j]&&(-1!=="n?(".indexOf(h[j].t)||j===y)){for(f=h[j].v.indexOf(".")>-1&&j===y?h[j].v.indexOf(".")+1:0,x=h[j].v.substr(0,f);f<h[j].v.length;++f)t<w.length&&(x+=w.charAt(t++));h[j].v=x,h[j].t="t",z=j}}}for(j=0;j<h.length;++j)null!=h[j]&&"n?".indexOf(h[j].t)>-1&&(v=d>1&&b<0&&j>0&&"-"===h[j-1].v?-b:b,h[j].v=ao(h[j].t,h[j].v,v),h[j].t="t");var A="";for(j=0;j!==h.length;++j)null!=h[j]&&(A+=h[j].v);return A}(e[1],b,c,e[0])}function au(a,b){if("number"!=typeof b){b=+b||-1;for(var c=0;c<392;++c){if(void 0==U[c]){b<0&&(b=c);continue}if(U[c]==a){b=c;break}}b<0&&(b=391)}return U[b]=a,b}function av(a){for(var b=0;392!=b;++b)void 0!==a[b]&&au(a[b],b)}function aw(){var a;a||(a={}),a[0]="General",a[1]="0",a[2]="0.00",a[3]="#,##0",a[4]="#,##0.00",a[9]="0%",a[10]="0.00%",a[11]="0.00E+00",a[12]="# ?/?",a[13]="# ??/??",a[14]="m/d/yy",a[15]="d-mmm-yy",a[16]="d-mmm",a[17]="mmm-yy",a[18]="h:mm AM/PM",a[19]="h:mm:ss AM/PM",a[20]="h:mm",a[21]="h:mm:ss",a[22]="m/d/yy h:mm",a[37]="#,##0 ;(#,##0)",a[38]="#,##0 ;[Red](#,##0)",a[39]="#,##0.00;(#,##0.00)",a[40]="#,##0.00;[Red](#,##0.00)",a[45]="mm:ss",a[46]="[h]:mm:ss",a[47]="mmss.0",a[48]="##0.0E+0",a[49]="@",a[56]='"上午/下午 "hh"時"mm"分"ss"秒 "',U=a}var ax={5:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',23:"General",24:"General",25:"General",26:"General",27:"m/d/yy",28:"m/d/yy",29:"m/d/yy",30:"m/d/yy",31:"m/d/yy",32:"h:mm:ss",33:"h:mm:ss",34:"h:mm:ss",35:"h:mm:ss",36:"m/d/yy",41:'_(* #,##0_);_(* (#,##0);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* (#,##0);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* (#,##0.00);_("$"* "-"??_);_(@_)',50:"m/d/yy",51:"m/d/yy",52:"m/d/yy",53:"m/d/yy",54:"m/d/yy",55:"m/d/yy",56:"m/d/yy",57:"m/d/yy",58:"m/d/yy",59:"0",60:"0.00",61:"#,##0",62:"#,##0.00",63:'"$"#,##0_);\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',67:"0%",68:"0.00%",69:"# ?/?",70:"# ??/??",71:"m/d/yy",72:"m/d/yy",73:"d-mmm-yy",74:"d-mmm",75:"mmm-yy",76:"h:mm",77:"h:mm:ss",78:"m/d/yy h:mm",79:"mm:ss",80:"[h]:mm:ss",81:"mmss.0"},ay=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g,az=function(){var a={};a.version="1.2.0";var b=function(){for(var a=0,b=Array(256),c=0;256!=c;++c)a=1&(a=1&(a=1&(a=1&(a=1&(a=1&(a=1&(a=1&(a=c)?-0x12477ce0^a>>>1:a>>>1)?-0x12477ce0^a>>>1:a>>>1)?-0x12477ce0^a>>>1:a>>>1)?-0x12477ce0^a>>>1:a>>>1)?-0x12477ce0^a>>>1:a>>>1)?-0x12477ce0^a>>>1:a>>>1)?-0x12477ce0^a>>>1:a>>>1)?-0x12477ce0^a>>>1:a>>>1,b[c]=a;return"undefined"!=typeof Int32Array?new Int32Array(b):b}(),c=function(a){var b=0,c=0,d=0,e="undefined"!=typeof Int32Array?new Int32Array(4096):Array(4096);for(d=0;256!=d;++d)e[d]=a[d];for(d=0;256!=d;++d)for(c=a[d],b=256+d;b<4096;b+=256)c=e[b]=c>>>8^a[255&c];var f=[];for(d=1;16!=d;++d)f[d-1]="undefined"!=typeof Int32Array?e.subarray(256*d,256*d+256):e.slice(256*d,256*d+256);return f}(b),d=c[0],e=c[1],f=c[2],g=c[3],h=c[4],i=c[5],j=c[6],k=c[7],l=c[8],m=c[9],n=c[10],o=c[11],p=c[12],q=c[13],r=c[14];return a.table=b,a.bstr=function(a,c){for(var d=-1^c,e=0,f=a.length;e<f;)d=d>>>8^b[(d^a.charCodeAt(e++))&255];return~d},a.buf=function(a,c){for(var s=-1^c,t=a.length-15,u=0;u<t;)s=r[a[u++]^255&s]^q[a[u++]^s>>8&255]^p[a[u++]^s>>16&255]^o[a[u++]^s>>>24]^n[a[u++]]^m[a[u++]]^l[a[u++]]^k[a[u++]]^j[a[u++]]^i[a[u++]]^h[a[u++]]^g[a[u++]]^f[a[u++]]^e[a[u++]]^d[a[u++]]^b[a[u++]];for(t+=15;u<t;)s=s>>>8^b[(s^a[u++])&255];return~s},a.str=function(a,c){for(var d=-1^c,e=0,f=a.length,g=0,h=0;e<f;)(g=a.charCodeAt(e++))<128?d=d>>>8^b[(d^g)&255]:g<2048?d=(d=d>>>8^b[(d^(192|g>>6&31))&255])>>>8^b[(d^(128|63&g))&255]:g>=55296&&g<57344?(g=(1023&g)+64,h=1023&a.charCodeAt(e++),d=(d=(d=(d=d>>>8^b[(d^(240|g>>8&7))&255])>>>8^b[(d^(128|g>>2&63))&255])>>>8^b[(d^(128|h>>6&15|(3&g)<<4))&255])>>>8^b[(d^(128|63&h))&255]):d=(d=(d=d>>>8^b[(d^(224|g>>12&15))&255])>>>8^b[(d^(128|g>>6&63))&255])>>>8^b[(d^(128|63&g))&255];return~d},a}(),aA=function(){var a,b,c={};function d(a){if("/"==a.charAt(a.length-1))return -1===a.slice(0,-1).indexOf("/")?a:d(a.slice(0,-1));var b=a.lastIndexOf("/");return -1===b?a:a.slice(0,b+1)}function e(a){if("/"==a.charAt(a.length-1))return e(a.slice(0,-1));var b=a.lastIndexOf("/");return -1===b?a:a.slice(b+1)}function f(a){cf(a,0);for(var b={},c=0;a.l<=a.length-4;){var d=a.read_shift(2),e=a.read_shift(2),f=a.l+e,g={};21589===d&&(1&(c=a.read_shift(1))&&(g.mtime=a.read_shift(4)),e>5&&(2&c&&(g.atime=a.read_shift(4)),4&c&&(g.ctime=a.read_shift(4))),g.mtime&&(g.mt=new Date(1e3*g.mtime))),a.l=f,b[d]=g}return b}function g(){return a||(a={})}function h(a,b){if(80==a[0]&&75==a[1])return ah(a,b);if((32|a[0])==109&&(32|a[1])==105)return function(a,b){if("mime-version:"!=s(a.slice(0,13)).toLowerCase())throw Error("Unsupported MAD header");var c=b&&b.root||"",d=(B&&Buffer.isBuffer(a)?a.toString("binary"):s(a)).split("\r\n"),e=0,f="";for(e=0;e<d.length;++e)if((f=d[e],/^Content-Location:/i.test(f))&&(f=f.slice(f.indexOf("file")),c||(c=f.slice(0,f.lastIndexOf("/")+1)),f.slice(0,c.length)!=c))for(;c.length>0&&(c=(c=c.slice(0,c.length-1)).slice(0,c.lastIndexOf("/")+1),f.slice(0,c.length)!=c););var g=(d[1]||"").match(/boundary="(.*?)"/);if(!g)throw Error("MAD cannot find boundary");var h="--"+(g[1]||""),i={FileIndex:[],FullPaths:[]};j(i);var k,l=0;for(e=0;e<d.length;++e){var m=d[e];(m===h||m===h+"--")&&(l++&&function(a,b,c){for(var d,e="",f="",g="",h=0;h<10;++h){var i=b[h];if(!i||i.match(/^\s*$/))break;var j=i.match(/^(.*?):\s*([^\s].*)$/);if(j)switch(j[1].toLowerCase()){case"content-location":e=j[2].trim();break;case"content-type":g=j[2].trim();break;case"content-transfer-encoding":f=j[2].trim()}}switch(++h,f.toLowerCase()){case"base64":d=F(A(b.slice(h).join("")));break;case"quoted-printable":d=function(a){for(var b=[],c=0;c<a.length;++c){for(var d=a[c];c<=a.length&&"="==d.charAt(d.length-1);)d=d.slice(0,d.length-1)+a[++c];b.push(d)}for(var e=0;e<b.length;++e)b[e]=b[e].replace(/[=][0-9A-Fa-f]{2}/g,function(a){return String.fromCharCode(parseInt(a.slice(1),16))});return F(b.join("\r\n"))}(b.slice(h));break;default:throw Error("Unsupported Content-Transfer-Encoding "+f)}var k=aj(a,e.slice(c.length),d,{unsafe:!0});g&&(k.ctype=g)}(i,d.slice(k,e),c),k=e)}return i}(a,b);if(a.length<512)throw Error("CFB file size "+a.length+" < 512");var c=3,d=512,e=0,f=0,g=0,h=0,k=0,l=[],p=a.slice(0,512);cf(p,0);var q=function(a){if(80==a[a.l]&&75==a[a.l+1])return[0,0];a.chk(o,"Header Signature: "),a.l+=16;var b=a.read_shift(2,"u");return[a.read_shift(2,"u"),b]}(p);switch(c=q[0]){case 3:d=512;break;case 4:d=4096;break;case 0:if(0==q[1])return ah(a,b);default:throw Error("Major Version: Expected 3 or 4 saw "+c)}512!==d&&cf(p=a.slice(0,d),28);var r=a.slice(0,d),t=p,u=c,v=9;switch(t.l+=2,v=t.read_shift(2)){case 9:if(3!=u)throw Error("Sector Shift: Expected 9 saw "+v);break;case 12:if(4!=u)throw Error("Sector Shift: Expected 12 saw "+v);break;default:throw Error("Sector Shift: Expected 9 or 12 saw "+v)}t.chk("0600","Mini Sector Shift: "),t.chk("000000000000","Reserved: ");var w=p.read_shift(4,"i");if(3===c&&0!==w)throw Error("# Directory Sectors: Expected 0 saw "+w);p.l+=4,g=p.read_shift(4,"i"),p.l+=4,p.chk("00100000","Mini Stream Cutoff Size: "),h=p.read_shift(4,"i"),e=p.read_shift(4,"i"),k=p.read_shift(4,"i"),f=p.read_shift(4,"i");for(var x=-1,y=0;y<109&&!((x=p.read_shift(4,"i"))<0);++y)l[y]=x;var z=function(a,b){for(var c=Math.ceil(a.length/b)-1,d=[],e=1;e<c;++e)d[e-1]=a.slice(e*b,(e+1)*b);return d[c-1]=a.slice(c*b),d}(a,d);!function a(b,c,d,e,f){var g=n;if(b===n){if(0!==c)throw Error("DIFAT chain shorter than expected")}else if(-1!==b){var h=d[b],i=(e>>>2)-1;if(!h)return;for(var j=0;j<i&&(g=b8(h,4*j))!==n;++j)f.push(g);a(b8(h,e-4),c-1,d,e,f)}}(k,f,z,d,l);var C=function(a,b,c,d){var e=a.length,f=[],g=[],h=[],i=[],j=d-1,k=0,l=0,m=0,n=0;for(k=0;k<e;++k)if(h=[],(m=k+b)>=e&&(m-=e),!g[m]){i=[];var o=[];for(l=m;l>=0;){o[l]=!0,g[l]=!0,h[h.length]=l,i.push(a[l]);var p=c[Math.floor(4*l/d)];if(d<4+(n=4*l&j))throw Error("FAT boundary crossed: "+l+" 4 "+d);if(!a[p]||o[l=b8(a[p],n)])break}f[m]={nodes:h,data:bM([i])}}return f}(z,g,l,d);C[g].name="!Directory",e>0&&h!==n&&(C[h].name="!MiniFAT"),C[l[0]].name="!FAT",C.fat_addrs=l,C.ssz=d;var D=[],E=[],G=[];(function(a,b,c,d,e,f,g,h){for(var j,k=0,l=2*!!d.length,o=b[a].data,p=0,q=0;p<o.length;p+=128){var r=o.slice(p,p+128);cf(r,64),q=r.read_shift(2),j=bO(r,0,q-l),d.push(j);var s={name:j,type:r.read_shift(1),color:r.read_shift(1),L:r.read_shift(4,"i"),R:r.read_shift(4,"i"),C:r.read_shift(4,"i"),clsid:r.read_shift(16),state:r.read_shift(4,"i"),start:0,size:0};0!==r.read_shift(2)+r.read_shift(2)+r.read_shift(2)+r.read_shift(2)&&(s.ct=i(r,r.l-8)),0!==r.read_shift(2)+r.read_shift(2)+r.read_shift(2)+r.read_shift(2)&&(s.mt=i(r,r.l-8)),s.start=r.read_shift(4,"i"),s.size=r.read_shift(4,"i"),s.size<0&&s.start<0&&(s.size=s.type=0,s.start=n,s.name=""),5===s.type?(k=s.start,e>0&&k!==n&&(b[k].name="!StreamData")):s.size>=4096?(s.storage="fat",void 0===b[s.start]&&(b[s.start]=function(a,b,c,d,e){var f=[],g=[];e||(e=[]);var h=d-1,i=0,j=0;for(i=b;i>=0;){e[i]=!0,f[f.length]=i,g.push(a[i]);var k=c[Math.floor(4*i/d)];if(d<4+(j=4*i&h))throw Error("FAT boundary crossed: "+i+" 4 "+d);if(!a[k])break;i=b8(a[k],j)}return{nodes:f,data:bM([g])}}(c,s.start,b.fat_addrs,b.ssz)),b[s.start].name=s.name,s.content=b[s.start].data.slice(0,s.size)):(s.storage="minifat",s.size<0?s.size=0:k!==n&&s.start!==n&&b[k]&&(s.content=function(a,b,c){for(var d=a.start,e=a.size,f=[],g=d;c&&e>0&&g>=0;)f.push(b.slice(g*m,g*m+m)),e-=m,g=b8(c,4*g);return 0===f.length?ch(0):J(f).slice(0,a.size)}(s,b[k].data,(b[h]||{}).data))),s.content&&cf(s.content,0),f[j]=s,g.push(s)}})(g,C,z,D,e,{},E,h),function(a,b,c){for(var d=0,e=0,f=0,g=0,h=0,i=c.length,j=[],k=[];d<i;++d)j[d]=k[d]=d,b[d]=c[d];for(;h<k.length;++h)e=a[d=k[h]].L,f=a[d].R,g=a[d].C,j[d]===d&&(-1!==e&&j[e]!==e&&(j[d]=j[e]),-1!==f&&j[f]!==f&&(j[d]=j[f])),-1!==g&&(j[g]=d),-1!==e&&d!=j[d]&&(j[e]=j[d],k.lastIndexOf(e)<h&&k.push(e)),-1!==f&&d!=j[d]&&(j[f]=j[d],k.lastIndexOf(f)<h&&k.push(f));for(d=1;d<i;++d)j[d]===d&&(-1!==f&&j[f]!==f?j[d]=j[f]:-1!==e&&j[e]!==e&&(j[d]=j[e]));for(d=1;d<i;++d)if(0!==a[d].type){if((h=d)!=j[h])do h=j[h],b[d]=b[h]+"/"+b[d];while(0!==h&&-1!==j[h]&&h!=j[h]);j[d]=-1}for(b[0]+="/",d=1;d<i;++d)2!==a[d].type&&(b[d]+="/")}(E,G,D),D.shift();var H={FileIndex:E,FullPaths:G};return b&&b.raw&&(H.raw={header:r,sectors:z}),H}function i(a,b){return new Date((b7(a,b+4)/1e7*0x100000000+b7(a,b)/1e7-0x2b6109100)*1e3)}function j(a,b){var c=b||{},d=c.root||"Root Entry";if(a.FullPaths||(a.FullPaths=[]),a.FileIndex||(a.FileIndex=[]),a.FullPaths.length!==a.FileIndex.length)throw Error("inconsistent CFB structure");0===a.FullPaths.length&&(a.FullPaths[0]=d+"/",a.FileIndex[0]={name:d,type:5}),c.CLSID&&(a.FileIndex[0].clsid=c.CLSID),function(a){var b="\x01Sh33tJ5";if(!aA.find(a,"/"+b)){var c=ch(4);c[0]=55,c[1]=c[3]=50,c[2]=54,a.FileIndex.push({name:b,type:2,content:c,size:4,L:69,R:69,C:69}),a.FullPaths.push(a.FullPaths[0]+b),k(a)}}(a)}function k(a,b){j(a);for(var c=!1,f=!1,g=a.FullPaths.length-1;g>=0;--g){var h=a.FileIndex[g];switch(h.type){case 0:f?c=!0:(a.FileIndex.pop(),a.FullPaths.pop());break;case 1:case 2:case 5:f=!0,isNaN(h.R*h.L*h.C)&&(c=!0),h.R>-1&&h.L>-1&&h.R==h.L&&(c=!0);break;default:c=!0}}if(c||b){var i=new Date(1987,1,19),k=0,l=Object.create?Object.create(null):{},m=[];for(g=0;g<a.FullPaths.length;++g)l[a.FullPaths[g]]=!0,0!==a.FileIndex[g].type&&m.push([a.FullPaths[g],a.FileIndex[g]]);for(g=0;g<m.length;++g){var n=d(m[g][0]);(f=l[n])||(m.push([n,{name:e(n).replace("/",""),type:1,clsid:q,ct:i,mt:i,content:null}]),l[n]=!0)}for(m.sort(function(a,b){return function(a,b){for(var c=a.split("/"),d=b.split("/"),e=0,f=0,g=Math.min(c.length,d.length);e<g;++e){if(f=c[e].length-d[e].length)return f;if(c[e]!=d[e])return c[e]<d[e]?-1:1}return c.length-d.length}(a[0],b[0])}),a.FullPaths=[],a.FileIndex=[],g=0;g<m.length;++g)a.FullPaths[g]=m[g][0],a.FileIndex[g]=m[g][1];for(g=0;g<m.length;++g){var o=a.FileIndex[g],p=a.FullPaths[g];if(o.name=e(p).replace("/",""),o.L=o.R=o.C=-(o.color=1),o.size=o.content?o.content.length:0,o.start=0,o.clsid=o.clsid||q,0===g)o.C=m.length>1?1:-1,o.size=0,o.type=5;else if("/"==p.slice(-1)){for(k=g+1;k<m.length&&d(a.FullPaths[k])!=p;++k);for(o.C=k>=m.length?-1:k,k=g+1;k<m.length&&d(a.FullPaths[k])!=d(p);++k);o.R=k>=m.length?-1:k,o.type=1}else d(a.FullPaths[g+1]||"")==d(p)&&(o.R=g+1),o.type=2}}}function l(a,c){var d=c||{};if("mad"==d.fileType)return function(a,b){for(var c=b||{},d=c.boundary||"SheetJS",e=["MIME-Version: 1.0",'Content-Type: multipart/related; boundary="'+(d="------="+d).slice(2)+'"',"","",""],f=a.FullPaths[0],g=f,h=a.FileIndex[0],i=1;i<a.FullPaths.length;++i)if(g=a.FullPaths[i].slice(f.length),(h=a.FileIndex[i]).size&&h.content&&"\x01Sh33tJ5"!=g){g=g.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF]/g,function(a){return"_x"+a.charCodeAt(0).toString(16)+"_"}).replace(/[\u0080-\uFFFF]/g,function(a){return"_u"+a.charCodeAt(0).toString(16)+"_"});for(var j=h.content,k=B&&Buffer.isBuffer(j)?j.toString("binary"):s(j),l=0,m=Math.min(1024,k.length),n=0,o=0;o<=m;++o)(n=k.charCodeAt(o))>=32&&n<128&&++l;var p=l>=4*m/5;e.push(d),e.push("Content-Location: "+(c.root||"file:///C:/SheetJS/")+g),e.push("Content-Transfer-Encoding: "+(p?"quoted-printable":"base64")),e.push("Content-Type: "+function(a,b){if(a.ctype)return a.ctype;var c=a.name||"",d=c.match(/\.([^\.]+)$/);return d&&ai[d[1]]||b&&(d=(c=b).match(/[\.\\]([^\.\\])+$/))&&ai[d[1]]?ai[d[1]]:"application/octet-stream"}(h,g)),e.push(""),e.push(p?function(a){var b=a.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF=]/g,function(a){var b=a.charCodeAt(0).toString(16).toUpperCase();return"="+(1==b.length?"0"+b:b)});"\n"==(b=b.replace(/ $/mg,"=20").replace(/\t$/mg,"=09")).charAt(0)&&(b="=0D"+b.slice(1)),b=b.replace(/\r(?!\n)/mg,"=0D").replace(/\n\n/mg,"\n=0A").replace(/([^\r\n])\n/mg,"$1=0A");for(var c=[],d=b.split("\r\n"),e=0;e<d.length;++e){var f=d[e];if(0==f.length){c.push("");continue}for(var g=0;g<f.length;){var h=76,i=f.slice(g,g+h);"="==i.charAt(h-1)?h--:"="==i.charAt(h-2)?h-=2:"="==i.charAt(h-3)&&(h-=3),i=f.slice(g,g+h),(g+=h)<f.length&&(i+="="),c.push(i)}}return c.join("\r\n")}(k):function(a){for(var b=z(a),c=[],d=0;d<b.length;d+=76)c.push(b.slice(d,d+76));return c.join("\r\n")+"\r\n"}(k))}return e.push(d+"--\r\n"),e.join("\r\n")}(a,d);if(k(a),"zip"===d.fileType)return function(a,c){var d=[],e=[],f=ch(1),g=8*!!(c||{}).compression,h=0,i=0,j=0,k=0,l=a.FullPaths[0],m=l,n=a.FileIndex[0],o=[],p=0;for(h=1;h<a.FullPaths.length;++h)if(m=a.FullPaths[h].slice(l.length),(n=a.FileIndex[h]).size&&n.content&&"\x01Sh33tJ5"!=m){var q,r=j,s=ch(m.length);for(i=0;i<m.length;++i)s.write_shift(1,127&m.charCodeAt(i));s=s.slice(0,s.l),o[k]=az.buf(n.content,0);var t=n.content;8==g&&(q=t,t=b?b.deflateRawSync(q):_(q)),(f=ch(30)).write_shift(4,0x4034b50),f.write_shift(2,20),f.write_shift(2,0),f.write_shift(2,g),n.mt?function(a,b){"string"==typeof b&&(b=new Date(b));var c=b.getHours();c=(c=c<<6|b.getMinutes())<<5|b.getSeconds()>>>1,a.write_shift(2,c);var d=b.getFullYear()-1980;d=(d=d<<4|b.getMonth()+1)<<5|b.getDate(),a.write_shift(2,d)}(f,n.mt):f.write_shift(4,0),f.write_shift(-4,(0,o[k])),f.write_shift(4,(0,t.length)),f.write_shift(4,(0,n.content.length)),f.write_shift(2,s.length),f.write_shift(2,0),j+=f.length,d.push(f),j+=s.length,d.push(s),j+=t.length,d.push(t),(f=ch(46)).write_shift(4,0x2014b50),f.write_shift(2,0),f.write_shift(2,20),f.write_shift(2,0),f.write_shift(2,g),f.write_shift(4,0),f.write_shift(-4,o[k]),f.write_shift(4,t.length),f.write_shift(4,n.content.length),f.write_shift(2,s.length),f.write_shift(2,0),f.write_shift(2,0),f.write_shift(2,0),f.write_shift(2,0),f.write_shift(4,0),f.write_shift(4,r),p+=f.l,e.push(f),p+=s.length,e.push(s),++k}return(f=ch(22)).write_shift(4,0x6054b50),f.write_shift(2,0),f.write_shift(2,0),f.write_shift(2,k),f.write_shift(2,k),f.write_shift(4,p),f.write_shift(4,j),f.write_shift(2,0),J([J(d),J(e),f])}(a,d);var e=function(a){for(var b=0,c=0,d=0;d<a.FileIndex.length;++d){var e=a.FileIndex[d];if(e.content){var f=e.content.length;f>0&&(f<4096?b+=f+63>>6:c+=f+511>>9)}}for(var g=a.FullPaths.length+3>>2,h=b+7>>3,i=b+127>>7,j=h+c+g+i,k=j+127>>7,l=k<=109?0:Math.ceil((k-109)/127);j+k+l+127>>7>k;)l=++k<=109?0:Math.ceil((k-109)/127);var m=[1,l,k,i,g,c,b,0];return a.FileIndex[0].size=b<<6,m[7]=(a.FileIndex[0].start=m[0]+m[1]+m[2]+m[3]+m[4]+m[5])+(m[6]+7>>3),m}(a),f=ch(e[7]<<9),g=0,h=0;for(g=0;g<8;++g)f.write_shift(1,p[g]);for(g=0;g<8;++g)f.write_shift(2,0);for(f.write_shift(2,62),f.write_shift(2,3),f.write_shift(2,65534),f.write_shift(2,9),f.write_shift(2,6),g=0;g<3;++g)f.write_shift(2,0);for(f.write_shift(4,0),f.write_shift(4,e[2]),f.write_shift(4,e[0]+e[1]+e[2]+e[3]-1),f.write_shift(4,0),f.write_shift(4,4096),f.write_shift(4,e[3]?e[0]+e[1]+e[2]-1:n),f.write_shift(4,e[3]),f.write_shift(-4,e[1]?e[0]-1:n),f.write_shift(4,e[1]),g=0;g<109;++g)f.write_shift(-4,g<e[2]?e[1]+g:-1);if(e[1])for(h=0;h<e[1];++h){for(;g<236+127*h;++g)f.write_shift(-4,g<e[2]?e[1]+g:-1);f.write_shift(-4,h===e[1]-1?n:h+1)}var i=function(a){for(h+=a;g<h-1;++g)f.write_shift(-4,g+1);a&&(++g,f.write_shift(-4,n))};for(h=(g=0)+e[1];g<h;++g)f.write_shift(-4,r.DIFSECT);for(h+=e[2];g<h;++g)f.write_shift(-4,r.FATSECT);i(e[3]),i(e[4]);for(var j=0,l=0,m=a.FileIndex[0];j<a.FileIndex.length;++j)(m=a.FileIndex[j]).content&&((l=m.content.length)<4096||(m.start=h,i(l+511>>9)));for(i(e[6]+7>>3);511&f.l;)f.write_shift(-4,r.ENDOFCHAIN);for(j=0,h=g=0;j<a.FileIndex.length;++j)(m=a.FileIndex[j]).content&&(l=m.content.length)&&!(l>=4096)&&(m.start=h,i(l+63>>6));for(;511&f.l;)f.write_shift(-4,r.ENDOFCHAIN);for(g=0;g<e[4]<<2;++g){var o=a.FullPaths[g];if(!o||0===o.length){for(j=0;j<17;++j)f.write_shift(4,0);for(j=0;j<3;++j)f.write_shift(4,-1);for(j=0;j<12;++j)f.write_shift(4,0);continue}m=a.FileIndex[g],0===g&&(m.start=m.size?m.start-1:n);var q=0===g&&d.root||m.name;if(l=2*(q.length+1),f.write_shift(64,q,"utf16le"),f.write_shift(2,l),f.write_shift(1,m.type),f.write_shift(1,m.color),f.write_shift(-4,m.L),f.write_shift(-4,m.R),f.write_shift(-4,m.C),m.clsid)f.write_shift(16,m.clsid,"hex");else for(j=0;j<4;++j)f.write_shift(4,0);f.write_shift(4,m.state||0),f.write_shift(4,0),f.write_shift(4,0),f.write_shift(4,0),f.write_shift(4,0),f.write_shift(4,m.start),f.write_shift(4,m.size),f.write_shift(4,0)}for(g=1;g<a.FileIndex.length;++g)if((m=a.FileIndex[g]).size>=4096)if(f.l=m.start+1<<9,B&&Buffer.isBuffer(m.content))m.content.copy(f,f.l,0,m.size),f.l+=m.size+511&-512;else{for(j=0;j<m.size;++j)f.write_shift(1,m.content[j]);for(;511&j;++j)f.write_shift(1,0)}for(g=1;g<a.FileIndex.length;++g)if((m=a.FileIndex[g]).size>0&&m.size<4096)if(B&&Buffer.isBuffer(m.content))m.content.copy(f,f.l,0,m.size),f.l+=m.size+63&-64;else{for(j=0;j<m.size;++j)f.write_shift(1,m.content[j]);for(;63&j;++j)f.write_shift(1,0)}if(B)f.l=f.length;else for(;f.l<f.length;)f.write_shift(1,0);return f}c.version="1.2.1";var m=64,n=-2,o="d0cf11e0a1b11ae1",p=[208,207,17,224,161,177,26,225],q="00000000000000000000000000000000",r={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:-2,FREESECT:-1,HEADER_SIGNATURE:o,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:q,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function s(a){for(var b=Array(a.length),c=0;c<a.length;++c)b[c]=String.fromCharCode(a[c]);return b.join("")}for(var t=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],u=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258],v=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577],w="undefined"!=typeof Uint8Array,x=w?new Uint8Array(256):[],y=0;y<256;++y)x[y]=function(a){var b=(a<<1|a<<11)&139536|(a<<5|a<<15)&558144;return(b>>16|b>>8|b)&255}(y);function G(a,b){var c=7&b,d=b>>>3;return(a[d]|(c<=5?0:a[d+1]<<8))>>>c&7}function H(a,b){var c=7&b,d=b>>>3;return(a[d]|(c<=3?0:a[d+1]<<8))>>>c&31}function I(a,b){var c=7&b,d=b>>>3;return(a[d]|(c<=1?0:a[d+1]<<8))>>>c&127}function M(a,b,c){var d=7&b,e=b>>>3,f=(1<<c)-1,g=a[e]>>>d;return c<8-d||(g|=a[e+1]<<8-d,c<16-d||(g|=a[e+2]<<16-d,c<24-d))?g&f:(g|=a[e+3]<<24-d)&f}function N(a,b,c){var d=7&b,e=b>>>3;return d<=5?a[e]|=(7&c)<<d:(a[e]|=c<<d&255,a[e+1]=(7&c)>>8-d),b+3}function O(a,b,c){var d=b>>>3;return c<<=7&b,a[d]|=255&c,c>>>=8,a[d+1]=c,b+8}function P(a,b,c){var d=b>>>3;return c<<=7&b,a[d]|=255&c,c>>>=8,a[d+1]=255&c,a[d+2]=c>>>8,b+16}function Q(a,b){var c=a.length,d=2*c>b?2*c:b+5,e=0;if(c>=b)return a;if(B){var f=E(d);if(a.copy)a.copy(f);else for(;e<a.length;++e)f[e]=a[e];return f}if(w){var g=new Uint8Array(d);if(g.set)g.set(a);else for(;e<c;++e)g[e]=a[e];return g}return a.length=d,a}function R(a){for(var b=Array(a),c=0;c<a;++c)b[c]=0;return b}function S(a,b,c){var d=1,e=0,f=0,g=0,h=0,i=a.length,j=w?new Uint16Array(32):R(32);for(f=0;f<32;++f)j[f]=0;for(f=i;f<c;++f)a[f]=0;i=a.length;var k=w?new Uint16Array(i):R(i);for(f=0;f<i;++f)j[e=a[f]]++,d<e&&(d=e),k[f]=0;for(f=1,j[0]=0;f<=d;++f)j[f+16]=h=h+j[f-1]<<1;for(f=0;f<i;++f)0!=(h=a[f])&&(k[f]=j[h+16]++);var l=0;for(f=0;f<i;++f)if(0!=(l=a[f]))for(h=function(a,b){var c=x[255&a];return b<=8?c>>>8-b:(c=c<<8|x[a>>8&255],b<=16)?c>>>16-b:(c=c<<8|x[a>>16&255])>>>24-b}(k[f],d)>>d-l,g=(1<<d+4-l)-1;g>=0;--g)b[h|g<<l]=15&l|f<<4;return d}var T=w?new Uint16Array(512):R(512),U=w?new Uint16Array(32):R(32);if(!w){for(var V=0;V<512;++V)T[V]=0;for(V=0;V<32;++V)U[V]=0}for(var W=[],X=0;X<32;X++)W.push(5);S(W,U,32);var Y=[];for(X=0;X<=143;X++)Y.push(8);for(;X<=255;X++)Y.push(9);for(;X<=279;X++)Y.push(7);for(;X<=287;X++)Y.push(8);S(Y,T,288);var Z=function(){for(var a=w?new Uint8Array(32768):[],b=0,c=0;b<v.length-1;++b)for(;c<v[b+1];++c)a[c]=b;for(;c<32768;++c)a[c]=29;var d=w?new Uint8Array(259):[];for(b=0,c=0;b<u.length-1;++b)for(;c<u[b+1];++c)d[c]=b;return function(b,c){if(b.length<8){for(var e=0;e<b.length;){var f=Math.min(65535,b.length-e),g=e+f==b.length;for(c.write_shift(1,+g),c.write_shift(2,f),c.write_shift(2,65535&~f);f-- >0;)c[c.l++]=b[e++]}return c.l}return function(b,c){for(var e=0,f=0,g=w?new Uint16Array(32768):[];f<b.length;){var h=Math.min(65535,b.length-f);if(h<10){for(7&(e=N(c,e,+(f+h==b.length)))&&(e+=8-(7&e)),c.l=e/8|0,c.write_shift(2,h),c.write_shift(2,65535&~h);h-- >0;)c[c.l++]=b[f++];e=8*c.l;continue}e=N(c,e,+(f+h==b.length)+2);for(var i=0;h-- >0;){var j,k,l=b[f],m=-1,n=0;if((m=g[i=(i<<5^l)&32767])&&((m|=-32768&f)>f&&(m-=32768),m<f))for(;b[m+n]==b[f+n]&&n<250;)++n;if(n>2){(l=d[n])<=22?e=O(c,e,x[l+1]>>1)-1:(O(c,e,3),O(c,e+=5,x[l-23]>>5),e+=3);var o=l<8?0:l-4>>2;o>0&&(P(c,e,n-u[l]),e+=o),e=O(c,e,x[l=a[f-m]]>>3)-3;var p=l<4?0:l-2>>1;p>0&&(P(c,e,f-m-v[l]),e+=p);for(var q=0;q<n;++q)g[i]=32767&f,i=(i<<5^b[f])&32767,++f;h-=n-1}else l<=143?l+=48:(k=(1&(k=1))<<(7&(j=e)),c[j>>>3]|=k,e=j+1),e=O(c,e,x[l]),g[i]=32767&f,++f}e=O(c,e,0)-1}return c.l=(e+7)/8|0,c.l}(b,c)}}();function _(a){var b=ch(50+Math.floor(1.1*a.length)),c=Z(a,b);return b.slice(0,c)}var aa=w?new Uint16Array(32768):R(32768),ab=w?new Uint16Array(32768):R(32768),ac=w?new Uint16Array(128):R(128),ad=1,ae=1;function af(a,b){var c=function(a,b){if(3==a[0]&&!(3&a[1]))return[D(b),2];for(var c=0,d=0,e=E(b||262144),f=0,g=e.length>>>0,h=0,i=0;(1&d)==0;){if(d=G(a,c),c+=3,d>>>1==0){7&c&&(c+=8-(7&c));var j=a[c>>>3]|a[(c>>>3)+1]<<8;if(c+=32,j>0)for(!b&&g<f+j&&(g=(e=Q(e,f+j)).length);j-- >0;)e[f++]=a[c>>>3],c+=8;continue}for(d>>1==1?(h=9,i=5):(c=function(a,b){var c,d,e,f=H(a,b)+257,g=H(a,b+=5)+1;b+=5;var h=(d=7&(c=b),((a[e=c>>>3]|(d<=4?0:a[e+1]<<8))>>>d&15)+4);b+=4;for(var i=0,j=w?new Uint8Array(19):R(19),k=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],l=1,m=w?new Uint8Array(8):R(8),n=w?new Uint8Array(8):R(8),o=j.length,p=0;p<h;++p)j[t[p]]=i=G(a,b),l<i&&(l=i),m[i]++,b+=3;var q=0;for(p=1,m[0]=0;p<=l;++p)n[p]=q=q+m[p-1]<<1;for(p=0;p<o;++p)0!=(q=j[p])&&(k[p]=n[q]++);var r=0;for(p=0;p<o;++p)if(0!=(r=j[p])){q=x[k[p]]>>8-r;for(var s=(1<<7-r)-1;s>=0;--s)ac[q|s<<r]=7&r|p<<3}var u=[];for(l=1;u.length<f+g;)switch(q=ac[I(a,b)],b+=7&q,q>>>=3){case 16:for(i=3+function(a,b){var c=7&b,d=b>>>3;return(a[d]|(c<=6?0:a[d+1]<<8))>>>c&3}(a,b),b+=2,q=u[u.length-1];i-- >0;)u.push(q);break;case 17:for(i=3+G(a,b),b+=3;i-- >0;)u.push(0);break;case 18:for(i=11+I(a,b),b+=7;i-- >0;)u.push(0);break;default:u.push(q),l<q&&(l=q)}var v=u.slice(0,f),y=u.slice(f);for(p=f;p<286;++p)v[p]=0;for(p=g;p<30;++p)y[p]=0;return ad=S(v,aa,286),ae=S(y,ab,30),b}(a,c),h=ad,i=ae);;){!b&&g<f+32767&&(g=(e=Q(e,f+32767)).length);var k=M(a,c,h),l=d>>>1==1?T[k]:aa[k];if(c+=15&l,((l>>>=4)>>>8&255)==0)e[f++]=l;else if(256==l)break;else{var m=(l-=257)<8?0:l-4>>2;m>5&&(m=0);var n=f+u[l];m>0&&(n+=M(a,c,m),c+=m),k=M(a,c,i),c+=15&(l=d>>>1==1?U[k]:ab[k]);var o=(l>>>=4)<4?0:l-2>>1,p=v[l];for(o>0&&(p+=M(a,c,o),c+=o),!b&&g<n&&(g=(e=Q(e,n+100)).length);f<n;)e[f]=e[f-p],++f}}}return b?[e,c+7>>>3]:[e.slice(0,f),c+7>>>3]}(a.slice(a.l||0),b);return a.l+=c[1],c[0]}function ag(a,b){if(a)"undefined"!=typeof console&&console.error(b);else throw Error(b)}function ah(a,c){cf(a,0);var d={FileIndex:[],FullPaths:[]};j(d,{root:c.root});for(var e=a.length-4;(80!=a[e]||75!=a[e+1]||5!=a[e+2]||6!=a[e+3])&&e>=0;)--e;a.l=e+4,a.l+=4;var g=a.read_shift(2);a.l+=6;var h=a.read_shift(4);for(e=0,a.l=h;e<g;++e){a.l+=20;var i=a.read_shift(4),k=a.read_shift(4),l=a.read_shift(2),m=a.read_shift(2),n=a.read_shift(2);a.l+=8;var o=a.read_shift(4),p=f(a.slice(a.l+l,a.l+l+m));a.l+=l+m+n;var q=a.l;a.l=o+4,function(a,c,d,e,g){a.l+=2;var h,i,j,k,l,m,n,o=a.read_shift(2),p=a.read_shift(2),q=(h=65535&a.read_shift(2),i=65535&a.read_shift(2),j=new Date,k=31&i,l=15&(i>>>=5),i>>>=4,j.setMilliseconds(0),j.setFullYear(i+1980),j.setMonth(l-1),j.setDate(k),m=31&h,n=63&(h>>>=5),h>>>=6,j.setHours(h),j.setMinutes(n),j.setSeconds(m<<1),j);if(8257&o)throw Error("Unsupported ZIP encryption");for(var r=a.read_shift(4),s=a.read_shift(4),t=a.read_shift(4),u=a.read_shift(2),v=a.read_shift(2),w="",x=0;x<u;++x)w+=String.fromCharCode(a[a.l++]);if(v){var y=f(a.slice(a.l,a.l+v));(y[21589]||{}).mt&&(q=y[21589].mt),((g||{})[21589]||{}).mt&&(q=g[21589].mt)}a.l+=v;var z=a.slice(a.l,a.l+s);switch(p){case 8:z=function(a,c){if(!b)return af(a,c);var d=new b.InflateRaw,e=d._processChunk(a.slice(a.l),d._finishFlushFlag);return a.l+=d.bytesRead,e}(a,t);break;case 0:break;default:throw Error("Unsupported ZIP Compression method "+p)}var A=!1;8&o&&(0x8074b50==a.read_shift(4)&&(a.read_shift(4),A=!0),s=a.read_shift(4),t=a.read_shift(4)),s!=c&&ag(A,"Bad compressed size: "+c+" != "+s),t!=d&&ag(A,"Bad uncompressed size: "+d+" != "+t),aj(e,w,z,{unsafe:!0,mt:q})}(a,i,k,d,p),a.l=q}return d}var ai={htm:"text/html",xml:"text/xml",gif:"image/gif",jpg:"image/jpeg",png:"image/png",mso:"application/x-mso",thmx:"application/vnd.ms-officetheme",sh33tj5:"application/octet-stream"};function aj(a,b,c,d){var f=d&&d.unsafe;f||j(a);var g=!f&&aA.find(a,b);if(!g){var h=a.FullPaths[0];b.slice(0,h.length)==h?h=b:("/"!=h.slice(-1)&&(h+="/"),h=(h+b).replace("//","/")),g={name:e(b),type:2},a.FileIndex.push(g),a.FullPaths.push(h),f||aA.utils.cfb_gc(a)}return g.content=c,g.size=c?c.length:0,d&&(d.CLSID&&(g.clsid=d.CLSID),d.mt&&(g.mt=d.mt),d.ct&&(g.ct=d.ct)),g}return c.find=function(a,b){var c=a.FullPaths.map(function(a){return a.toUpperCase()}),d=c.map(function(a){var b=a.split("/");return b[b.length-("/"==a.slice(-1)?2:1)]}),e=!1;47===b.charCodeAt(0)?(e=!0,b=c[0].slice(0,-1)+b):e=-1!==b.indexOf("/");var f=b.toUpperCase(),g=!0===e?c.indexOf(f):d.indexOf(f);if(-1!==g)return a.FileIndex[g];var h=!f.match(L);for(f=f.replace(K,""),h&&(f=f.replace(L,"!")),g=0;g<c.length;++g)if((h?c[g].replace(L,"!"):c[g]).replace(K,"")==f||(h?d[g].replace(L,"!"):d[g]).replace(K,"")==f)return a.FileIndex[g];return null},c.read=function(b,c){var d=c&&c.type;switch(!d&&B&&Buffer.isBuffer(b)&&(d="buffer"),d||"base64"){case"file":return g(),h(a.readFileSync(b),c);case"base64":return h(F(A(b)),c);case"binary":return h(F(b),c)}return h(b,c)},c.parse=h,c.write=function(b,c){var d=l(b,c);switch(c&&c.type||"buffer"){case"file":g(),a.writeFileSync(c.filename,d);break;case"binary":return"string"==typeof d?d:s(d);case"base64":return z("string"==typeof d?d:s(d));case"buffer":if(B)return Buffer.isBuffer(d)?d:C(d);case"array":return"string"==typeof d?F(d):d}return d},c.writeFile=function(b,c,d){g();var e=l(b,d);a.writeFileSync(c,e)},c.utils={cfb_new:function(a){var b={};return j(b,a),b},cfb_add:aj,cfb_del:function(a,b){j(a);var c=aA.find(a,b);if(c){for(var d=0;d<a.FileIndex.length;++d)if(a.FileIndex[d]==c)return a.FileIndex.splice(d,1),a.FullPaths.splice(d,1),!0}return!1},cfb_mov:function(a,b,c){j(a);var d=aA.find(a,b);if(d){for(var f=0;f<a.FileIndex.length;++f)if(a.FileIndex[f]==d)return a.FileIndex[f].name=e(c),a.FullPaths[f]=c,!0}return!1},cfb_gc:function(a){k(a,!0)},ReadShift:b9,CheckField:ce,prep_blob:cf,bconcat:J,use_zlib:function(a){try{var c=new a.InflateRaw;if(c._processChunk(new Uint8Array([3,0]),c._finishFlushFlag),c.bytesRead)b=a;else throw Error("zlib does not expose bytesRead")}catch(a){console.error("cannot use native zlib: "+(a.message||a))}},_deflateRaw:_,_inflateRaw:af,consts:r},c}();function aB(a,b,c){if(void 0!==d&&d.writeFileSync)return c?d.writeFileSync(a,b,c):d.writeFileSync(a,b);if("undefined"!=typeof Deno){if(c&&"string"==typeof b)switch(c){case"utf8":b=new TextEncoder(c).encode(b);break;case"binary":b=G(b);break;default:throw Error("Unsupported encoding "+c)}return Deno.writeFileSync(a,b)}var e="utf8"==c?bu(b):b;if("undefined"!=typeof IE_SaveFile)return IE_SaveFile(e,a);if("undefined"!=typeof Blob){var f=new Blob([function(a){if("string"==typeof a)return G(a);if(Array.isArray(a)){if("undefined"==typeof Uint8Array)throw Error("Unsupported");return new Uint8Array(a)}return a}(e)],{type:"application/octet-stream"});if("undefined"!=typeof navigator&&navigator.msSaveBlob)return navigator.msSaveBlob(f,a);if("undefined"!=typeof saveAs)return saveAs(f,a);if("undefined"!=typeof URL&&"undefined"!=typeof document&&document.createElement&&URL.createObjectURL){var g=URL.createObjectURL(f);if("object"==typeof chrome&&"function"==typeof(chrome.downloads||{}).download)return URL.revokeObjectURL&&"undefined"!=typeof setTimeout&&setTimeout(function(){URL.revokeObjectURL(g)},6e4),chrome.downloads.download({url:g,filename:a,saveAs:!0});var h=document.createElement("a");if(null!=h.download)return h.download=a,h.href=g,document.body.appendChild(h),h.click(),document.body.removeChild(h),URL.revokeObjectURL&&"undefined"!=typeof setTimeout&&setTimeout(function(){URL.revokeObjectURL(g)},6e4),g}}if("undefined"!=typeof $&&"undefined"!=typeof File&&"undefined"!=typeof Folder)try{var i=File(a);return i.open("w"),i.encoding="binary",Array.isArray(b)&&(b=H(b)),i.write(b),i.close(),b}catch(a){if(!a.message||!a.message.match(/onstruct/))throw a}throw Error("cannot save file "+a)}function aC(a){for(var b=Object.keys(a),c=[],d=0;d<b.length;++d)Object.prototype.hasOwnProperty.call(a,b[d])&&c.push(b[d]);return c}function aD(a,b){for(var c=[],d=aC(a),e=0;e!==d.length;++e)null==c[a[d[e]][b]]&&(c[a[d[e]][b]]=d[e]);return c}function aE(a){for(var b=[],c=aC(a),d=0;d!==c.length;++d)b[a[c[d]]]=c[d];return b}function aF(a){for(var b=[],c=aC(a),d=0;d!==c.length;++d)b[a[c[d]]]=parseInt(c[d],10);return b}var aG=new Date(1899,11,30,0,0,0);function aH(a,b){var c=a.getTime();return b&&(c-=1263168e5),(c-(aG.getTime()+(a.getTimezoneOffset()-aG.getTimezoneOffset())*6e4))/864e5}var aI=new Date,aJ=aG.getTime()+(aI.getTimezoneOffset()-aG.getTimezoneOffset())*6e4,aK=aI.getTimezoneOffset();function aL(a){var b=new Date;return b.setTime(24*a*36e5+aJ),b.getTimezoneOffset()!==aK&&b.setTime(b.getTime()+(b.getTimezoneOffset()-aK)*6e4),b}var aM=new Date("2017-02-19T19:06:09.000Z"),aN=isNaN(aM.getFullYear())?new Date("2/19/17"):aM,aO=2017==aN.getFullYear();function aP(a,b){var c=new Date(a);if(aO)return b>0?c.setTime(c.getTime()+60*c.getTimezoneOffset()*1e3):b<0&&c.setTime(c.getTime()-60*c.getTimezoneOffset()*1e3),c;if(a instanceof Date)return a;if(1917==aN.getFullYear()&&!isNaN(c.getFullYear())){var d=c.getFullYear();return a.indexOf(""+d)>-1||c.setFullYear(c.getFullYear()+100),c}var e=a.match(/\d+/g)||["2017","2","19","0","0","0"],f=new Date(+e[0],e[1]-1,+e[2],+e[3]||0,+e[4]||0,+e[5]||0);return a.indexOf("Z")>-1&&(f=new Date(f.getTime()-60*f.getTimezoneOffset()*1e3)),f}function aQ(a,b){if(B&&Buffer.isBuffer(a)){if(b){if(255==a[0]&&254==a[1])return bu(a.slice(2).toString("utf16le"));if(254==a[1]&&255==a[2])return bu(u(a.slice(2).toString("binary")))}return a.toString("binary")}if("undefined"!=typeof TextDecoder)try{if(b){if(255==a[0]&&254==a[1])return bu(new TextDecoder("utf-16le").decode(a.slice(2)));if(254==a[0]&&255==a[1])return bu(new TextDecoder("utf-16be").decode(a.slice(2)))}var c={"€":"\x80","‚":"\x82",ƒ:"\x83","„":"\x84","…":"\x85","†":"\x86","‡":"\x87",ˆ:"\x88","‰":"\x89",Š:"\x8a","‹":"\x8b",Œ:"\x8c",Ž:"\x8e","‘":"\x91","’":"\x92","“":"\x93","”":"\x94","•":"\x95","–":"\x96","—":"\x97","˜":"\x98","™":"\x99",š:"\x9a","›":"\x9b",œ:"\x9c",ž:"\x9e",Ÿ:"\x9f"};return Array.isArray(a)&&(a=new Uint8Array(a)),new TextDecoder("latin1").decode(a).replace(/[€‚ƒ„…†‡ˆ‰Š‹ŒŽ‘’“”•–—˜™š›œžŸ]/g,function(a){return c[a]||a})}catch(a){}for(var d=[],e=0;e!=a.length;++e)d.push(String.fromCharCode(a[e]));return d.join("")}function aR(a){if("undefined"!=typeof JSON&&!Array.isArray(a))return JSON.parse(JSON.stringify(a));if("object"!=typeof a||null==a)return a;if(a instanceof Date)return new Date(a.getTime());var b={};for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&(b[c]=aR(a[c]));return b}function aS(a,b){for(var c="";c.length<b;)c+=a;return c}function aT(a){var b=Number(a);if(!isNaN(b))return isFinite(b)?b:NaN;if(!/\d/.test(a))return b;var c=1,d=a.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,function(){return c*=100,""});return isNaN(b=Number(d))&&isNaN(b=Number(d=d.replace(/[(](.*)[)]/,function(a,b){return c=-c,b})))?b:b/c}var aU=["january","february","march","april","may","june","july","august","september","october","november","december"];function aV(a){var b=new Date(a),c=new Date(NaN),d=b.getYear(),e=b.getMonth(),f=b.getDate();if(isNaN(f))return c;var g=a.toLowerCase();if(g.match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)){if((g=g.replace(/[^a-z]/g,"").replace(/([^a-z]|^)[ap]m?([^a-z]|$)/,"")).length>3&&-1==aU.indexOf(g))return c}else if(g.match(/[a-z]/))return c;return d<0||d>8099?c:(e>0||f>1)&&101!=d?b:a.match(/[^-0-9:,\/\\]/)?c:b}var aW=function(){var a=5=="abacaba".split(/(:?b)/i).length;return function(b,c,d){if(a||"string"==typeof c)return b.split(c);for(var e=b.split(c),f=[e[0]],g=1;g<e.length;++g)f.push(d),f.push(e[g]);return f}}();function aX(a){return a?a.content&&a.type?aQ(a.content,!0):a.data?v(a.data):a.asNodeBuffer&&B?v(a.asNodeBuffer().toString("binary")):a.asBinary?v(a.asBinary()):a._data&&a._data.getContent?v(aQ(Array.prototype.slice.call(a._data.getContent(),0))):null:null}function aY(a){if(!a)return null;if(a.data)return t(a.data);if(a.asNodeBuffer&&B)return a.asNodeBuffer();if(a._data&&a._data.getContent){var b=a._data.getContent();return"string"==typeof b?t(b):Array.prototype.slice.call(b)}return a.content&&a.type?a.content:null}function aZ(a,b){for(var c=a.FullPaths||aC(a.files),d=b.toLowerCase().replace(/[\/]/g,"\\"),e=d.replace(/\\/g,"/"),f=0;f<c.length;++f){var g=c[f].replace(/^Root Entry[\/]/,"").toLowerCase();if(d==g||e==g)return a.files?a.files[c[f]]:a.FileIndex[f]}return null}function a$(a,b){var c=aZ(a,b);if(null==c)throw Error("Cannot find file "+b+" in zip");return c}function a_(a,b,c){if(!c){var d;return(d=a$(a,b))&&".bin"===d.name.slice(-4)?aY(d):aX(d)}if(!b)return null;try{return a_(a,b)}catch(a){return null}}function a0(a,b,c){if(!c)return aX(a$(a,b));if(!b)return null;try{return a0(a,b)}catch(a){return null}}function a1(a){for(var b=a.FullPaths||aC(a.files),c=[],d=0;d<b.length;++d)"/"!=b[d].slice(-1)&&c.push(b[d].replace(/^Root Entry[\/]/,""));return c.sort()}function a2(a,b,c){if(a.FullPaths){if("string"==typeof c){var d;return d=B?C(c):function(a){for(var b=[],c=0,d=a.length+250,e=D(a.length+255),f=0;f<a.length;++f){var g=a.charCodeAt(f);if(g<128)e[c++]=g;else if(g<2048)e[c++]=192|g>>6&31,e[c++]=128|63&g;else if(g>=55296&&g<57344){g=(1023&g)+64;var h=1023&a.charCodeAt(++f);e[c++]=240|g>>8&7,e[c++]=128|g>>2&63,e[c++]=128|h>>6&15|(3&g)<<4,e[c++]=128|63&h}else e[c++]=224|g>>12&15,e[c++]=128|g>>6&63,e[c++]=128|63&g;c>d&&(b.push(e.slice(0,c)),c=0,e=D(65535),d=65530)}return b.push(e.slice(0,c)),J(b)}(c),aA.utils.cfb_add(a,b,d)}aA.utils.cfb_add(a,b,c)}else a.file(b,c)}function a3(){return aA.utils.cfb_new()}function a4(a,b){switch(b.type){case"base64":return aA.read(a,{type:"base64"});case"binary":return aA.read(a,{type:"binary"});case"buffer":case"array":return aA.read(a,{type:"buffer"})}throw Error("Unrecognized type "+b.type)}function a5(a,b){if("/"==a.charAt(0))return a.slice(1);var c=b.split("/");"/"!=b.slice(-1)&&c.pop();for(var d=a.split("/");0!==d.length;){var e=d.shift();".."===e?c.pop():"."!==e&&c.push(e)}return c.join("/")}var a6='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r\n',a7=/([^"\s?>\/]+)\s*=\s*((?:")([^"]*)(?:")|(?:')([^']*)(?:')|([^'">\s]+))/g,a8=/<[\/\?]?[a-zA-Z0-9:_-]+(?:\s+[^"\s?>\/]+\s*=\s*(?:"[^"]*"|'[^']*'|[^'">\s=]+))*\s*[\/\?]?>/mg,a9=a6.match(a8)?a8:/<[^>]*>/g,ba=/<\w*:/,bb=/<(\/?)\w+:/;function bc(a,b,c){for(var d={},e=0,f=0;e!==a.length&&32!==(f=a.charCodeAt(e))&&10!==f&&13!==f;++e);if(b||(d[0]=a.slice(0,e)),e===a.length)return d;var g=a.match(a7),h=0,i="",j=0,k="",l="",m=1;if(g)for(j=0;j!=g.length;++j){for(f=0,l=g[j];f!=l.length&&61!==l.charCodeAt(f);++f);for(k=l.slice(0,f).trim();32==l.charCodeAt(f+1);)++f;for(h=0,m=+(34==(e=l.charCodeAt(f+1))||39==e),i=l.slice(f+1+m,l.length-m);h!=k.length&&58!==k.charCodeAt(h);++h);if(h===k.length)k.indexOf("_")>0&&(k=k.slice(0,k.indexOf("_"))),d[k]=i,c||(d[k.toLowerCase()]=i);else{var n=(5===h&&"xmlns"===k.slice(0,5)?"xmlns":"")+k.slice(h+1);if(d[n]&&"ext"==k.slice(h-3,h))continue;d[n]=i,c||(d[n.toLowerCase()]=i)}}return d}function bd(a){return a.replace(bb,"<$1")}var be={"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"},bf=aE(be),bg=function(){var a=/&(?:quot|apos|gt|lt|amp|#x?([\da-fA-F]+));/ig,b=/_x([\da-fA-F]{4})_/ig;return function c(d){var e=d+"",f=e.indexOf("<![CDATA[");if(-1==f)return e.replace(a,function(a,b){return be[a]||String.fromCharCode(parseInt(b,a.indexOf("x")>-1?16:10))||a}).replace(b,function(a,b){return String.fromCharCode(parseInt(b,16))});var g=e.indexOf("]]>");return c(e.slice(0,f))+e.slice(f+9,g)+c(e.slice(g+3))}}(),bh=/[&<>'"]/g,bi=/[\u0000-\u0008\u000b-\u001f]/g;function bj(a){return(a+"").replace(bh,function(a){return bf[a]}).replace(bi,function(a){return"_x"+("000"+a.charCodeAt(0).toString(16)).slice(-4)+"_"})}function bk(a){return bj(a).replace(/ /g,"_x0020_")}var bl=/[\u0000-\u001f]/g;function bm(a){return(a+"").replace(bh,function(a){return bf[a]}).replace(/\n/g,"<br/>").replace(bl,function(a){return"&#x"+("000"+a.charCodeAt(0).toString(16)).slice(-4)+";"})}var bn=function(){var a=/&#(\d+);/g;function b(a,b){return String.fromCharCode(parseInt(b,10))}return function(c){return c.replace(a,b)}}();function bo(a){switch(a){case 1:case!0:case"1":case"true":case"TRUE":return!0;default:return!1}}function bp(a){for(var b="",c=0,d=0,e=0,f=0,g=0,h=0;c<a.length;){if((d=a.charCodeAt(c++))<128){b+=String.fromCharCode(d);continue}if(e=a.charCodeAt(c++),d>191&&d<224){b+=String.fromCharCode((31&d)<<6|63&e);continue}if(f=a.charCodeAt(c++),d<240){b+=String.fromCharCode((15&d)<<12|(63&e)<<6|63&f);continue}b+=String.fromCharCode(55296+((h=((7&d)<<18|(63&e)<<12|(63&f)<<6|63&a.charCodeAt(c++))-65536)>>>10&1023)),b+=String.fromCharCode(56320+(1023&h))}return b}function bq(a){var b,c,d,e=D(2*a.length),f=1,g=0,h=0;for(c=0;c<a.length;c+=f)f=1,(d=a.charCodeAt(c))<128?b=d:d<224?(b=(31&d)*64+(63&a.charCodeAt(c+1)),f=2):d<240?(b=(15&d)*4096+(63&a.charCodeAt(c+1))*64+(63&a.charCodeAt(c+2)),f=3):(f=4,h=55296+((b=(7&d)*262144+(63&a.charCodeAt(c+1))*4096+(63&a.charCodeAt(c+2))*64+(63&a.charCodeAt(c+3))-65536)>>>10&1023),b=56320+(1023&b)),0!==h&&(e[g++]=255&h,e[g++]=h>>>8,h=0),e[g++]=b%256,e[g++]=b>>>8;return e.slice(0,g).toString("ucs2")}function br(a){return C(a,"binary").toString("utf8")}var bs="foo bar baz\xe2\x98\x83\xf0\x9f\x8d\xa3",bt=B&&(br(bs)==bp(bs)&&br||bq(bs)==bp(bs)&&bq)||bp,bu=B?function(a){return C(a,"utf8").toString("binary")}:function(a){for(var b=[],c=0,d=0,e=0;c<a.length;)switch(d=a.charCodeAt(c++),!0){case d<128:b.push(String.fromCharCode(d));break;case d<2048:b.push(String.fromCharCode(192+(d>>6))),b.push(String.fromCharCode(128+(63&d)));break;case d>=55296&&d<57344:d-=55296,b.push(String.fromCharCode(240+((e=a.charCodeAt(c++)-56320+(d<<10))>>18&7))),b.push(String.fromCharCode(144+(e>>12&63))),b.push(String.fromCharCode(128+(e>>6&63))),b.push(String.fromCharCode(128+(63&e)));break;default:b.push(String.fromCharCode(224+(d>>12))),b.push(String.fromCharCode(128+(d>>6&63))),b.push(String.fromCharCode(128+(63&d)))}return b.join("")},bv=function(){var a={};return function(b,c){var d=b+"|"+(c||"");return a[d]?a[d]:a[d]=RegExp("<(?:\\w+:)?"+b+'(?: xml:space="preserve")?(?:[^>]*)>([\\s\\S]*?)</(?:\\w+:)?'+b+">",c||"")}}(),bw=function(){var a=[["nbsp"," "],["middot","\xb7"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map(function(a){return[RegExp("&"+a[0]+";","ig"),a[1]]});return function(b){for(var c=b.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/>\s+/g,">").replace(/\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,"\n").replace(/<[^>]*>/g,""),d=0;d<a.length;++d)c=c.replace(a[d][0],a[d][1]);return c}}(),bx=function(){var a={};return function(b){return void 0!==a[b]?a[b]:a[b]=RegExp("<(?:vt:)?"+b+">([\\s\\S]*?)</(?:vt:)?"+b+">","g")}}(),by=/<\/?(?:vt:)?variant>/g,bz=/<(?:vt:)([^>]*)>([\s\S]*)</;function bA(a,b){var c=bc(a),d=a.match(bx(c.baseType))||[],e=[];if(d.length!=c.size){if(b.WTF)throw Error("unexpected vector length "+d.length+" != "+c.size);return e}return d.forEach(function(a){var b=a.replace(by,"").match(bz);b&&e.push({v:bt(b[2]),t:b[1]})}),e}var bB=/(^\s|\s$|\n)/;function bC(a,b){return"<"+a+(b.match(bB)?' xml:space="preserve"':"")+">"+b+"</"+a+">"}function bD(a){return aC(a).map(function(b){return" "+b+'="'+a[b]+'"'}).join("")}function bE(a,b,c){return"<"+a+(null!=c?bD(c):"")+(null!=b?(b.match(bB)?' xml:space="preserve"':"")+">"+b+"</"+a:"/")+">"}function bF(a,b){try{return a.toISOString().replace(/\.\d*/,"")}catch(a){if(b)throw a}return""}function bG(a){if(B&&Buffer.isBuffer(a))return a.toString("utf8");if("string"==typeof a)return a;if("undefined"!=typeof Uint8Array&&a instanceof Uint8Array)return bt(H(I(a)));throw Error("Bad input format: expected Buffer or string")}var bH=/<(\/?)([^\s?><!\/:]*:|)([^\s?<>:\/]+)(?:[\s?:\/][^>]*)?>/mg,bI={CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/metadata/core-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/custom-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/extended-properties",CT:"http://schemas.openxmlformats.org/package/2006/content-types",RELS:"http://schemas.openxmlformats.org/package/2006/relationships",TCMNT:"http://schemas.microsoft.com/office/spreadsheetml/2018/threadedcomments",dc:"http://purl.org/dc/elements/1.1/",dcterms:"http://purl.org/dc/terms/",dcmitype:"http://purl.org/dc/dcmitype/",r:"http://schemas.openxmlformats.org/officeDocument/2006/relationships",vt:"http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes",xsi:"http://www.w3.org/2001/XMLSchema-instance",xsd:"http://www.w3.org/2001/XMLSchema"},bJ=["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"],bK={o:"urn:schemas-microsoft-com:office:office",x:"urn:schemas-microsoft-com:office:excel",ss:"urn:schemas-microsoft-com:office:spreadsheet",dt:"uuid:C2F41010-65B3-11d1-A29F-00AA00C14882",mv:"http://macVmlSchemaUri",v:"urn:schemas-microsoft-com:vml",html:"http://www.w3.org/TR/REC-html40"},bL=function(a){for(var b=[],c=0;c<a[0].length;++c)if(a[0][c])for(var d=0,e=a[0][c].length;d<e;d+=10240)b.push.apply(b,a[0][c].slice(d,d+10240));return b},bM=B?function(a){return a[0].length>0&&Buffer.isBuffer(a[0][0])?Buffer.concat(a[0].map(function(a){return Buffer.isBuffer(a)?a:C(a)})):bL(a)}:bL,bN=function(a,b,c){for(var d=[],e=b;e<c;e+=2)d.push(String.fromCharCode(b5(a,e)));return d.join("").replace(K,"")},bO=B?function(a,b,c){return Buffer.isBuffer(a)?a.toString("utf16le",b,c).replace(K,""):bN(a,b,c)}:bN,bP=function(a,b,c){for(var d=[],e=b;e<b+c;++e)d.push(("0"+a[e].toString(16)).slice(-2));return d.join("")},bQ=B?function(a,b,c){return Buffer.isBuffer(a)?a.toString("hex",b,b+c):bP(a,b,c)}:bP,bR=function(a,b,c){for(var d=[],e=b;e<c;e++)d.push(String.fromCharCode(b4(a,e)));return d.join("")},bS=B?function(a,b,c){return Buffer.isBuffer(a)?a.toString("utf8",b,c):bR(a,b,c)}:bR,bT=function(a,b){var c=b7(a,b);return c>0?bS(a,b+4,b+4+c-1):""},bU=bT,bV=function(a,b){var c=b7(a,b);return c>0?bS(a,b+4,b+4+c-1):""},bW=bV,bX=function(a,b){var c=2*b7(a,b);return c>0?bS(a,b+4,b+4+c-1):""},bY=bX,bZ=function(a,b){var c=b7(a,b);return c>0?bO(a,b+4,b+4+c):""},b$=bZ,b_=function(a,b){var c=b7(a,b);return c>0?bS(a,b+4,b+4+c):""},b0=b_,b1=function(a,b){for(var c=1-2*(a[b+7]>>>7),d=((127&a[b+7])<<4)+(a[b+6]>>>4&15),e=15&a[b+6],f=5;f>=0;--f)e=256*e+a[b+f];return 2047==d?0==e?1/0*c:NaN:(0==d?d=-1022:(d-=1023,e+=0x10000000000000),c*Math.pow(2,d-52)*e)},b2=b1,b3=function(a){return Array.isArray(a)||"undefined"!=typeof Uint8Array&&a instanceof Uint8Array};B&&(bU=function(a,b){if(!Buffer.isBuffer(a))return bT(a,b);var c=a.readUInt32LE(b);return c>0?a.toString("utf8",b+4,b+4+c-1):""},bW=function(a,b){if(!Buffer.isBuffer(a))return bV(a,b);var c=a.readUInt32LE(b);return c>0?a.toString("utf8",b+4,b+4+c-1):""},bY=function(a,b){if(!Buffer.isBuffer(a))return bX(a,b);var c=2*a.readUInt32LE(b);return a.toString("utf16le",b+4,b+4+c-1)},b$=function(a,b){if(!Buffer.isBuffer(a))return bZ(a,b);var c=a.readUInt32LE(b);return a.toString("utf16le",b+4,b+4+c)},b0=function(a,b){if(!Buffer.isBuffer(a))return b_(a,b);var c=a.readUInt32LE(b);return a.toString("utf8",b+4,b+4+c)},b2=function(a,b){return Buffer.isBuffer(a)?a.readDoubleLE(b):b1(a,b)},b3=function(a){return Buffer.isBuffer(a)||Array.isArray(a)||"undefined"!=typeof Uint8Array&&a instanceof Uint8Array}),void 0!==e&&(bO=function(a,b,c){return e.utils.decode(1200,a.slice(b,c)).replace(K,"")},bS=function(a,b,c){return e.utils.decode(65001,a.slice(b,c))},bU=function(a,b){var c=b7(a,b);return c>0?e.utils.decode(n,a.slice(b+4,b+4+c-1)):""},bW=function(a,b){var c=b7(a,b);return c>0?e.utils.decode(m,a.slice(b+4,b+4+c-1)):""},bY=function(a,b){var c=2*b7(a,b);return c>0?e.utils.decode(1200,a.slice(b+4,b+4+c-1)):""},b$=function(a,b){var c=b7(a,b);return c>0?e.utils.decode(1200,a.slice(b+4,b+4+c)):""},b0=function(a,b){var c=b7(a,b);return c>0?e.utils.decode(65001,a.slice(b+4,b+4+c)):""});var b4=function(a,b){return a[b]},b5=function(a,b){return 256*a[b+1]+a[b]},b6=function(a,b){var c=256*a[b+1]+a[b];return c<32768?c:-((65535-c+1)*1)},b7=function(a,b){return 0x1000000*a[b+3]+(a[b+2]<<16)+(a[b+1]<<8)+a[b]},b8=function(a,b){return a[b+3]<<24|a[b+2]<<16|a[b+1]<<8|a[b]};function b9(a,b){var c,d,f,g,h,i,j="",k=[];switch(b){case"dbcs":if(i=this.l,B&&Buffer.isBuffer(this))j=this.slice(this.l,this.l+2*a).toString("utf16le");else for(h=0;h<a;++h)j+=String.fromCharCode(b5(this,i)),i+=2;a*=2;break;case"utf8":j=bS(this,this.l,this.l+a);break;case"utf16le":a*=2,j=bO(this,this.l,this.l+a);break;case"wstr":if(void 0===e)return b9.call(this,a,"dbcs");j=e.utils.decode(m,this.slice(this.l,this.l+2*a)),a*=2;break;case"lpstr-ansi":j=bU(this,this.l),a=4+b7(this,this.l);break;case"lpstr-cp":j=bW(this,this.l),a=4+b7(this,this.l);break;case"lpwstr":j=bY(this,this.l),a=4+2*b7(this,this.l);break;case"lpp4":a=4+b7(this,this.l),j=b$(this,this.l),2&a&&(a+=2);break;case"8lpp4":a=4+b7(this,this.l),j=b0(this,this.l),3&a&&(a+=4-(3&a));break;case"cstr":for(a=0,j="";0!==(f=b4(this,this.l+a++));)k.push(w(f));j=k.join("");break;case"_wstr":for(a=0,j="";0!==(f=b5(this,this.l+a));)k.push(w(f)),a+=2;a+=2,j=k.join("");break;case"dbcs-cont":for(h=0,j="",i=this.l;h<a;++h){if(this.lens&&-1!==this.lens.indexOf(i))return f=b4(this,i),this.l=i+1,g=b9.call(this,a-h,f?"dbcs-cont":"sbcs-cont"),k.join("")+g;k.push(w(b5(this,i))),i+=2}j=k.join(""),a*=2;break;case"cpstr":if(void 0!==e){j=e.utils.decode(m,this.slice(this.l,this.l+a));break}case"sbcs-cont":for(h=0,j="",i=this.l;h!=a;++h){if(this.lens&&-1!==this.lens.indexOf(i))return f=b4(this,i),this.l=i+1,g=b9.call(this,a-h,f?"dbcs-cont":"sbcs-cont"),k.join("")+g;k.push(w(b4(this,i))),i+=1}j=k.join("");break;default:switch(a){case 1:return c=b4(this,this.l),this.l++,c;case 2:return c=("i"===b?b6:b5)(this,this.l),this.l+=2,c;case 4:case -4:if("i"===b||(128&this[this.l+3])==0)return c=(a>0?b8:function(a,b){return a[b]<<24|a[b+1]<<16|a[b+2]<<8|a[b+3]})(this,this.l),this.l+=4,c;return d=b7(this,this.l),this.l+=4,d;case 8:case -8:if("f"===b)return d=8==a?b2(this,this.l):b2([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0),this.l+=8,d;a=8;case 16:j=bQ(this,this.l,a)}}return this.l+=a,j}var ca=function(a,b,c){a[c]=255&b,a[c+1]=b>>>8&255,a[c+2]=b>>>16&255,a[c+3]=b>>>24&255},cb=function(a,b,c){a[c]=255&b,a[c+1]=b>>8&255,a[c+2]=b>>16&255,a[c+3]=b>>24&255},cc=function(a,b,c){a[c]=255&b,a[c+1]=b>>>8&255};function cd(a,b,c){var d=0,f=0;if("dbcs"===c){for(f=0;f!=b.length;++f)cc(this,b.charCodeAt(f),this.l+2*f);d=2*b.length}else if("sbcs"===c){if(void 0!==e&&874==n)for(f=0;f!=b.length;++f){var g=e.utils.encode(n,b.charAt(f));this[this.l+f]=g[0]}else for(f=0,b=b.replace(/[^\x00-\x7F]/g,"_");f!=b.length;++f)this[this.l+f]=255&b.charCodeAt(f);d=b.length}else if("hex"===c){for(;f<a;++f)this[this.l++]=parseInt(b.slice(2*f,2*f+2),16)||0;return this}else if("utf16le"===c){var h=Math.min(this.l+a,this.length);for(f=0;f<Math.min(b.length,a);++f){var i=b.charCodeAt(f);this[this.l++]=255&i,this[this.l++]=i>>8}for(;this.l<h;)this[this.l++]=0;return this}else switch(a){case 1:d=1,this[this.l]=255&b;break;case 2:d=2,this[this.l]=255&b,b>>>=8,this[this.l+1]=255&b;break;case 3:d=3,this[this.l]=255&b,b>>>=8,this[this.l+1]=255&b,b>>>=8,this[this.l+2]=255&b;break;case 4:d=4,ca(this,b,this.l);break;case 8:if(d=8,"f"===c){!function(a,b,c){var d=(b<0||1/b==-1/0)<<7,e=0,f=0,g=d?-b:b;isFinite(g)?0==g?e=f=0:(e=Math.floor(Math.log(g)/Math.LN2),f=g*Math.pow(2,52-e),e<=-1023&&(!isFinite(f)||f<0x10000000000000)?e=-1022:(f-=0x10000000000000,e+=1023)):(e=2047,f=26985*!!isNaN(b));for(var h=0;h<=5;++h,f/=256)a[c+h]=255&f;a[c+6]=(15&e)<<4|15&f,a[c+7]=e>>4|d}(this,b,this.l);break}case 16:break;case -4:d=4,cb(this,b,this.l)}return this.l+=d,this}function ce(a,b){var c=bQ(this,this.l,a.length>>1);if(c!==a)throw Error(b+"Expected "+a+" saw "+c);this.l+=a.length>>1}function cf(a,b){a.l=b,a.read_shift=b9,a.chk=ce,a.write_shift=cd}function cg(a,b){a.l+=b}function ch(a){var b=D(a);return cf(b,0),b}function ci(a,b,c){if(a){cf(a,a.l||0);for(var d,e,f,g=a.length,h=0,i=0;a.l<g;){128&(h=a.read_shift(1))&&(h=(127&h)+((127&a.read_shift(1))<<7));var j=gv[h]||gv[65535];for(e=1,f=127&(d=a.read_shift(1));e<4&&128&d;++e)f+=(127&(d=a.read_shift(1)))<<7*e;i=a.l+f;var k=j.f&&j.f(a,f,c);if(a.l=i,b(k,j,h))return}}}function cj(){var a=[],b=B?256:2048,c=function(a){var b=ch(a);return cf(b,0),b},d=c(b),e=function(){d&&(d.length>d.l&&((d=d.slice(0,d.l)).l=d.length),d.length>0&&a.push(d),d=null)},f=function(a){return d&&a<d.length-d.l?d:(e(),d=c(Math.max(a+1,b)))};return{next:f,push:function(a){e(),null==(d=a).l&&(d.l=d.length),f(b)},end:function(){return e(),J(a)},_bufs:a}}function ck(a,b,c,d){var e,f=+b;if(!isNaN(f)){d||(d=gv[f].p||(c||[]).length||0),e=1+ +(f>=128)+1,d>=128&&++e,d>=16384&&++e,d>=2097152&&++e;var g=a.next(e);f<=127?g.write_shift(1,f):(g.write_shift(1,(127&f)+128),g.write_shift(1,f>>7));for(var h=0;4!=h;++h)if(d>=128)g.write_shift(1,(127&d)+128),d>>=7;else{g.write_shift(1,d);break}d>0&&b3(c)&&a.push(c)}}function cl(a,b,c){var d=aR(a);if(b.s?(d.cRel&&(d.c+=b.s.c),d.rRel&&(d.r+=b.s.r)):(d.cRel&&(d.c+=b.c),d.rRel&&(d.r+=b.r)),!c||c.biff<12){for(;d.c>=256;)d.c-=256;for(;d.r>=65536;)d.r-=65536}return d}function cm(a,b,c){var d=aR(a);return d.s=cl(d.s,b.s,c),d.e=cl(d.e,b.s,c),d}function cn(a,b){if(a.cRel&&a.c<0)for(a=aR(a);a.c<0;)a.c+=b>8?16384:256;if(a.rRel&&a.r<0)for(a=aR(a);a.r<0;)a.r+=b>8?1048576:b>5?65536:16384;var c=cu(a);return a.cRel||null==a.cRel||(c=c.replace(/^([A-Z])/,"$$$1")),a.rRel||null==a.rRel||(c=c.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")),c}function co(a,b){return 0!=a.s.r||a.s.rRel||a.e.r!=(b.biff>=12?1048575:b.biff>=8?65536:16384)||a.e.rRel?0!=a.s.c||a.s.cRel||a.e.c!=(b.biff>=12?16383:255)||a.e.cRel?cn(a.s,b.biff)+":"+cn(a.e,b.biff):(a.s.rRel?"":"$")+cq(a.s.r)+":"+(a.e.rRel?"":"$")+cq(a.e.r):(a.s.cRel?"":"$")+cs(a.s.c)+":"+(a.e.cRel?"":"$")+cs(a.e.c)}function cp(a){return parseInt(a.replace(/\$(\d+)$/,"$1"),10)-1}function cq(a){return""+(a+1)}function cr(a){for(var b=a.replace(/^\$([A-Z])/,"$1"),c=0,d=0;d!==b.length;++d)c=26*c+b.charCodeAt(d)-64;return c-1}function cs(a){if(a<0)throw Error("invalid column "+a);var b="";for(++a;a;a=Math.floor((a-1)/26))b=String.fromCharCode((a-1)%26+65)+b;return b}function ct(a){for(var b=0,c=0,d=0;d<a.length;++d){var e=a.charCodeAt(d);e>=48&&e<=57?b=10*b+(e-48):e>=65&&e<=90&&(c=26*c+(e-64))}return{c:c-1,r:b-1}}function cu(a){for(var b=a.c+1,c="";b;b=(b-1)/26|0)c=String.fromCharCode((b-1)%26+65)+c;return c+(a.r+1)}function cv(a){var b=a.indexOf(":");return -1==b?{s:ct(a),e:ct(a)}:{s:ct(a.slice(0,b)),e:ct(a.slice(b+1))}}function cw(a,b){return void 0===b||"number"==typeof b?cw(a.s,a.e):("string"!=typeof a&&(a=cu(a)),"string"!=typeof b&&(b=cu(b)),a==b?a:a+":"+b)}function cx(a){var b={s:{c:0,r:0},e:{c:0,r:0}},c=0,d=0,e=0,f=a.length;for(c=0;d<f&&!((e=a.charCodeAt(d)-64)<1)&&!(e>26);++d)c=26*c+e;for(b.s.c=--c,c=0;d<f&&!((e=a.charCodeAt(d)-48)<0)&&!(e>9);++d)c=10*c+e;if(b.s.r=--c,d===f||10!=e)return b.e.c=b.s.c,b.e.r=b.s.r,b;for(++d,c=0;d!=f&&!((e=a.charCodeAt(d)-64)<1)&&!(e>26);++d)c=26*c+e;for(b.e.c=--c,c=0;d!=f&&!((e=a.charCodeAt(d)-48)<0)&&!(e>9);++d)c=10*c+e;return b.e.r=--c,b}function cy(a,b){var c="d"==a.t&&b instanceof Date;if(null!=a.z)try{return a.w=at(a.z,c?aH(b):b)}catch(a){}try{return a.w=at((a.XF||{}).numFmtId||14*!!c,c?aH(b):b)}catch(a){return""+b}}function cz(a,b,c){return null==a||null==a.t||"z"==a.t?"":void 0!==a.w?a.w:("d"==a.t&&!a.z&&c&&c.dateNF&&(a.z=c.dateNF),"e"==a.t)?c$[a.v]||a.v:void 0==b?cy(a,a.v):cy(a,b)}function cA(a,b){var c=b&&b.sheet?b.sheet:"Sheet1",d={};return d[c]=a,{SheetNames:[c],Sheets:d}}function cB(a,b){return function(a,b,c){var d=c||{},e=(0,d.dense),f=a||(e?[]:{}),g=0,h=0;if(f&&null!=d.origin){if("number"==typeof d.origin)g=d.origin;else{var i="string"==typeof d.origin?ct(d.origin):d.origin;g=i.r,h=i.c}f["!ref"]||(f["!ref"]="A1:A1")}var j={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(f["!ref"]){var k=cx(f["!ref"]);j.s.c=k.s.c,j.s.r=k.s.r,j.e.c=Math.max(j.e.c,k.e.c),j.e.r=Math.max(j.e.r,k.e.r),-1==g&&(j.e.r=g=k.e.r+1)}for(var l=0;l!=b.length;++l)if(b[l]){if(!Array.isArray(b[l]))throw Error("aoa_to_sheet expects an array of arrays");for(var m=0;m!=b[l].length;++m)if(void 0!==b[l][m]){var n={v:b[l][m]},o=g+l,p=h+m;if(j.s.r>o&&(j.s.r=o),j.s.c>p&&(j.s.c=p),j.e.r<o&&(j.e.r=o),j.e.c<p&&(j.e.c=p),!b[l][m]||"object"!=typeof b[l][m]||Array.isArray(b[l][m])||b[l][m]instanceof Date)if(Array.isArray(n.v)&&(n.f=b[l][m][1],n.v=n.v[0]),null===n.v)if(n.f)n.t="n";else if(d.nullError)n.t="e",n.v=0;else{if(!d.sheetStubs)continue;n.t="z"}else"number"==typeof n.v?n.t="n":"boolean"==typeof n.v?n.t="b":n.v instanceof Date?(n.z=d.dateNF||U[14],d.cellDates?(n.t="d",n.w=at(n.z,aH(n.v))):(n.t="n",n.v=aH(n.v),n.w=at(n.z,n.v))):n.t="s";else n=b[l][m];if(e)f[o]||(f[o]=[]),f[o][p]&&f[o][p].z&&(n.z=f[o][p].z),f[o][p]=n;else{var q=cu({c:p,r:o});f[q]&&f[q].z&&(n.z=f[q].z),f[q]=n}}}return j.s.c<1e7&&(f["!ref"]=cw(j)),f}(null,a,b)}function cC(a,b){return b||(b=ch(4)),b.write_shift(4,a),b}function cD(a){var b=a.read_shift(4);return 0===b?"":a.read_shift(b,"dbcs")}function cE(a,b){var c=!1;return null==b&&(c=!0,b=ch(4+2*a.length)),b.write_shift(4,a.length),a.length>0&&b.write_shift(0,a,"dbcs"),c?b.slice(0,b.l):b}function cF(a,b){var c=a.l,d=a.read_shift(1),e=cD(a),f=[],g={t:e,h:e};if((1&d)!=0){for(var h=a.read_shift(4),i=0;i!=h;++i)f.push({ich:a.read_shift(2),ifnt:a.read_shift(2)});g.r=f}else g.r=[{ich:0,ifnt:0}];return a.l=c+b,g}function cG(a){var b=a.read_shift(4),c=a.read_shift(2);return c+=a.read_shift(1)<<16,a.l++,{c:b,iStyleRef:c}}function cH(a,b){return null==b&&(b=ch(8)),b.write_shift(-4,a.c),b.write_shift(3,a.iStyleRef||a.s),b.write_shift(1,0),b}function cI(a){var b=a.read_shift(2);return b+=a.read_shift(1)<<16,a.l++,{c:-1,iStyleRef:b}}function cJ(a,b){return null==b&&(b=ch(4)),b.write_shift(3,a.iStyleRef||a.s),b.write_shift(1,0),b}function cK(a){var b=a.read_shift(4);return 0===b||0xffffffff===b?"":a.read_shift(b,"dbcs")}function cL(a,b){var c=!1;return null==b&&(c=!0,b=ch(127)),b.write_shift(4,a.length>0?a.length:0xffffffff),a.length>0&&b.write_shift(0,a,"dbcs"),c?b.slice(0,b.l):b}function cM(a){var b=a.slice(a.l,a.l+4),c=1&b[0],d=2&b[0];a.l+=4;var e=0===d?b2([0,0,0,0,252&b[0],b[1],b[2],b[3]],0):b8(b,0)>>2;return c?e/100:e}function cN(a,b){null==b&&(b=ch(4));var c=0,d=0,e=100*a;if(a==(0|a)&&a>=-0x20000000&&a<0x20000000?d=1:e==(0|e)&&e>=-0x20000000&&e<0x20000000&&(d=1,c=1),d)b.write_shift(-4,((c?e:a)<<2)+(c+2));else throw Error("unsupported RkNumber "+a)}function cO(a){var b={s:{},e:{}};return b.s.r=a.read_shift(4),b.e.r=a.read_shift(4),b.s.c=a.read_shift(4),b.e.c=a.read_shift(4),b}var cP=function(a,b){return b||(b=ch(16)),b.write_shift(4,a.s.r),b.write_shift(4,a.e.r),b.write_shift(4,a.s.c),b.write_shift(4,a.e.c),b};function cQ(a){if(a.length-a.l<8)throw"XLS Xnum Buffer underflow";return a.read_shift(8,"f")}function cR(a,b){return(b||ch(8)).write_shift(8,a,"f")}function cS(a,b){if(b||(b=ch(8)),!a||a.auto)return b.write_shift(4,0),b.write_shift(4,0),b;null!=a.index?(b.write_shift(1,2),b.write_shift(1,a.index)):null!=a.theme?(b.write_shift(1,6),b.write_shift(1,a.theme)):(b.write_shift(1,5),b.write_shift(1,0));var c=a.tint||0;if(c>0?c*=32767:c<0&&(c*=32768),b.write_shift(2,c),a.rgb&&null==a.theme){var d=a.rgb||"FFFFFF";"number"==typeof d&&(d=("000000"+d.toString(16)).slice(-6)),b.write_shift(1,parseInt(d.slice(0,2),16)),b.write_shift(1,parseInt(d.slice(2,4),16)),b.write_shift(1,parseInt(d.slice(4,6),16)),b.write_shift(1,255)}else b.write_shift(2,0),b.write_shift(1,0),b.write_shift(1,0);return b}function cT(a,b){var c=a.read_shift(4);switch(c){case 0:return"";case 0xffffffff:case 0xfffffffe:return({2:"BITMAP",3:"METAFILEPICT",8:"DIB",14:"ENHMETAFILE"})[a.read_shift(4)]||""}if(c>400)throw Error("Unsupported Clipboard: "+c.toString(16));return a.l-=4,a.read_shift(0,1==b?"lpstr":"lpwstr")}var cU=[80,81],cV={1:{n:"CodePage",t:2},2:{n:"Category",t:80},3:{n:"PresentationFormat",t:80},4:{n:"ByteCount",t:3},5:{n:"LineCount",t:3},6:{n:"ParagraphCount",t:3},7:{n:"SlideCount",t:3},8:{n:"NoteCount",t:3},9:{n:"HiddenCount",t:3},10:{n:"MultimediaClipCount",t:3},11:{n:"ScaleCrop",t:11},12:{n:"HeadingPairs",t:4108},13:{n:"TitlesOfParts",t:4126},14:{n:"Manager",t:80},15:{n:"Company",t:80},16:{n:"LinksUpToDate",t:11},17:{n:"CharacterCount",t:3},19:{n:"SharedDoc",t:11},22:{n:"HyperlinksChanged",t:11},23:{n:"AppVersion",t:3,p:"version"},24:{n:"DigSig",t:65},26:{n:"ContentType",t:80},27:{n:"ContentStatus",t:80},28:{n:"Language",t:80},29:{n:"Version",t:80},255:{},0x80000000:{n:"Locale",t:19},0x80000003:{n:"Behavior",t:19},0x72627262:{}},cW={1:{n:"CodePage",t:2},2:{n:"Title",t:80},3:{n:"Subject",t:80},4:{n:"Author",t:80},5:{n:"Keywords",t:80},6:{n:"Comments",t:80},7:{n:"Template",t:80},8:{n:"LastAuthor",t:80},9:{n:"RevNumber",t:80},10:{n:"EditTime",t:64},11:{n:"LastPrinted",t:64},12:{n:"CreatedDate",t:64},13:{n:"ModifiedDate",t:64},14:{n:"PageCount",t:3},15:{n:"WordCount",t:3},16:{n:"CharCount",t:3},17:{n:"Thumbnail",t:71},18:{n:"Application",t:80},19:{n:"DocSecurity",t:3},255:{},0x80000000:{n:"Locale",t:19},0x80000003:{n:"Behavior",t:19},0x72627262:{}},cX={1:"US",2:"CA",3:"",7:"RU",20:"EG",30:"GR",31:"NL",32:"BE",33:"FR",34:"ES",36:"HU",39:"IT",41:"CH",43:"AT",44:"GB",45:"DK",46:"SE",47:"NO",48:"PL",49:"DE",52:"MX",55:"BR",61:"AU",64:"NZ",66:"TH",81:"JP",82:"KR",84:"VN",86:"CN",90:"TR",105:"JS",213:"DZ",216:"MA",218:"LY",351:"PT",354:"IS",358:"FI",420:"CZ",886:"TW",961:"LB",962:"JO",963:"SY",964:"IQ",965:"KW",966:"SA",971:"AE",972:"IL",974:"QA",981:"IR",65535:"US"},cY=[null,"solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"],cZ=aR([0,0xffffff,0xff0000,65280,255,0xffff00,0xff00ff,65535,0,0xffffff,0xff0000,65280,255,0xffff00,0xff00ff,65535,8388608,32768,128,8421376,8388736,32896,0xc0c0c0,8421504,0x9999ff,0x993366,0xffffcc,0xccffff,6684774,0xff8080,26316,0xccccff,128,0xff00ff,0xffff00,65535,8388736,8388608,32896,255,52479,0xccffff,0xccffcc,0xffff99,0x99ccff,0xff99cc,0xcc99ff,0xffcc99,3368703,3394764,0x99cc00,0xffcc00,0xff9900,0xff6600,6710937,9868950,13158,3381606,13056,3355392,0x993300,0x993366,3355545,3355443,0xffffff,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0].map(function(a){return[a>>16&255,a>>8&255,255&a]})),c$={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"},c_={"#NULL!":0,"#DIV/0!":7,"#VALUE!":15,"#REF!":23,"#NAME?":29,"#NUM!":36,"#N/A":42,"#GETTING_DATA":43,"#WTF?":255},c0={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.sheet.macroEnabled.main+xml":"workbooks","application/vnd.ms-excel.sheet.binary.macroEnabled.main":"workbooks","application/vnd.ms-excel.addin.macroEnabled.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":"sheets","application/vnd.ms-excel.worksheet":"sheets","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":"charts","application/vnd.ms-excel.chartsheet":"charts","application/vnd.ms-excel.macrosheet+xml":"macros","application/vnd.ms-excel.macrosheet":"macros","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":"dialogs","application/vnd.ms-excel.dialogsheet":"dialogs","application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml":"strs","application/vnd.ms-excel.sharedStrings":"strs","application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":"styles","application/vnd.ms-excel.styles":"styles","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":"comments","application/vnd.ms-excel.comments":"comments","application/vnd.ms-excel.threadedcomments+xml":"threadedcomments","application/vnd.ms-excel.person+xml":"people","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"metadata","application/vnd.ms-excel.sheetMetadata":"metadata","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"TODO","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"},c1={workbooks:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml",xlsm:"application/vnd.ms-excel.sheet.macroEnabled.main+xml",xlsb:"application/vnd.ms-excel.sheet.binary.macroEnabled.main",xlam:"application/vnd.ms-excel.addin.macroEnabled.main+xml",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml"},strs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml",xlsb:"application/vnd.ms-excel.sharedStrings"},comments:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml",xlsb:"application/vnd.ms-excel.comments"},sheets:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml",xlsb:"application/vnd.ms-excel.worksheet"},charts:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml",xlsb:"application/vnd.ms-excel.chartsheet"},dialogs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml",xlsb:"application/vnd.ms-excel.dialogsheet"},macros:{xlsx:"application/vnd.ms-excel.macrosheet+xml",xlsb:"application/vnd.ms-excel.macrosheet"},metadata:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml",xlsb:"application/vnd.ms-excel.sheetMetadata"},styles:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml",xlsb:"application/vnd.ms-excel.styles"}};function c2(){return{workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""}}function c3(a,b){var c,d=function(a){for(var b=[],c=aC(a),d=0;d!==c.length;++d)null==b[a[c[d]]]&&(b[a[c[d]]]=[]),b[a[c[d]]].push(c[d]);return b}(c0),e=[];e[e.length]=a6,e[e.length]=bE("Types",null,{xmlns:bI.CT,"xmlns:xsd":bI.xsd,"xmlns:xsi":bI.xsi}),e=e.concat([["xml","application/xml"],["bin","application/vnd.ms-excel.sheet.binary.macroEnabled.main"],["vml","application/vnd.openxmlformats-officedocument.vmlDrawing"],["data","application/vnd.openxmlformats-officedocument.model+data"],["bmp","image/bmp"],["png","image/png"],["gif","image/gif"],["emf","image/x-emf"],["wmf","image/x-wmf"],["jpg","image/jpeg"],["jpeg","image/jpeg"],["tif","image/tiff"],["tiff","image/tiff"],["pdf","application/pdf"],["rels","application/vnd.openxmlformats-package.relationships+xml"]].map(function(a){return bE("Default",null,{Extension:a[0],ContentType:a[1]})}));var f=function(d){a[d]&&a[d].length>0&&(c=a[d][0],e[e.length]=bE("Override",null,{PartName:("/"==c[0]?"":"/")+c,ContentType:c1[d][b.bookType]||c1[d].xlsx}))},g=function(c){(a[c]||[]).forEach(function(a){e[e.length]=bE("Override",null,{PartName:("/"==a[0]?"":"/")+a,ContentType:c1[c][b.bookType]||c1[c].xlsx})})},h=function(b){(a[b]||[]).forEach(function(a){e[e.length]=bE("Override",null,{PartName:("/"==a[0]?"":"/")+a,ContentType:d[b][0]})})};return f("workbooks"),g("sheets"),g("charts"),h("themes"),["strs","styles"].forEach(f),["coreprops","extprops","custprops"].forEach(h),h("vba"),h("comments"),h("threadedcomments"),h("drawings"),g("metadata"),h("people"),e.length>2&&(e[e.length]="</Types>",e[1]=e[1].replace("/>",">")),e.join("")}var c4={WB:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",HLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",VML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing",XPATH:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLinkPath",XMISS:"http://schemas.microsoft.com/office/2006/relationships/xlExternalLinkPath/xlPathMissing",CMNT:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties",SST:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings",STY:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",THEME:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",CS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chartsheet",WS:["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"],DS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/dialogsheet",MS:"http://schemas.microsoft.com/office/2006/relationships/xlMacrosheet",DRAW:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing",XLMETA:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sheetMetadata",TCMNT:"http://schemas.microsoft.com/office/2017/10/relationships/threadedComment",PEOPLE:"http://schemas.microsoft.com/office/2017/10/relationships/person",VBA:"http://schemas.microsoft.com/office/2006/relationships/vbaProject"};function c5(a){var b=a.lastIndexOf("/");return a.slice(0,b+1)+"_rels/"+a.slice(b+1)+".rels"}function c6(a,b){var c={"!id":{}};if(!a)return c;"/"!==b.charAt(0)&&(b="/"+b);var d={};return(a.match(a9)||[]).forEach(function(a){var e=bc(a);if("<Relationship"===e[0]){var f={};f.Type=e.Type,f.Target=e.Target,f.Id=e.Id,e.TargetMode&&(f.TargetMode=e.TargetMode),c["External"===e.TargetMode?e.Target:a5(e.Target,b)]=f,d[e.Id]=f}}),c["!id"]=d,c}function c7(a){var b=[a6,bE("Relationships",null,{xmlns:bI.RELS})];return aC(a["!id"]).forEach(function(c){b[b.length]=bE("Relationship",null,a["!id"][c])}),b.length>2&&(b[b.length]="</Relationships>",b[1]=b[1].replace("/>",">")),b.join("")}function c8(a,b,c,d,e,f){if(e||(e={}),a["!id"]||(a["!id"]={}),a["!idx"]||(a["!idx"]=1),b<0)for(b=a["!idx"];a["!id"]["rId"+b];++b);if(a["!idx"]=b+1,e.Id="rId"+b,e.Type=d,e.Target=c,f?e.TargetMode=f:[c4.HLINK,c4.XPATH,c4.XMISS].indexOf(e.Type)>-1&&(e.TargetMode="External"),a["!id"][e.Id])throw Error("Cannot rewrite rId "+b);return a["!id"][e.Id]=e,a[("/"+e.Target).replace("//","/")]=e,b}function c9(a,b,c){return'  <rdf:Description rdf:about="'+a+'">\n'+('    <rdf:type rdf:resource="http://docs.oasis-open.org/ns/office/1.2/meta/'+(c||"odf")+"#")+b+'"/>\n  </rdf:Description>\n'}function da(){return'<office:document-meta xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:xlink="http://www.w3.org/1999/xlink" office:version="1.2"><office:meta><meta:generator>SheetJS '+l.version+"</meta:generator></office:meta></office:document-meta>"}var db=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]],dc=function(){for(var a=Array(db.length),b=0;b<db.length;++b){var c=db[b],d="(?:"+c[0].slice(0,c[0].indexOf(":"))+":)"+c[0].slice(c[0].indexOf(":")+1);a[b]=RegExp("<"+d+"[^>]*>([\\s\\S]*?)</"+d+">")}return a}();function dd(a){var b={};a=bt(a);for(var c=0;c<db.length;++c){var d=db[c],e=a.match(dc[c]);null!=e&&e.length>0&&(b[d[1]]=bg(e[1])),"date"===d[2]&&b[d[1]]&&(b[d[1]]=aP(b[d[1]]))}return b}function de(a,b,c,d,e){null==e[a]&&null!=b&&""!==b&&(e[a]=b,b=bj(b),d[d.length]=c?bE(a,b,c):bC(a,b))}function df(a,b){var c=b||{},d=[a6,bE("cp:coreProperties",null,{"xmlns:cp":bI.CORE_PROPS,"xmlns:dc":bI.dc,"xmlns:dcterms":bI.dcterms,"xmlns:dcmitype":bI.dcmitype,"xmlns:xsi":bI.xsi})],e={};if(!a&&!c.Props)return d.join("");a&&(null!=a.CreatedDate&&de("dcterms:created","string"==typeof a.CreatedDate?a.CreatedDate:bF(a.CreatedDate,c.WTF),{"xsi:type":"dcterms:W3CDTF"},d,e),null!=a.ModifiedDate&&de("dcterms:modified","string"==typeof a.ModifiedDate?a.ModifiedDate:bF(a.ModifiedDate,c.WTF),{"xsi:type":"dcterms:W3CDTF"},d,e));for(var f=0;f!=db.length;++f){var g=db[f],h=c.Props&&null!=c.Props[g[1]]?c.Props[g[1]]:a?a[g[1]]:null;!0===h?h="1":!1===h?h="0":"number"==typeof h&&(h=String(h)),null!=h&&de(g[0],h,null,d,e)}return d.length>2&&(d[d.length]="</cp:coreProperties>",d[1]=d[1].replace("/>",">")),d.join("")}var dg=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]],dh=["Worksheets","SheetNames","NamedRanges","DefinedNames","Chartsheets","ChartNames"];function di(a,b,c,d){var e=[];if("string"==typeof a)e=bA(a,d);else for(var f=0;f<a.length;++f)e=e.concat(a[f].map(function(a){return{v:a}}));var g="string"==typeof b?bA(b,d).map(function(a){return a.v}):b,h=0,i=0;if(g.length>0)for(var j=0;j!==e.length;j+=2){switch(i=+e[j+1].v,e[j].v){case"Worksheets":case"工作表":case"Листы":case"أوراق العمل":case"ワークシート":case"גליונות עבודה":case"Arbeitsbl\xe4tter":case"\xc7alışma Sayfaları":case"Feuilles de calcul":case"Fogli di lavoro":case"Folhas de c\xe1lculo":case"Planilhas":case"Regneark":case"Hojas de c\xe1lculo":case"Werkbladen":c.Worksheets=i,c.SheetNames=g.slice(h,h+i);break;case"Named Ranges":case"Rangos con nombre":case"名前付き一覧":case"Benannte Bereiche":case"Navngivne omr\xe5der":c.NamedRanges=i,c.DefinedNames=g.slice(h,h+i);break;case"Charts":case"Diagramme":c.Chartsheets=i,c.ChartNames=g.slice(h,h+i)}h+=i}}function dj(a){var b=[];return a||(a={}),a.Application="SheetJS",b[b.length]=a6,b[b.length]=bE("Properties",null,{xmlns:bI.EXT_PROPS,"xmlns:vt":bI.vt}),dg.forEach(function(c){var d;if(void 0!==a[c[1]]){switch(c[2]){case"string":d=bj(String(a[c[1]]));break;case"bool":d=a[c[1]]?"true":"false"}void 0!==d&&(b[b.length]=bE(c[0],d))}}),b[b.length]=bE("HeadingPairs",bE("vt:vector",bE("vt:variant","<vt:lpstr>Worksheets</vt:lpstr>")+bE("vt:variant",bE("vt:i4",String(a.Worksheets))),{size:2,baseType:"variant"})),b[b.length]=bE("TitlesOfParts",bE("vt:vector",a.SheetNames.map(function(a){return"<vt:lpstr>"+bj(a)+"</vt:lpstr>"}).join(""),{size:a.Worksheets,baseType:"lpstr"})),b.length>2&&(b[b.length]="</Properties>",b[1]=b[1].replace("/>",">")),b.join("")}var dk=/<[^>]+>[^<]*/g;function dl(a){var b=[a6,bE("Properties",null,{xmlns:bI.CUST_PROPS,"xmlns:vt":bI.vt})];if(!a)return b.join("");var c=1;return aC(a).forEach(function(d){++c,b[b.length]=bE("property",function(a,b){switch(typeof a){case"string":var c=bE("vt:lpwstr",bj(a));return b&&(c=c.replace(/&quot;/g,"_x0022_")),c;case"number":return bE((0|a)==a?"vt:i4":"vt:r8",bj(String(a)));case"boolean":return bE("vt:bool",a?"true":"false")}if(a instanceof Date)return bE("vt:filetime",bF(a));throw Error("Unable to serialize "+a)}(a[d],!0),{fmtid:"{D5CDD505-2E9C-101B-9397-08002B2CF9AE}",pid:c,name:bj(d)})}),b.length>2&&(b[b.length]="</Properties>",b[1]=b[1].replace("/>",">")),b.join("")}var dm={Title:"Title",Subject:"Subject",Author:"Author",Keywords:"Keywords",Comments:"Description",LastAuthor:"LastAuthor",RevNumber:"Revision",Application:"AppName",LastPrinted:"LastPrinted",CreatedDate:"Created",ModifiedDate:"LastSaved",Category:"Category",Manager:"Manager",Company:"Company",AppVersion:"Version",ContentStatus:"ContentStatus",Identifier:"Identifier",Language:"Language"};function dn(a){var b=a.read_shift(4);return new Date((a.read_shift(4)/1e7*0x100000000+b/1e7-0x2b6109100)*1e3).toISOString().replace(/\.000/,"")}function dp(a,b,c){var d=a.l,e=a.read_shift(0,"lpstr-cp");if(c)for(;a.l-d&3;)++a.l;return e}function dq(a,b,c){var d=a.read_shift(0,"lpwstr");return c&&(a.l+=4-(d.length+1&3)&3),d}function dr(a,b,c){return 31===b?dq(a):dp(a,b,c)}function ds(a,b,c){return dr(a,b,4*(!1!==c))}function dt(a,b){for(var c=a.read_shift(4),d={},e=0;e!=c;++e){var f=a.read_shift(4),g=a.read_shift(4);d[f]=a.read_shift(g,1200===b?"utf16le":"utf8").replace(K,"").replace(L,"!"),1200===b&&g%2&&(a.l+=2)}return 3&a.l&&(a.l=a.l>>3<<2),d}function du(a){var b=a.read_shift(4),c=a.slice(a.l,a.l+b);return a.l+=b,(3&b)>0&&(a.l+=4-(3&b)&3),c}function dv(a,b,c){var d,e,f=a.read_shift(2),g=c||{};if(a.l+=2,12!==b&&f!==b&&-1===cU.indexOf(b)&&((65534&b)!=4126||(65534&f)!=4126))throw Error("Expected type "+b+" saw "+f);switch(12===b?f:b){case 2:return e=a.read_shift(2,"i"),g.raw||(a.l+=2),e;case 3:return a.read_shift(4,"i");case 11:return 0!==a.read_shift(4);case 19:return a.read_shift(4);case 30:return dp(a,f,4).replace(K,"");case 31:return dq(a);case 64:return dn(a);case 65:return du(a);case 71:return(d={}).Size=a.read_shift(4),a.l+=d.Size+3-(d.Size-1)%4,d;case 80:return ds(a,f,!g.raw).replace(K,"");case 81:return(function(a,b){if(!b)throw Error("VtUnalignedString must have positive length");return dr(a,b,0)})(a,f).replace(K,"");case 4108:for(var h=a.read_shift(4),i=[],j=0;j<h/2;++j)i.push(function(a){var b=a.l,c=dv(a,81);return 0==a[a.l]&&0==a[a.l+1]&&a.l-b&2&&(a.l+=2),[c,dv(a,3)]}(a));return i;case 4126:case 4127:return 4127==f?function(a){for(var b=a.read_shift(4),c=[],d=0;d!=b;++d){var e=a.l;c[d]=a.read_shift(0,"lpwstr").replace(K,""),a.l-e&2&&(a.l+=2)}return c}(a):function(a){for(var b=a.read_shift(4),c=[],d=0;d!=b;++d)c[d]=a.read_shift(0,"lpstr-cp").replace(K,"");return c}(a);default:throw Error("TypedPropertyValue unrecognized type "+b+" "+f)}}function dw(a,b){var c,d,e,f,g,h=ch(4),i=ch(4);switch(h.write_shift(4,80==a?31:a),a){case 3:i.write_shift(-4,b);break;case 5:(i=ch(8)).write_shift(8,b,"f");break;case 11:i.write_shift(4,+!!b);break;case 64:d=(c=("string"==typeof b?new Date(Date.parse(b)):b).getTime()/1e3+0x2b6109100)%0x100000000,e=(c-d)/0x100000000*1e7,(f=(d*=1e7)/0x100000000|0)>0&&(d%=0x100000000,e+=f),(g=ch(8)).write_shift(4,d),g.write_shift(4,e),i=g;break;case 31:case 80:for((i=ch(4+2*(b.length+1)+(b.length%2?0:2))).write_shift(4,b.length+1),i.write_shift(0,b,"dbcs");i.l!=i.length;)i.write_shift(1,0);break;default:throw Error("TypedPropertyValue unrecognized type "+a+" "+b)}return J([h,i])}function dx(a,b){var c=a.l,d=a.read_shift(4),e=a.read_shift(4),f=[],g=0,h=0,i=-1,j={};for(g=0;g!=e;++g){var k=a.read_shift(4),l=a.read_shift(4);f[g]=[k,l+c]}f.sort(function(a,b){return a[1]-b[1]});var m={};for(g=0;g!=e;++g){if(a.l!==f[g][1]){var n=!0;if(g>0&&b)switch(b[f[g-1][0]].t){case 2:a.l+2===f[g][1]&&(a.l+=2,n=!1);break;case 80:case 4108:a.l<=f[g][1]&&(a.l=f[g][1],n=!1)}if((!b||0==g)&&a.l<=f[g][1]&&(n=!1,a.l=f[g][1]),n)throw Error("Read Error: Expected address "+f[g][1]+" at "+a.l+" :"+g)}if(b){var o=b[f[g][0]];if(m[o.n]=dv(a,o.t,{raw:!0}),"version"===o.p&&(m[o.n]=String(m[o.n]>>16)+"."+("0000"+String(65535&m[o.n])).slice(-4)),"CodePage"==o.n)switch(m[o.n]){case 0:m[o.n]=1252;case 874:case 932:case 936:case 949:case 950:case 1250:case 1251:case 1253:case 1254:case 1255:case 1256:case 1257:case 1258:case 1e4:case 1200:case 1201:case 1252:case 65e3:case -536:case 65001:case -535:r(h=m[o.n]>>>0&65535);break;default:throw Error("Unsupported CodePage: "+m[o.n])}}else if(1===f[g][0]){if(r(h=m.CodePage=dv(a,2)),-1!==i){var p=a.l;a.l=f[i][1],j=dt(a,h),a.l=p}}else if(0===f[g][0]){if(0===h){i=g,a.l=f[g+1][1];continue}j=dt(a,h)}else{var q,s=j[f[g][0]];switch(a[a.l]){case 65:a.l+=4,q=du(a);break;case 30:case 31:a.l+=4,q=ds(a,a[a.l-4]).replace(/\u0000+$/,"");break;case 3:a.l+=4,q=a.read_shift(4,"i");break;case 19:a.l+=4,q=a.read_shift(4);break;case 5:a.l+=4,q=a.read_shift(8,"f");break;case 11:a.l+=4,q=dD(a,4);break;case 64:a.l+=4,q=aP(dn(a));break;default:throw Error("unparsed value: "+a[a.l])}m[s]=q}}return a.l=c+d,m}var dy=["CodePage","Thumbnail","_PID_LINKBASE","_PID_HLINKS","SystemIdentifier","FMTID"];function dz(a,b,c){var d=ch(8),e=[],f=[],g=8,h=0,i=ch(8),j=ch(8);if(i.write_shift(4,2),i.write_shift(4,1200),j.write_shift(4,1),f.push(i),e.push(j),g+=8+i.length,!b){(j=ch(8)).write_shift(4,0),e.unshift(j);var k=[ch(4)];for(k[0].write_shift(4,a.length),h=0;h<a.length;++h){var l=a[h][0];for((i=ch(8+2*(l.length+1)+(l.length%2?0:2))).write_shift(4,h+2),i.write_shift(4,l.length+1),i.write_shift(0,l,"dbcs");i.l!=i.length;)i.write_shift(1,0);k.push(i)}i=J(k),f.unshift(i),g+=8+i.length}for(h=0;h<a.length;++h)if(!(b&&!b[a[h][0]]||dy.indexOf(a[h][0])>-1||dh.indexOf(a[h][0])>-1)&&null!=a[h][1]){var m=a[h][1],n=0;if(b){var o=c[n=+b[a[h][0]]];if("version"==o.p&&"string"==typeof m){var p=m.split(".");m=(p[0]<<16)+(+p[1]||0)}i=dw(o.t,m)}else{var q=function(a){switch(typeof a){case"boolean":return 11;case"number":return(0|a)==a?3:5;case"string":return 31;case"object":if(a instanceof Date)return 64}return -1}(m);-1==q&&(q=31,m=String(m)),i=dw(q,m)}f.push(i),(j=ch(8)).write_shift(4,b?n:2+h),e.push(j),g+=8+i.length}var r=8*(f.length+1);for(h=0;h<f.length;++h)e[h].write_shift(4,r),r+=f[h].length;return d.write_shift(4,g),d.write_shift(4,f.length),J([d].concat(e).concat(f))}function dA(a,b,c){var d,e=a.content;if(!e)return{};cf(e,0);var f,g,h,i,j=0;e.chk("feff","Byte Order: "),e.read_shift(2);var k=e.read_shift(4),l=e.read_shift(16);if(l!==aA.utils.consts.HEADER_CLSID&&l!==c)throw Error("Bad PropertySet CLSID "+l);if(1!==(f=e.read_shift(4))&&2!==f)throw Error("Unrecognized #Sets: "+f);if(g=e.read_shift(16),i=e.read_shift(4),1===f&&i!==e.l)throw Error("Length mismatch: "+i+" !== "+e.l);2===f&&(h=e.read_shift(16),j=e.read_shift(4));var m=dx(e,b),n={SystemIdentifier:k};for(var o in m)n[o]=m[o];if(n.FMTID=g,1===f)return n;if(j-e.l==2&&(e.l+=2),e.l!==j)throw Error("Length mismatch 2: "+e.l+" !== "+j);try{d=dx(e,null)}catch(a){}for(o in d)n[o]=d[o];return n.FMTID=[g,h],n}function dB(a,b,c,d,e,f){var g=ch(e?68:48),h=[g];g.write_shift(2,65534),g.write_shift(2,0),g.write_shift(4,0x32363237),g.write_shift(16,aA.utils.consts.HEADER_CLSID,"hex"),g.write_shift(4,e?2:1),g.write_shift(16,b,"hex"),g.write_shift(4,e?68:48);var i=dz(a,c,d);if(h.push(i),e){var j=dz(e,null,null);g.write_shift(16,f,"hex"),g.write_shift(4,68+i.length),h.push(j)}return J(h)}function dC(a,b){return a.read_shift(b),null}function dD(a,b){return 1===a.read_shift(b)}function dE(a,b){return b||(b=ch(2)),b.write_shift(2,+!!a),b}function dF(a){return a.read_shift(2,"u")}function dG(a,b){return b||(b=ch(2)),b.write_shift(2,a),b}function dH(a,b){for(var c=[],d=a.l+b;a.l<d;)c.push(dF(a,d-a.l));if(d!==a.l)throw Error("Slurp error");return c}function dI(a,b,c){return c||(c=ch(2)),c.write_shift(1,"e"==b?+a:+!!a),c.write_shift(1,+("e"==b)),c}function dJ(a,b,c){var d=a.read_shift(c&&c.biff>=12?2:1),e="sbcs-cont",f=m;c&&c.biff>=8&&(m=1200),c&&8!=c.biff?12==c.biff&&(e="wstr"):a.read_shift(1)&&(e="dbcs-cont"),c.biff>=2&&c.biff<=5&&(e="cpstr");var g=d?a.read_shift(d,e):"";return m=f,g}function dK(a,b,c){if(c){if(c.biff>=2&&c.biff<=5)return a.read_shift(b,"cpstr");if(c.biff>=12)return a.read_shift(b,"dbcs-cont")}return 0===a.read_shift(1)?a.read_shift(b,"sbcs-cont"):a.read_shift(b,"dbcs-cont")}function dL(a,b,c){var d=a.read_shift(c&&2==c.biff?1:2);return 0===d?(a.l++,""):dK(a,d,c)}function dM(a,b,c){if(c.biff>5)return dL(a,b,c);var d=a.read_shift(1);return 0===d?(a.l++,""):a.read_shift(d,c.biff<=4||!a.lens?"cpstr":"sbcs-cont")}function dN(a,b,c){return c||(c=ch(3+2*a.length)),c.write_shift(2,a.length),c.write_shift(1,1),c.write_shift(31,a,"utf16le"),c}function dO(a){var b=a.read_shift(4);return b>0?a.read_shift(b,"utf16le").replace(K,""):""}function dP(a,b){b||(b=ch(6+2*a.length)),b.write_shift(4,1+a.length);for(var c=0;c<a.length;++c)b.write_shift(2,a.charCodeAt(c));return b.write_shift(2,0),b}function dQ(a){return[a.read_shift(1),a.read_shift(1),a.read_shift(1),a.read_shift(1)]}function dR(a,b){var c=dQ(a,b);return c[3]=0,c}function dS(a){return{r:a.read_shift(2),c:a.read_shift(2),ixfe:a.read_shift(2)}}function dT(a,b,c,d){return d||(d=ch(6)),d.write_shift(2,a),d.write_shift(2,b),d.write_shift(2,c||0),d}function dU(a){return[a.read_shift(2),cM(a)]}function dV(a){var b=a.read_shift(2),c=a.read_shift(2);return{s:{c:a.read_shift(2),r:b},e:{c:a.read_shift(2),r:c}}}function dW(a,b){return b||(b=ch(8)),b.write_shift(2,a.s.r),b.write_shift(2,a.e.r),b.write_shift(2,a.s.c),b.write_shift(2,a.e.c),b}function dX(a){var b=a.read_shift(2),c=a.read_shift(2);return{s:{c:a.read_shift(1),r:b},e:{c:a.read_shift(1),r:c}}}function dY(a){a.l+=4;var b=a.read_shift(2),c=a.read_shift(2),d=a.read_shift(2);return a.l+=12,[c,b,d]}function dZ(a){a.l+=2,a.l+=a.read_shift(2)}var d$={0:dZ,4:dZ,5:dZ,6:dZ,7:function(a){return a.l+=4,a.cf=a.read_shift(2),{}},8:dZ,9:dZ,10:dZ,11:dZ,12:dZ,13:function(a){var b={};return a.l+=4,a.l+=16,b.fSharedNote=a.read_shift(2),a.l+=4,b},14:dZ,15:dZ,16:dZ,17:dZ,18:dZ,19:dZ,20:dZ,21:dY};function d_(a,b){var c={BIFFVer:0,dt:0};switch(c.BIFFVer=a.read_shift(2),(b-=2)>=2&&(c.dt=a.read_shift(2),a.l-=2),c.BIFFVer){case 1536:case 1280:case 1024:case 768:case 512:case 2:case 7:break;default:if(b>6)throw Error("Unexpected BIFF Ver "+c.BIFFVer)}return a.read_shift(b),c}function d0(a,b,c){var d=1536,e=16;switch(c.bookType){case"biff8":case"xla":break;case"biff5":d=1280,e=8;break;case"biff4":d=4,e=6;break;case"biff3":d=3,e=6;break;case"biff2":d=2,e=4;break;default:throw Error("unsupported BIFF version")}var f=ch(e);return f.write_shift(2,d),f.write_shift(2,b),e>4&&f.write_shift(2,29282),e>6&&f.write_shift(2,1997),e>8&&(f.write_shift(2,49161),f.write_shift(2,1),f.write_shift(2,1798),f.write_shift(2,0)),f}function d1(a,b,c){var d=0;c&&2==c.biff||(d=a.read_shift(2));var e=a.read_shift(2);return c&&2==c.biff&&(d=1-(e>>15),e&=32767),[{Unsynced:1&d,DyZero:(2&d)>>1,ExAsc:(4&d)>>2,ExDsc:(8&d)>>3},e]}function d2(a,b,c){var d=a.l+b,e=8!=c.biff&&c.biff?2:4,f=a.read_shift(e),g=a.read_shift(e),h=a.read_shift(2),i=a.read_shift(2);return a.l=d,{s:{r:f,c:h},e:{r:g,c:i}}}function d3(a,b,c,d){var e=c&&5==c.biff;d||(d=ch(e?16:20)),d.write_shift(2,0),a.style?(d.write_shift(2,a.numFmtId||0),d.write_shift(2,65524)):(d.write_shift(2,a.numFmtId||0),d.write_shift(2,b<<4));var f=0;return a.numFmtId>0&&e&&(f|=1024),d.write_shift(4,f),d.write_shift(4,0),e||d.write_shift(4,0),d.write_shift(2,0),d}function d4(a,b,c){var d,e=dS(a,6);(2==c.biff||9==b)&&++a.l;var f=(d=a.read_shift(1),1===a.read_shift(1)?d:1===d);return e.val=f,e.t=!0===f||!1===f?"b":"e",e}var d5=function(a,b,c){return 0===b?"":dM(a,b,c)};function d6(a,b,c){var d,e=a.read_shift(2),f={fBuiltIn:1&e,fWantAdvise:e>>>1&1,fWantPict:e>>>2&1,fOle:e>>>3&1,fOleLink:e>>>4&1,cf:e>>>5&1023,fIcon:e>>>15&1};return 14849===c.sbcch&&(d=function(a,b,c){a.l+=4,b-=4;var d=a.l+b,e=dJ(a,b,c),f=a.read_shift(2);if(f!==(d-=a.l))throw Error("Malformed AddinUdf: padding = "+d+" != "+f);return a.l+=f,e}(a,b-2,c)),f.body=d||a.read_shift(b-2),"string"==typeof d&&(f.Name=d),f}var d7=["_xlnm.Consolidate_Area","_xlnm.Auto_Open","_xlnm.Auto_Close","_xlnm.Extract","_xlnm.Database","_xlnm.Criteria","_xlnm.Print_Area","_xlnm.Print_Titles","_xlnm.Recorder","_xlnm.Data_Form","_xlnm.Auto_Activate","_xlnm.Auto_Deactivate","_xlnm.Sheet_Title","_xlnm._FilterDatabase"];function d8(a,b,c){var d,e,f,g,h,i,j,k=a.l+b,l=a.read_shift(2),m=a.read_shift(1),n=a.read_shift(1),o=a.read_shift(c&&2==c.biff?1:2),p=0;(!c||c.biff>=5)&&(5!=c.biff&&(a.l+=2),p=a.read_shift(2),5==c.biff&&(a.l+=2),a.l+=4);var q=dK(a,n,c);32&l&&(q=d7[q.charCodeAt(0)]);var r=k-a.l;return c&&2==c.biff&&--r,{chKey:m,Name:q,itab:p,rgce:k!=a.l&&0!==o&&r>0?(d=a,e=r,f=c,g=o,i=d.l+e,j=fy(d,g,f),i!==d.l&&(h=fx(d,i-d.l,j,f)),[j,h]):[]}}function d9(a,b,c){if(c.biff<8){var d,e,f,g;return d=a,e=b,f=c,3==d[d.l+1]&&d[d.l]++,3==(g=dJ(d,e,f)).charCodeAt(0)?g.slice(1):g}for(var h=[],i=a.l+b,j=a.read_shift(c.biff>8?4:2);0!=j--;)h.push(function(a,b,c){var d=c.biff>8?4:2;return[a.read_shift(d),a.read_shift(d,"i"),a.read_shift(d,"i")]}(a,c.biff,c));if(a.l!=i)throw Error("Bad ExternSheet: "+a.l+" != "+i);return h}function ea(a,b,c){var d=dX(a,6);switch(c.biff){case 2:a.l++,b-=7;break;case 3:case 4:a.l+=2,b-=8;break;default:a.l+=6,b-=12}return[d,function(a,b,c){var d,e,f=a.l+b,g=2==c.biff?1:2,h=a.read_shift(g);if(65535==h)return[[],(d=b-2,void(a.l+=d))];var i=fy(a,h,c);return b!==h+g&&(e=fx(a,b-h-g,i,c)),a.l=f,[i,e]}(a,b,c,d)]}var eb={8:function(a,b){var c=a.l+b;a.l+=10;var d=a.read_shift(2);a.l+=4,a.l+=2,a.l+=2,a.l+=2,a.l+=4;var e=a.read_shift(1);return a.l+=e,a.l=c,{fmt:d}}};function ec(a,b,c){if(!c.cellStyles)return void(a.l+=b);var d=c&&c.biff>=12?4:2,e=a.read_shift(d),f=a.read_shift(d),g=a.read_shift(d),h=a.read_shift(d),i=a.read_shift(2);2==d&&(a.l+=2);var j={s:e,e:f,w:g,ixfe:h,flags:i};return(c.biff>=5||!c.biff)&&(j.level=i>>8&7),j}var ed=[2,3,48,49,131,139,140,245],ee=function(){var a={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969},b=aE({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});function c(b,c){var d=c||{};d.dateNF||(d.dateNF="yyyymmdd");var f=cB(function(b,c){var d=[],f=D(1);switch(c.type){case"base64":f=F(A(b));break;case"binary":f=F(b);break;case"buffer":case"array":f=b}cf(f,0);var g=f.read_shift(1),h=!!(136&g),i=!1,j=!1;switch(g){case 2:case 3:case 131:case 139:case 245:break;case 48:case 49:i=!0,h=!0;break;case 140:j=!0;break;default:throw Error("DBF Unsupported Version: "+g.toString(16))}var k=0,l=521;2==g&&(k=f.read_shift(2)),f.l+=3,2!=g&&(k=f.read_shift(4)),k>1048576&&(k=1e6),2!=g&&(l=f.read_shift(2));var m=f.read_shift(2),n=c.codepage||1252;2!=g&&(f.l+=16,f.read_shift(1),0!==f[f.l]&&(n=a[f[f.l]]),f.l+=1,f.l+=2),j&&(f.l+=36);for(var o=[],p={},q=Math.min(f.length,2==g?521:l-10-264*!!i),r=j?32:11;f.l<q&&13!=f[f.l];)switch((p={}).name=e.utils.decode(n,f.slice(f.l,f.l+r)).replace(/[\u0000\r\n].*$/g,""),f.l+=r,p.type=String.fromCharCode(f.read_shift(1)),2!=g&&!j&&(p.offset=f.read_shift(4)),p.len=f.read_shift(1),2==g&&(p.offset=f.read_shift(2)),p.dec=f.read_shift(1),p.name.length&&o.push(p),2!=g&&(f.l+=j?13:14),p.type){case"B":(!i||8!=p.len)&&c.WTF&&console.log("Skipping "+p.name+":"+p.type);break;case"G":case"P":c.WTF&&console.log("Skipping "+p.name+":"+p.type);break;case"+":case"0":case"@":case"C":case"D":case"F":case"I":case"L":case"M":case"N":case"O":case"T":case"Y":break;default:throw Error("Unknown Field Type: "+p.type)}if(13!==f[f.l]&&(f.l=l-1),13!==f.read_shift(1))throw Error("DBF Terminator not found "+f.l+" "+f[f.l]);f.l=l;var s=0,t=0;for(t=0,d[0]=[];t!=o.length;++t)d[0][t]=o[t].name;for(;k-- >0;){if(42===f[f.l]){f.l+=m;continue}for(++f.l,d[++s]=[],t=0,t=0;t!=o.length;++t){var u=f.slice(f.l,f.l+o[t].len);f.l+=o[t].len,cf(u,0);var v=e.utils.decode(n,u);switch(o[t].type){case"C":v.trim().length&&(d[s][t]=v.replace(/\s+$/,""));break;case"D":8===v.length?d[s][t]=new Date(+v.slice(0,4),v.slice(4,6)-1,+v.slice(6,8)):d[s][t]=v;break;case"F":d[s][t]=parseFloat(v.trim());break;case"+":case"I":d[s][t]=j?0x80000000^u.read_shift(-4,"i"):u.read_shift(4,"i");break;case"L":switch(v.trim().toUpperCase()){case"Y":case"T":d[s][t]=!0;break;case"N":case"F":d[s][t]=!1;break;case"":case"?":break;default:throw Error("DBF Unrecognized L:|"+v+"|")}break;case"M":if(!h)throw Error("DBF Unexpected MEMO for type "+g.toString(16));d[s][t]="##MEMO##"+(j?parseInt(v.trim(),10):u.read_shift(4));break;case"N":(v=v.replace(/\u0000/g,"").trim())&&"."!=v&&(d[s][t]=+v||0);break;case"@":d[s][t]=new Date(u.read_shift(-8,"f")-621356832e5);break;case"T":d[s][t]=new Date((u.read_shift(4)-2440588)*864e5+u.read_shift(4));break;case"Y":d[s][t]=u.read_shift(4,"i")/1e4+u.read_shift(4,"i")/1e4*0x100000000;break;case"O":d[s][t]=-u.read_shift(-8,"f");break;case"B":if(i&&8==o[t].len){d[s][t]=u.read_shift(8,"f");break}case"G":case"P":u.l+=o[t].len;break;case"0":if("_NullFlags"===o[t].name)break;default:throw Error("DBF Unsupported data type "+o[t].type)}}}if(2!=g&&f.l<f.length&&26!=f[f.l++])throw Error("DBF EOF Marker missing "+(f.l-1)+" of "+f.length+" "+f[f.l-1].toString(16));return c&&c.sheetRows&&(d=d.slice(0,c.sheetRows)),c.DBF=o,d}(b,d),d);return f["!cols"]=d.DBF.map(function(a){return{wch:a.len,DBF:a}}),delete d.DBF,f}var d={B:8,C:250,L:1,D:8,"?":0,"":0};return{to_workbook:function(a,b){try{return cA(c(a,b),b)}catch(a){if(b&&b.WTF)throw a}return{SheetNames:[],Sheets:{}}},to_sheet:c,from_sheet:function(a,c){var e=c||{};if(+e.codepage>=0&&r(+e.codepage),"string"==e.type)throw Error("Cannot write DBF to JS string");var f=cj(),g=g7(a,{header:1,raw:!0,cellDates:!0}),h=g[0],i=g.slice(1),j=a["!cols"]||[],k=0,l=0,m=0,o=1;for(k=0;k<h.length;++k){if(((j[k]||{}).DBF||{}).name){h[k]=j[k].DBF.name,++m;continue}if(null!=h[k]){if(++m,"number"==typeof h[k]&&(h[k]=h[k].toString(10)),"string"!=typeof h[k])throw Error("DBF Invalid column name "+h[k]+" |"+typeof h[k]+"|");if(h.indexOf(h[k])!==k){for(l=0;l<1024;++l)if(-1==h.indexOf(h[k]+"_"+l)){h[k]+="_"+l;break}}}}var p=cx(a["!ref"]),q=[],s=[],t=[];for(k=0;k<=p.e.c-p.s.c;++k){var u="",v="",w=0,x=[];for(l=0;l<i.length;++l)null!=i[l][k]&&x.push(i[l][k]);if(0==x.length||null==h[k]){q[k]="?";continue}for(l=0;l<x.length;++l){switch(typeof x[l]){case"number":v="B";break;case"string":default:v="C";break;case"boolean":v="L";break;case"object":v=x[l]instanceof Date?"D":"C"}w=Math.max(w,String(x[l]).length),u=u&&u!=v?"C":v}w>250&&(w=250),"C"==(v=((j[k]||{}).DBF||{}).type)&&j[k].DBF.len>w&&(w=j[k].DBF.len),"B"==u&&"N"==v&&(u="N",t[k]=j[k].DBF.dec,w=j[k].DBF.len),s[k]="C"==u||"N"==v?w:d[u]||0,o+=s[k],q[k]=u}var y=f.next(32);for(y.write_shift(4,0x13021130),y.write_shift(4,i.length),y.write_shift(2,296+32*m),y.write_shift(2,o),k=0;k<4;++k)y.write_shift(4,0);for(y.write_shift(4,(+b[n]||3)<<8),k=0,l=0;k<h.length;++k)if(null!=h[k]){var z=f.next(32),A=(h[k].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);z.write_shift(1,A,"sbcs"),z.write_shift(1,"?"==q[k]?"C":q[k],"sbcs"),z.write_shift(4,l),z.write_shift(1,s[k]||d[q[k]]||0),z.write_shift(1,t[k]||0),z.write_shift(1,2),z.write_shift(4,0),z.write_shift(1,0),z.write_shift(4,0),z.write_shift(4,0),l+=s[k]||d[q[k]]||0}var B=f.next(264);for(B.write_shift(4,13),k=0;k<65;++k)B.write_shift(4,0);for(k=0;k<i.length;++k){var C=f.next(o);for(C.write_shift(1,0),l=0;l<h.length;++l)if(null!=h[l])switch(q[l]){case"L":C.write_shift(1,null==i[k][l]?63:i[k][l]?84:70);break;case"B":C.write_shift(8,i[k][l]||0,"f");break;case"N":var D="0";for("number"==typeof i[k][l]&&(D=i[k][l].toFixed(t[l]||0)),m=0;m<s[l]-D.length;++m)C.write_shift(1,32);C.write_shift(1,D,"sbcs");break;case"D":i[k][l]?(C.write_shift(4,("0000"+i[k][l].getFullYear()).slice(-4),"sbcs"),C.write_shift(2,("00"+(i[k][l].getMonth()+1)).slice(-2),"sbcs"),C.write_shift(2,("00"+i[k][l].getDate()).slice(-2),"sbcs")):C.write_shift(8,"00000000","sbcs");break;case"C":var E=String(null!=i[k][l]?i[k][l]:"").slice(0,s[l]);for(C.write_shift(1,E,"sbcs"),m=0;m<s[l]-E.length;++m)C.write_shift(1,32)}}return f.next(1).write_shift(1,26),f.end()}}}(),ef=function(){var a={AA:"\xc0",BA:"\xc1",CA:"\xc2",DA:195,HA:"\xc4",JA:197,AE:"\xc8",BE:"\xc9",CE:"\xca",HE:"\xcb",AI:"\xcc",BI:"\xcd",CI:"\xce",HI:"\xcf",AO:"\xd2",BO:"\xd3",CO:"\xd4",DO:213,HO:"\xd6",AU:"\xd9",BU:"\xda",CU:"\xdb",HU:"\xdc",Aa:"\xe0",Ba:"\xe1",Ca:"\xe2",Da:227,Ha:"\xe4",Ja:229,Ae:"\xe8",Be:"\xe9",Ce:"\xea",He:"\xeb",Ai:"\xec",Bi:"\xed",Ci:"\xee",Hi:"\xef",Ao:"\xf2",Bo:"\xf3",Co:"\xf4",Do:245,Ho:"\xf6",Au:"\xf9",Bu:"\xfa",Cu:"\xfb",Hu:"\xfc",KC:"\xc7",Kc:"\xe7",q:"\xe6",z:"œ",a:"\xc6",j:"Œ",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223},b=RegExp("\x1bN("+aC(a).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm"),c=function(b,c){var d=a[c];return"number"==typeof d?x(d):d},d=function(a,b,c){var d=b.charCodeAt(0)-32<<4|c.charCodeAt(0)-48;return 59==d?a:x(d)};function f(a,f){var g,h=a.split(/[\n\r]+/),i=-1,j=-1,k=0,l=0,m=[],n=[],o=null,p={},q=[],s=[],t=[],u=0;for(+f.codepage>=0&&r(+f.codepage);k!==h.length;++k){u=0;var v,w=h[k].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,d).replace(b,c),x=w.replace(/;;/g,"\0").split(";").map(function(a){return a.replace(/\u0000/g,";")}),y=x[0];if(w.length>0)switch(y){case"ID":case"E":case"B":case"O":case"W":break;case"P":"P"==x[1].charAt(0)&&n.push(w.slice(3).replace(/;;/g,";"));break;case"C":var z=!1,A=!1,B=!1,C=!1,D=-1,E=-1;for(l=1;l<x.length;++l)switch(x[l].charAt(0)){case"A":case"G":break;case"X":j=parseInt(x[l].slice(1))-1,A=!0;break;case"Y":for(i=parseInt(x[l].slice(1))-1,A||(j=0),g=m.length;g<=i;++g)m[g]=[];break;case"K":'"'===(v=x[l].slice(1)).charAt(0)?v=v.slice(1,v.length-1):"TRUE"===v?v=!0:"FALSE"===v?v=!1:isNaN(aT(v))?isNaN(aV(v).getDate())||(v=aP(v)):(v=aT(v),null!==o&&aq(o)&&(v=aL(v))),void 0!==e&&"string"==typeof v&&"string"!=(f||{}).type&&(f||{}).codepage&&(v=e.utils.decode(f.codepage,v)),z=!0;break;case"E":C=!0;var F=fd(x[l].slice(1),{r:i,c:j});m[i][j]=[m[i][j],F];break;case"S":B=!0,m[i][j]=[m[i][j],"S5S"];break;case"R":D=parseInt(x[l].slice(1))-1;break;case"C":E=parseInt(x[l].slice(1))-1;break;default:if(f&&f.WTF)throw Error("SYLK bad record "+w)}if(z&&(m[i][j]&&2==m[i][j].length?m[i][j][0]=v:m[i][j]=v,o=null),B){if(C)throw Error("SYLK shared formula cannot have own formula");var G=D>-1&&m[D][E];if(!G||!G[1])throw Error("SYLK shared formula cannot find base");m[i][j][1]=fg(G[1],{r:i-D,c:j-E})}break;case"F":var H=0;for(l=1;l<x.length;++l)switch(x[l].charAt(0)){case"X":j=parseInt(x[l].slice(1))-1,++H;break;case"Y":for(i=parseInt(x[l].slice(1))-1,g=m.length;g<=i;++g)m[g]=[];break;case"M":u=parseInt(x[l].slice(1))/20;break;case"F":case"G":case"S":case"D":case"N":break;case"P":o=n[parseInt(x[l].slice(1))];break;case"W":for(g=parseInt((t=x[l].slice(1).split(" "))[0],10);g<=parseInt(t[1],10);++g)u=parseInt(t[2],10),s[g-1]=0===u?{hidden:!0}:{wch:u},eN(s[g-1]);break;case"C":s[j=parseInt(x[l].slice(1))-1]||(s[j]={});break;case"R":q[i=parseInt(x[l].slice(1))-1]||(q[i]={}),u>0?(q[i].hpt=u,q[i].hpx=eP(u)):0===u&&(q[i].hidden=!0);break;default:if(f&&f.WTF)throw Error("SYLK bad record "+w)}H<1&&(o=null);break;default:if(f&&f.WTF)throw Error("SYLK bad record "+w)}}return q.length>0&&(p["!rows"]=q),s.length>0&&(p["!cols"]=s),f&&f.sheetRows&&(m=m.slice(0,f.sheetRows)),[m,p]}function g(a,b){var c=function(a,b){switch(b.type){case"base64":return f(A(a),b);case"binary":return f(a,b);case"buffer":return f(B&&Buffer.isBuffer(a)?a.toString("binary"):H(a),b);case"array":return f(aQ(a),b)}throw Error("Unrecognized type "+b.type)}(a,b),d=c[0],e=c[1],g=cB(d,b);return aC(e).forEach(function(a){g[a]=e[a]}),g}return a["|"]=254,{to_workbook:function(a,b){return cA(g(a,b),b)},to_sheet:g,from_sheet:function(a,b){var c,d=["ID;PWXL;N;E"],e=[],f=cx(a["!ref"]),g=Array.isArray(a);d.push("P;PGeneral"),d.push("F;P0;DG0G8;M255"),a["!cols"]&&a["!cols"].forEach(function(a,b){var c="F;W"+(b+1)+" "+(b+1)+" ";a.hidden?c+="0":("number"!=typeof a.width||a.wpx||(a.wpx=eI(a.width)),"number"!=typeof a.wpx||a.wch||(a.wch=eJ(a.wpx)),"number"==typeof a.wch&&(c+=Math.round(a.wch)))," "!=c.charAt(c.length-1)&&d.push(c)}),a["!rows"]&&a["!rows"].forEach(function(a,b){var c="F;";a.hidden?c+="M0;":a.hpt?c+="M"+20*a.hpt+";":a.hpx&&(c+="M"+20*eO(a.hpx)+";"),c.length>2&&d.push(c+"R"+(b+1))}),d.push("B;Y"+(f.e.r-f.s.r+1)+";X"+(f.e.c-f.s.c+1)+";D"+[f.s.c,f.s.r,f.e.c,f.e.r].join(" "));for(var h=f.s.r;h<=f.e.r;++h)for(var i=f.s.c;i<=f.e.c;++i){var j=cu({r:h,c:i});(c=g?(a[h]||[])[i]:a[j])&&(null!=c.v||c.f&&!c.F)&&e.push(function(a,b,c,d){var e="C;Y"+(c+1)+";X"+(d+1)+";K";switch(a.t){case"n":e+=a.v||0,a.f&&!a.F&&(e+=";E"+ff(a.f,{r:c,c:d}));break;case"b":e+=a.v?"TRUE":"FALSE";break;case"e":e+=a.w||a.v;break;case"d":e+='"'+(a.w||a.v)+'"';break;case"s":e+='"'+a.v.replace(/"/g,"").replace(/;/g,";;")+'"'}return e}(c,0,h,i,b))}return d.join("\r\n")+"\r\n"+e.join("\r\n")+"\r\nE\r\n"}}}(),eg=function(){var a,b;function c(a,b){for(var c=a.split("\n"),d=-1,e=-1,f=0,g=[];f!==c.length;++f){if("BOT"===c[f].trim()){g[++d]=[],e=0;continue}if(!(d<0)){for(var h=c[f].trim().split(","),i=h[0],j=h[1],k=c[++f]||"";1&(k.match(/["]/g)||[]).length&&f<c.length-1;)k+="\n"+c[++f];switch(k=k.trim(),+i){case -1:if("BOT"===k){g[++d]=[],e=0;continue}if("EOD"!==k)throw Error("Unrecognized DIF special command "+k);break;case 0:"TRUE"===k?g[d][e]=!0:"FALSE"===k?g[d][e]=!1:isNaN(aT(j))?isNaN(aV(j).getDate())?g[d][e]=j:g[d][e]=aP(j):g[d][e]=aT(j),++e;break;case 1:(k=(k=k.slice(1,k.length-1)).replace(/""/g,'"'))&&k.match(/^=".*"$/)&&(k=k.slice(2,-1)),g[d][e++]=""!==k?k:null}if("EOD"===k)break}}return b&&b.sheetRows&&(g=g.slice(0,b.sheetRows)),g}function d(a,b){return cB(function(a,b){switch(b.type){case"base64":return c(A(a),b);case"binary":return c(a,b);case"buffer":return c(B&&Buffer.isBuffer(a)?a.toString("binary"):H(a),b);case"array":return c(aQ(a),b)}throw Error("Unrecognized type "+b.type)}(a,b),b)}return{to_workbook:function(a,b){return cA(d(a,b),b)},to_sheet:d,from_sheet:(a=function(a,b,c,d,e){a.push(b),a.push(c+","+d),a.push('"'+e.replace(/"/g,'""')+'"')},b=function(a,b,c,d){a.push(b+","+c),a.push(1==b?'"'+d.replace(/"/g,'""')+'"':d)},function(c){var d,e=[],f=cx(c["!ref"]),g=Array.isArray(c);a(e,"TABLE",0,1,"sheetjs"),a(e,"VECTORS",0,f.e.r-f.s.r+1,""),a(e,"TUPLES",0,f.e.c-f.s.c+1,""),a(e,"DATA",0,0,"");for(var h=f.s.r;h<=f.e.r;++h){b(e,-1,0,"BOT");for(var i=f.s.c;i<=f.e.c;++i){var j=cu({r:h,c:i});if(!(d=g?(c[h]||[])[i]:c[j])){b(e,1,0,"");continue}switch(d.t){case"n":var k=d.w;k||null==d.v||(k=d.v),null==k?!d.f||d.F?b(e,1,0,""):b(e,1,0,"="+d.f):b(e,0,k,"V");break;case"b":b(e,0,+!!d.v,d.v?"TRUE":"FALSE");break;case"s":b(e,1,0,isNaN(d.v)?d.v:'="'+d.v+'"');break;case"d":d.w||(d.w=at(d.z||U[14],aH(aP(d.v)))),b(e,0,d.w,"V");break;default:b(e,1,0,"")}}}return b(e,-1,0,"EOD"),e.join("\r\n")})}}(),eh=function(){function a(a){return a.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function b(a,b){return cB(function(a,b){for(var c=a.split("\n"),d=-1,e=-1,f=0,g=[];f!==c.length;++f){var h=c[f].trim().split(":");if("cell"===h[0]){var i=ct(h[1]);if(g.length<=i.r)for(d=g.length;d<=i.r;++d)g[d]||(g[d]=[]);switch(d=i.r,e=i.c,h[2]){case"t":g[d][e]=h[3].replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,"\n");break;case"v":g[d][e]=+h[3];break;case"vtf":var j=h[h.length-1];case"vtc":"nl"===h[3]?g[d][e]=!!+h[4]:g[d][e]=+h[4],"vtf"==h[2]&&(g[d][e]=[g[d][e],j])}}}return b&&b.sheetRows&&(g=g.slice(0,b.sheetRows)),g}(a,b),b)}var c="--SocialCalcSpreadsheetControlSave\nContent-type: text/plain; charset=UTF-8\n";return{to_workbook:function(a,c){return cA(b(a,c),c)},to_sheet:b,from_sheet:function(b){return["socialcalc:version:1.5\nMIME-Version: 1.0\nContent-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave",c,"# SocialCalc Spreadsheet Control Save\npart:sheet",c,function(b){if(!b||!b["!ref"])return"";for(var c,d=[],e=[],f="",g=cv(b["!ref"]),h=Array.isArray(b),i=g.s.r;i<=g.e.r;++i)for(var j=g.s.c;j<=g.e.c;++j)if(f=cu({r:i,c:j}),(c=h?(b[i]||[])[j]:b[f])&&null!=c.v&&"z"!==c.t){switch(e=["cell",f,"t"],c.t){case"s":case"str":e.push(a(c.v));break;case"n":c.f?(e[2]="vtf",e[3]="n",e[4]=c.v,e[5]=a(c.f)):(e[2]="v",e[3]=c.v);break;case"b":e[2]="vt"+(c.f?"f":"c"),e[3]="nl",e[4]=c.v?"1":"0",e[5]=a(c.f||(c.v?"TRUE":"FALSE"));break;case"d":var k=aH(aP(c.v));e[2]="vtc",e[3]="nd",e[4]=""+k,e[5]=c.w||at(c.z||U[14],k);break;case"e":continue}d.push(e.join(":"))}return d.push("sheet:c:"+(g.e.c-g.s.c+1)+":r:"+(g.e.r-g.s.r+1)+":tvf:1"),d.push("valueformat:1:text-wiki"),d.join("\n")}(b),"--SocialCalcSpreadsheetControlSave--"].join("\n")}}}(),ei=function(){function a(a,b,c,d,e){e.raw?b[c][d]=a:""===a||("TRUE"===a?b[c][d]=!0:"FALSE"===a?b[c][d]=!1:isNaN(aT(a))?isNaN(aV(a).getDate())?b[c][d]=a:b[c][d]=aP(a):b[c][d]=aT(a))}var b={44:",",9:"	",59:";",124:"|"},c={44:3,9:2,59:1,124:0};function d(a){for(var d={},e=!1,f=0,g=0;f<a.length;++f)34==(g=a.charCodeAt(f))?e=!e:!e&&g in b&&(d[g]=(d[g]||0)+1);for(f in g=[],d)Object.prototype.hasOwnProperty.call(d,f)&&g.push([d[f],f]);if(!g.length)for(f in d=c)Object.prototype.hasOwnProperty.call(d,f)&&g.push([d[f],f]);return g.sort(function(a,b){return a[0]-b[0]||c[a[1]]-c[b[1]]}),b[g.pop()[1]]||44}function f(b,c){var f,g="",h="string"==c.type?[0,0,0,0]:g1(b,c);switch(c.type){case"base64":g=A(b);break;case"binary":case"string":g=b;break;case"buffer":g=65001==c.codepage?b.toString("utf8"):c.codepage&&void 0!==e?e.utils.decode(c.codepage,b):B&&Buffer.isBuffer(b)?b.toString("binary"):H(b);break;case"array":g=aQ(b);break;default:throw Error("Unrecognized type "+c.type)}return(239==h[0]&&187==h[1]&&191==h[2]?g=bt(g.slice(3)):"string"!=c.type&&"buffer"!=c.type&&65001==c.codepage?g=bt(g):"binary"==c.type&&void 0!==e&&c.codepage&&(g=e.utils.decode(c.codepage,e.utils.encode(28591,g))),"socialcalc:version:"==g.slice(0,19))?eh.to_sheet("string"==c.type?g:bt(g),c):(f=g,!(c&&c.PRN)||c.FS||"sep="==f.slice(0,4)||f.indexOf("	")>=0||f.indexOf(",")>=0||f.indexOf(";")>=0?function(a,b){var c,e=b||{},f="",g=e.dense?[]:{},h={s:{c:0,r:0},e:{c:0,r:0}};"sep="==a.slice(0,4)?13==a.charCodeAt(5)&&10==a.charCodeAt(6)?(f=a.charAt(4),a=a.slice(7)):13==a.charCodeAt(5)||10==a.charCodeAt(5)?(f=a.charAt(4),a=a.slice(6)):f=d(a.slice(0,1024)):f=e&&e.FS?e.FS:d(a.slice(0,1024));var i=0,j=0,k=0,l=0,m=0,n=f.charCodeAt(0),o=!1,p=0,q=a.charCodeAt(0);a=a.replace(/\r\n/mg,"\n");var r=null!=e.dateNF?RegExp("^"+("number"==typeof(c=e.dateNF)?U[c]:c).replace(ay,"(\\d+)")+"$"):null;function s(){var b=a.slice(l,m),c={};if('"'==b.charAt(0)&&'"'==b.charAt(b.length-1)&&(b=b.slice(1,-1).replace(/""/g,'"')),0===b.length)c.t="z";else if(e.raw)c.t="s",c.v=b;else if(0===b.trim().length)c.t="s",c.v=b;else if(61==b.charCodeAt(0))34==b.charCodeAt(1)&&34==b.charCodeAt(b.length-1)?(c.t="s",c.v=b.slice(2,-1).replace(/""/g,'"')):1!=b.length?(c.t="n",c.f=b.slice(1)):(c.t="s",c.v=b);else if("TRUE"==b)c.t="b",c.v=!0;else if("FALSE"==b)c.t="b",c.v=!1;else if(isNaN(k=aT(b)))if(!isNaN(aV(b).getDate())||r&&b.match(r)){c.z=e.dateNF||U[14];var d,f,o,s,t,u,v,w,x,y,z=0;r&&b.match(r)&&(d=e.dateNF,f=b.match(r)||[],o=-1,s=-1,t=-1,u=-1,v=-1,w=-1,(d.match(ay)||[]).forEach(function(a,b){var c=parseInt(f[b+1],10);switch(a.toLowerCase().charAt(0)){case"y":o=c;break;case"d":t=c;break;case"h":u=c;break;case"s":w=c;break;case"m":u>=0?v=c:s=c}}),w>=0&&-1==v&&s>=0&&(v=s,s=-1),7==(x=(""+(o>=0?o:new Date().getFullYear())).slice(-4)+"-"+("00"+(s>=1?s:1)).slice(-2)+"-"+("00"+(t>=1?t:1)).slice(-2)).length&&(x="0"+x),8==x.length&&(x="20"+x),y=("00"+(u>=0?u:0)).slice(-2)+":"+("00"+(v>=0?v:0)).slice(-2)+":"+("00"+(w>=0?w:0)).slice(-2),b=-1==u&&-1==v&&-1==w?x:-1==o&&-1==s&&-1==t?y:x+"T"+y,z=1),e.cellDates?(c.t="d",c.v=aP(b,z)):(c.t="n",c.v=aH(aP(b,z))),!1!==e.cellText&&(c.w=at(c.z,c.v instanceof Date?aH(c.v):c.v)),e.cellNF||delete c.z}else c.t="s",c.v=b;else c.t="n",!1!==e.cellText&&(c.w=b),c.v=k;if("z"==c.t||(e.dense?(g[i]||(g[i]=[]),g[i][j]=c):g[cu({c:j,r:i})]=c),l=m+1,q=a.charCodeAt(l),h.e.c<j&&(h.e.c=j),h.e.r<i&&(h.e.r=i),p==n)++j;else if(j=0,++i,e.sheetRows&&e.sheetRows<=i)return!0}a:for(;m<a.length;++m)switch(p=a.charCodeAt(m)){case 34:34===q&&(o=!o);break;case n:case 10:case 13:if(!o&&s())break a}return m-l>0&&s(),g["!ref"]=cw(h),g}(f,c):cB(function(b,c){var d=c||{},e=[];if(!b||0===b.length)return e;for(var f=b.split(/[\r\n]/),g=f.length-1;g>=0&&0===f[g].length;)--g;for(var h=10,i=0,j=0;j<=g;++j)-1==(i=f[j].indexOf(" "))?i=f[j].length:i++,h=Math.max(h,i);for(j=0;j<=g;++j){e[j]=[];var k=0;for(a(f[j].slice(0,h).trim(),e,j,k,d),k=1;k<=(f[j].length-h)/10+1;++k)a(f[j].slice(h+(k-1)*10,h+10*k).trim(),e,j,k,d)}return d.sheetRows&&(e=e.slice(0,d.sheetRows)),e}(f,c),c))}return{to_workbook:function(a,b){return cA(f(a,b),b)},to_sheet:f,from_sheet:function(a){for(var b,c=[],d=cx(a["!ref"]),e=Array.isArray(a),f=d.s.r;f<=d.e.r;++f){for(var g=[],h=d.s.c;h<=d.e.c;++h){var i=cu({r:f,c:h});if(!(b=e?(a[f]||[])[h]:a[i])||null==b.v){g.push("          ");continue}for(var j=(b.w||(cz(b),b.w)||"").slice(0,10);j.length<10;)j+=" ";g.push(j+(0===h?" ":""))}c.push(g.join(""))}return c.join("\n")}}}(),ej=function(){function a(a,b,c){if(a){cf(a,a.l||0);for(var d=c.Enum||l;a.l<a.length;){var e=a.read_shift(2),f=d[e]||d[65535],g=a.read_shift(2),h=a.l+g,i=f.f&&f.f(a,g,c);if(a.l=h,b(i,f,e))return}}}function b(b,c){if(!b)return b;var d=c||{},e=d.dense?[]:{},f="Sheet1",g="",h=0,i={},j=[],k=[],n={s:{r:0,c:0},e:{r:0,c:0}},o=d.sheetRows||0;if(0==b[2]&&(8==b[3]||9==b[3])&&b.length>=16&&5==b[14]&&108===b[15])throw Error("Unsupported Works 3 for Mac file");if(2==b[2])d.Enum=l,a(b,function(a,b,c){switch(c){case 0:d.vers=a,a>=4096&&(d.qpro=!0);break;case 6:n=a;break;case 204:a&&(g=a);break;case 222:g=a;break;case 15:case 51:d.qpro||(a[1].v=a[1].v.slice(1));case 13:case 14:case 16:14==c&&(112&a[2])==112&&(15&a[2])>1&&(15&a[2])<15&&(a[1].z=d.dateNF||U[14],d.cellDates&&(a[1].t="d",a[1].v=aL(a[1].v))),d.qpro&&a[3]>h&&(e["!ref"]=cw(n),i[f]=e,j.push(f),e=d.dense?[]:{},n={s:{r:0,c:0},e:{r:0,c:0}},h=a[3],f=g||"Sheet"+(h+1),g="");var k=d.dense?(e[a[0].r]||[])[a[0].c]:e[cu(a[0])];if(k){k.t=a[1].t,k.v=a[1].v,null!=a[1].z&&(k.z=a[1].z),null!=a[1].f&&(k.f=a[1].f);break}d.dense?(e[a[0].r]||(e[a[0].r]=[]),e[a[0].r][a[0].c]=a[1]):e[cu(a[0])]=a[1]}},d);else if(26==b[2]||14==b[2])d.Enum=m,14==b[2]&&(d.qpro=!0,b.l=0),a(b,function(a,b,c){switch(c){case 204:f=a;break;case 22:a[1].v=a[1].v.slice(1);case 23:case 24:case 25:case 37:case 39:case 40:if(a[3]>h&&(e["!ref"]=cw(n),i[f]=e,j.push(f),e=d.dense?[]:{},n={s:{r:0,c:0},e:{r:0,c:0}},f="Sheet"+((h=a[3])+1)),o>0&&a[0].r>=o)break;d.dense?(e[a[0].r]||(e[a[0].r]=[]),e[a[0].r][a[0].c]=a[1]):e[cu(a[0])]=a[1],n.e.c<a[0].c&&(n.e.c=a[0].c),n.e.r<a[0].r&&(n.e.r=a[0].r);break;case 27:a[14e3]&&(k[a[14e3][0]]=a[14e3][1]);break;case 1537:k[a[0]]=a[1],a[0]==h&&(f=a[1])}},d);else throw Error("Unrecognized LOTUS BOF "+b[2]);if(e["!ref"]=cw(n),i[g||f]=e,j.push(g||f),!k.length)return{SheetNames:j,Sheets:i};for(var p={},q=[],r=0;r<k.length;++r)i[j[r]]?(q.push(k[r]||j[r]),p[k[r]]=i[k[r]]||i[j[r]]):(q.push(k[r]),p[k[r]]={"!ref":"A1"});return{SheetNames:q,Sheets:p}}function c(a,b,c){var d=[{c:0,r:0},{t:"n",v:0},0,0];return c.qpro&&20768!=c.vers?(d[0].c=a.read_shift(1),d[3]=a.read_shift(1),d[0].r=a.read_shift(2),a.l+=2):(d[2]=a.read_shift(1),d[0].c=a.read_shift(2),d[0].r=a.read_shift(2)),d}function d(a,b,d){var e=a.l+b,f=c(a,b,d);if(f[1].t="s",20768==d.vers){a.l++;var g=a.read_shift(1);return f[1].v=a.read_shift(g,"utf8"),f}return d.qpro&&a.l++,f[1].v=a.read_shift(e-a.l,"cstr"),f}function e(a,b,c){var d=32768&b;return b&=-32769,b=(d?a:0)+(b>=8192?b-16384:b),(d?"":"$")+(c?cs(b):cq(b))}var f={51:["FALSE",0],52:["TRUE",0],70:["LEN",1],80:["SUM",69],81:["AVERAGEA",69],82:["COUNTA",69],83:["MINA",69],84:["MAXA",69],111:["T",1]},g=["","","","","","","","","","+","-","*","/","^","=","<>","<=",">=","<",">","","","","","&","","","","","","",""];function h(a){var b=[{c:0,r:0},{t:"n",v:0},0];return b[0].r=a.read_shift(2),b[3]=a[a.l++],b[0].c=a[a.l++],b}function i(a,b){var c=h(a,b),d=a.read_shift(4),e=a.read_shift(4),f=a.read_shift(2);if(65535==f)return 0===d&&0xc0000000===e?(c[1].t="e",c[1].v=15):0===d&&0xd0000000===e?(c[1].t="e",c[1].v=42):c[1].v=0,c;var g=32768&f;return f=(32767&f)-16446,c[1].v=(1-2*g)*(e*Math.pow(2,f+32)+d*Math.pow(2,f)),c}function j(a,b){var c=h(a,b),d=a.read_shift(8,"f");return c[1].v=d,c}function k(a,b){return 0==a[a.l+b-1]?a.read_shift(b,"cstr"):""}var l={0:{n:"BOF",f:dF},1:{n:"EOF"},2:{n:"CALCMODE"},3:{n:"CALCORDER"},4:{n:"SPLIT"},5:{n:"SYNC"},6:{n:"RANGE",f:function(a,b,c){var d={s:{c:0,r:0},e:{c:0,r:0}};return 8==b&&c.qpro?(d.s.c=a.read_shift(1),a.l++,d.s.r=a.read_shift(2),d.e.c=a.read_shift(1),a.l++,d.e.r=a.read_shift(2)):(d.s.c=a.read_shift(2),d.s.r=a.read_shift(2),12==b&&c.qpro&&(a.l+=2),d.e.c=a.read_shift(2),d.e.r=a.read_shift(2),12==b&&c.qpro&&(a.l+=2),65535==d.s.c&&(d.s.c=d.e.c=d.s.r=d.e.r=0)),d}},7:{n:"WINDOW1"},8:{n:"COLW1"},9:{n:"WINTWO"},10:{n:"COLW2"},11:{n:"NAME"},12:{n:"BLANK"},13:{n:"INTEGER",f:function(a,b,d){var e=c(a,b,d);return e[1].v=a.read_shift(2,"i"),e}},14:{n:"NUMBER",f:function(a,b,d){var e=c(a,b,d);return e[1].v=a.read_shift(8,"f"),e}},15:{n:"LABEL",f:d},16:{n:"FORMULA",f:function(a,b,d){var h=a.l+b,i=c(a,b,d);if(i[1].v=a.read_shift(8,"f"),d.qpro)a.l=h;else{var j=a.read_shift(2);(function(a,b){cf(a,0);for(var c=[],d=0,h="",i="",j="",k="";a.l<a.length;){var l=a[a.l++];switch(l){case 0:c.push(a.read_shift(8,"f"));break;case 1:i=e(b[0].c,a.read_shift(2),!0),h=e(b[0].r,a.read_shift(2),!1),c.push(i+h);break;case 2:var m=e(b[0].c,a.read_shift(2),!0),n=e(b[0].r,a.read_shift(2),!1);i=e(b[0].c,a.read_shift(2),!0),h=e(b[0].r,a.read_shift(2),!1),c.push(m+n+":"+i+h);break;case 3:if(a.l<a.length)return void console.error("WK1 premature formula end");break;case 4:c.push("("+c.pop()+")");break;case 5:c.push(a.read_shift(2));break;case 6:for(var o="";l=a[a.l++];)o+=String.fromCharCode(l);c.push('"'+o.replace(/"/g,'""')+'"');break;case 8:c.push("-"+c.pop());break;case 23:c.push("+"+c.pop());break;case 22:c.push("NOT("+c.pop()+")");break;case 20:case 21:k=c.pop(),j=c.pop(),c.push(["AND","OR"][l-20]+"("+j+","+k+")");break;default:if(l<32&&g[l])k=c.pop(),j=c.pop(),c.push(j+g[l]+k);else if(f[l]){if(69==(d=f[l][1])&&(d=a[a.l++]),d>c.length)return void console.error("WK1 bad formula parse 0x"+l.toString(16)+":|"+c.join("|")+"|");var p=c.slice(-d);c.length-=d,c.push(f[l][0]+"("+p.join(",")+")")}else if(l<=7)return console.error("WK1 invalid opcode "+l.toString(16));else if(l<=24)return console.error("WK1 unsupported op "+l.toString(16));else if(l<=30)return console.error("WK1 invalid opcode "+l.toString(16));else if(l<=115)return console.error("WK1 unsupported function opcode "+l.toString(16));else return console.error("WK1 unrecognized opcode "+l.toString(16))}}1==c.length?b[1].f=""+c[0]:console.error("WK1 bad formula parse |"+c.join("|")+"|")})(a.slice(a.l,a.l+j),i),a.l+=j}return i}},24:{n:"TABLE"},25:{n:"ORANGE"},26:{n:"PRANGE"},27:{n:"SRANGE"},28:{n:"FRANGE"},29:{n:"KRANGE1"},32:{n:"HRANGE"},35:{n:"KRANGE2"},36:{n:"PROTEC"},37:{n:"FOOTER"},38:{n:"HEADER"},39:{n:"SETUP"},40:{n:"MARGINS"},41:{n:"LABELFMT"},42:{n:"TITLES"},43:{n:"SHEETJS"},45:{n:"GRAPH"},46:{n:"NGRAPH"},47:{n:"CALCCOUNT"},48:{n:"UNFORMATTED"},49:{n:"CURSORW12"},50:{n:"WINDOW"},51:{n:"STRING",f:d},55:{n:"PASSWORD"},56:{n:"LOCKED"},60:{n:"QUERY"},61:{n:"QUERYNAME"},62:{n:"PRINT"},63:{n:"PRINTNAME"},64:{n:"GRAPH2"},65:{n:"GRAPHNAME"},66:{n:"ZOOM"},67:{n:"SYMSPLIT"},68:{n:"NSROWS"},69:{n:"NSCOLS"},70:{n:"RULER"},71:{n:"NNAME"},72:{n:"ACOMM"},73:{n:"AMACRO"},74:{n:"PARSE"},102:{n:"PRANGES??"},103:{n:"RRANGES??"},104:{n:"FNAME??"},105:{n:"MRANGES??"},204:{n:"SHEETNAMECS",f:k},222:{n:"SHEETNAMELP",f:function(a,b){var c=a[a.l++];c>b-1&&(c=b-1);for(var d="";d.length<c;)d+=String.fromCharCode(a[a.l++]);return d}},65535:{n:""}},m={0:{n:"BOF"},1:{n:"EOF"},2:{n:"PASSWORD"},3:{n:"CALCSET"},4:{n:"WINDOWSET"},5:{n:"SHEETCELLPTR"},6:{n:"SHEETLAYOUT"},7:{n:"COLUMNWIDTH"},8:{n:"HIDDENCOLUMN"},9:{n:"USERRANGE"},10:{n:"SYSTEMRANGE"},11:{n:"ZEROFORCE"},12:{n:"SORTKEYDIR"},13:{n:"FILESEAL"},14:{n:"DATAFILLNUMS"},15:{n:"PRINTMAIN"},16:{n:"PRINTSTRING"},17:{n:"GRAPHMAIN"},18:{n:"GRAPHSTRING"},19:{n:"??"},20:{n:"ERRCELL"},21:{n:"NACELL"},22:{n:"LABEL16",f:function(a,b){var c=h(a,b);return c[1].t="s",c[1].v=a.read_shift(b-4,"cstr"),c}},23:{n:"NUMBER17",f:i},24:{n:"NUMBER18",f:function(a,b){var c=h(a,b);c[1].v=a.read_shift(2);var d=c[1].v>>1;if(1&c[1].v)switch(7&d){case 0:d=(d>>3)*5e3;break;case 1:d=(d>>3)*500;break;case 2:d=(d>>3)/20;break;case 3:d=(d>>3)/200;break;case 4:d=(d>>3)/2e3;break;case 5:d=(d>>3)/2e4;break;case 6:d=(d>>3)/16;break;case 7:d=(d>>3)/64}return c[1].v=d,c}},25:{n:"FORMULA19",f:function(a,b){var c=i(a,14);return a.l+=b-14,c}},26:{n:"FORMULA1A"},27:{n:"XFORMAT",f:function(a,b){for(var c={},d=a.l+b;a.l<d;){var e=a.read_shift(2);if(14e3==e){for(c[e]=[0,""],c[e][0]=a.read_shift(2);a[a.l];)c[e][1]+=String.fromCharCode(a[a.l]),a.l++;a.l++}}return c}},28:{n:"DTLABELMISC"},29:{n:"DTLABELCELL"},30:{n:"GRAPHWINDOW"},31:{n:"CPA"},32:{n:"LPLAUTO"},33:{n:"QUERY"},34:{n:"HIDDENSHEET"},35:{n:"??"},37:{n:"NUMBER25",f:function(a,b){var c=h(a,b),d=a.read_shift(4);return c[1].v=d>>6,c}},38:{n:"??"},39:{n:"NUMBER27",f:j},40:{n:"FORMULA28",f:function(a,b){var c=j(a,14);return a.l+=b-10,c}},142:{n:"??"},147:{n:"??"},150:{n:"??"},151:{n:"??"},152:{n:"??"},153:{n:"??"},154:{n:"??"},155:{n:"??"},156:{n:"??"},163:{n:"??"},174:{n:"??"},175:{n:"??"},176:{n:"??"},177:{n:"??"},184:{n:"??"},185:{n:"??"},186:{n:"??"},187:{n:"??"},188:{n:"??"},195:{n:"??"},201:{n:"??"},204:{n:"SHEETNAMECS",f:k},205:{n:"??"},206:{n:"??"},207:{n:"??"},208:{n:"??"},256:{n:"??"},259:{n:"??"},260:{n:"??"},261:{n:"??"},262:{n:"??"},263:{n:"??"},265:{n:"??"},266:{n:"??"},267:{n:"??"},268:{n:"??"},270:{n:"??"},271:{n:"??"},384:{n:"??"},389:{n:"??"},390:{n:"??"},393:{n:"??"},396:{n:"??"},512:{n:"??"},514:{n:"??"},513:{n:"??"},516:{n:"??"},517:{n:"??"},640:{n:"??"},641:{n:"??"},642:{n:"??"},643:{n:"??"},644:{n:"??"},645:{n:"??"},646:{n:"??"},647:{n:"??"},648:{n:"??"},658:{n:"??"},659:{n:"??"},660:{n:"??"},661:{n:"??"},662:{n:"??"},665:{n:"??"},666:{n:"??"},768:{n:"??"},772:{n:"??"},1537:{n:"SHEETINFOQP",f:function(a,b,c){if(c.qpro&&!(b<21)){var d=a.read_shift(1);return a.l+=17,a.l+=1,a.l+=2,[d,a.read_shift(b-21,"cstr")]}}},1600:{n:"??"},1602:{n:"??"},1793:{n:"??"},1794:{n:"??"},1795:{n:"??"},1796:{n:"??"},1920:{n:"??"},2048:{n:"??"},2049:{n:"??"},2052:{n:"??"},2688:{n:"??"},10998:{n:"??"},12849:{n:"??"},28233:{n:"??"},28484:{n:"??"},65535:{n:""}};return{sheet_to_wk1:function(a,b){var c,d,e,f,g=b||{};if(+g.codepage>=0&&r(+g.codepage),"string"==g.type)throw Error("Cannot write WK1 to JS string");var h=cj(),i=cx(a["!ref"]),j=Array.isArray(a),k=[];gx(h,0,(c=1030,(d=ch(2)).write_shift(2,1030),d)),gx(h,6,(e=i,(f=ch(8)).write_shift(2,e.s.c),f.write_shift(2,e.s.r),f.write_shift(2,e.e.c),f.write_shift(2,e.e.r),f));for(var l=Math.min(i.e.r,8191),m=i.s.r;m<=l;++m)for(var n=cq(m),o=i.s.c;o<=i.e.c;++o){m===i.s.r&&(k[o]=cs(o));var p=k[o]+n,q=j?(a[m]||[])[o]:a[p];q&&"z"!=q.t&&("n"==q.t?(0|q.v)==q.v&&q.v>=-32768&&q.v<=32767?gx(h,13,function(a,b,c){var d=ch(7);return d.write_shift(1,255),d.write_shift(2,b),d.write_shift(2,a),d.write_shift(2,c,"i"),d}(m,o,q.v)):gx(h,14,function(a,b,c){var d=ch(13);return d.write_shift(1,255),d.write_shift(2,b),d.write_shift(2,a),d.write_shift(8,c,"f"),d}(m,o,q.v)):gx(h,15,function(a,b,c){var d=ch(7+c.length);d.write_shift(1,255),d.write_shift(2,b),d.write_shift(2,a),d.write_shift(1,39);for(var e=0;e<d.length;++e){var f=c.charCodeAt(e);d.write_shift(1,f>=128?95:f)}return d.write_shift(1,0),d}(m,o,cz(q).slice(0,239))))}return gx(h,1),h.end()},book_to_wk3:function(a,b){var c=b||{};if(+c.codepage>=0&&r(+c.codepage),"string"==c.type)throw Error("Cannot write WK3 to JS string");var d=cj();gx(d,0,function(a){var b=ch(26);b.write_shift(2,4096),b.write_shift(2,4),b.write_shift(4,0);for(var c=0,d=0,e=0,f=0;f<a.SheetNames.length;++f){var g=a.SheetNames[f],h=a.Sheets[g];if(h&&h["!ref"]){++e;var i=cv(h["!ref"]);c<i.e.r&&(c=i.e.r),d<i.e.c&&(d=i.e.c)}}return c>8191&&(c=8191),b.write_shift(2,c),b.write_shift(1,e),b.write_shift(1,d),b.write_shift(2,0),b.write_shift(2,0),b.write_shift(1,1),b.write_shift(1,2),b.write_shift(4,0),b.write_shift(4,0),b}(a));for(var e=0,f=0;e<a.SheetNames.length;++e)(a.Sheets[a.SheetNames[e]]||{})["!ref"]&&gx(d,27,function(a,b){var c=ch(5+a.length);c.write_shift(2,14e3),c.write_shift(2,b);for(var d=0;d<a.length;++d){var e=a.charCodeAt(d);c[c.l++]=e>127?95:e}return c[c.l++]=0,c}(a.SheetNames[e],f++));var g=0;for(e=0;e<a.SheetNames.length;++e){var h=a.Sheets[a.SheetNames[e]];if(h&&h["!ref"]){for(var i=cx(h["!ref"]),j=Array.isArray(h),k=[],l=Math.min(i.e.r,8191),m=i.s.r;m<=l;++m)for(var n=cq(m),o=i.s.c;o<=i.e.c;++o){m===i.s.r&&(k[o]=cs(o));var p=k[o]+n,q=j?(h[m]||[])[o]:h[p];q&&"z"!=q.t&&("n"==q.t?gx(d,23,function(a,b,c,d){var e=ch(14);if(e.write_shift(2,a),e.write_shift(1,c),e.write_shift(1,b),0==d)return e.write_shift(4,0),e.write_shift(4,0),e.write_shift(2,65535),e;var f=0,g=0,h=0,i=0;return d<0&&(f=1,d=-d),g=0|Math.log2(d),d/=Math.pow(2,g-31),(0x80000000&(i=d>>>0))==0&&(d/=2,++g,i=d>>>0),d-=i,i|=0x80000000,i>>>=0,d*=0x100000000,h=d>>>0,e.write_shift(4,h),e.write_shift(4,i),g+=16383+32768*!!f,e.write_shift(2,g),e}(m,o,g,q.v)):gx(d,22,function(a,b,c,d){var e=ch(6+d.length);e.write_shift(2,a),e.write_shift(1,c),e.write_shift(1,b),e.write_shift(1,39);for(var f=0;f<d.length;++f){var g=d.charCodeAt(f);e.write_shift(1,g>=128?95:g)}return e.write_shift(1,0),e}(m,o,g,cz(q).slice(0,239))))}++g}}return gx(d,1),d.end()},to_workbook:function(a,c){switch(c.type){case"base64":return b(F(A(a)),c);case"binary":return b(F(a),c);case"buffer":case"array":return b(a,c)}throw"Unsupported type "+c.type}}}(),ek=function(){var a=bv("t"),b=bv("rPr");function c(c){var d=c.match(a);if(!d)return{t:"s",v:""};var e={t:"s",v:bg(d[1])},f=c.match(b);return f&&(e.s=function(a){var b={},c=a.match(a9),d=0,e=!1;if(c)for(;d!=c.length;++d){var f=bc(c[d]);switch(f[0].replace(/\w*:/g,"")){case"<condense":case"<extend":break;case"<shadow":if(!f.val)break;case"<shadow>":case"<shadow/>":b.shadow=1;break;case"</shadow>":break;case"<charset":if("1"==f.val)break;b.cp=p[parseInt(f.val,10)];break;case"<outline":if(!f.val)break;case"<outline>":case"<outline/>":b.outline=1;break;case"</outline>":break;case"<rFont":b.name=f.val;break;case"<sz":b.sz=f.val;break;case"<strike":if(!f.val)break;case"<strike>":case"<strike/>":b.strike=1;break;case"</strike>":break;case"<u":if(!f.val)break;switch(f.val){case"double":b.uval="double";break;case"singleAccounting":b.uval="single-accounting";break;case"doubleAccounting":b.uval="double-accounting"}case"<u>":case"<u/>":b.u=1;break;case"</u>":break;case"<b":if("0"==f.val)break;case"<b>":case"<b/>":b.b=1;break;case"</b>":break;case"<i":if("0"==f.val)break;case"<i>":case"<i/>":b.i=1;break;case"</i>":case"<color>":case"<color/>":case"</color>":case"<family>":case"<family/>":case"</family>":case"<vertAlign>":case"<vertAlign/>":case"</vertAlign>":case"<scheme":case"<scheme>":case"<scheme/>":case"</scheme>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<color":f.rgb&&(b.color=f.rgb.slice(2,8));break;case"<family":b.family=f.val;break;case"<vertAlign":b.valign=f.val;break;case"<ext":e=!0;break;case"</ext>":e=!1;break;default:if(47!==f[0].charCodeAt(1)&&!e)throw Error("Unrecognized rich format "+f[0])}}return b}(f[1])),e}var d=/<(?:\w+:)?r>/g,e=/<\/(?:\w+:)?r>/;return function(a){return a.replace(d,"").split(e).map(c).filter(function(a){return a.v})}}(),el=function(){var a=/(\r\n|\n)/g;function b(b){var c,d,e,f,g,h=[[],b.v,[]];return b.v?(b.s&&(c=b.s,d=h[0],e=h[2],f=[],c.u&&f.push("text-decoration: underline;"),c.uval&&f.push("text-underline-style:"+c.uval+";"),c.sz&&f.push("font-size:"+c.sz+"pt;"),c.outline&&f.push("text-effect: outline;"),c.shadow&&f.push("text-shadow: auto;"),d.push('<span style="'+f.join("")+'">'),c.b&&(d.push("<b>"),e.push("</b>")),c.i&&(d.push("<i>"),e.push("</i>")),c.strike&&(d.push("<s>"),e.push("</s>")),"superscript"==(g=c.valign||"")||"super"==g?g="sup":"subscript"==g&&(g="sub"),""!=g&&(d.push("<"+g+">"),e.push("</"+g+">")),e.push("</span>")),h[0].join("")+h[1].replace(a,"<br/>")+h[2].join("")):""}return function(a){return a.map(b).join("")}}(),em=/<(?:\w+:)?t[^>]*>([^<]*)<\/(?:\w+:)?t>/g,en=/<(?:\w+:)?r>/,eo=/<(?:\w+:)?rPh.*?>([\s\S]*?)<\/(?:\w+:)?rPh>/g;function ep(a,b){var c=!b||b.cellHTML,d={};return a?(a.match(/^\s*<(?:\w+:)?t[^>]*>/)?(d.t=bg(bt(a.slice(a.indexOf(">")+1).split(/<\/(?:\w+:)?t>/)[0]||"")),d.r=bt(a),c&&(d.h=bm(d.t))):a.match(en)&&(d.r=bt(a),d.t=bg(bt((a.replace(eo,"").match(em)||[]).join("").replace(a9,""))),c&&(d.h=el(ek(d.r)))),d):{t:""}}var eq=/<(?:\w+:)?sst([^>]*)>([\s\S]*)<\/(?:\w+:)?sst>/,er=/<(?:\w+:)?(?:si|sstItem)>/g,es=/<\/(?:\w+:)?(?:si|sstItem)>/,et=/^\s|\s$|[\t\n\r]/;function eu(a,b){if(!b.bookSST)return"";var c=[a6];c[c.length]=bE("sst",null,{xmlns:bJ[0],count:a.Count,uniqueCount:a.Unique});for(var d=0;d!=a.length;++d)if(null!=a[d]){var e=a[d],f="<si>";e.r?f+=e.r:(f+="<t",e.t||(e.t=""),e.t.match(et)&&(f+=' xml:space="preserve"'),f+=">"+bj(e.t)+"</t>"),f+="</si>",c[c.length]=f}return c.length>2&&(c[c.length]="</sst>",c[1]=c[1].replace("/>",">")),c.join("")}var ev=function(a,b){var c=!1;return null==b&&(c=!0,b=ch(15+4*a.t.length)),b.write_shift(1,0),cE(a.t,b),c?b.slice(0,b.l):b};function ew(a){if(void 0!==e)return e.utils.encode(n,a);for(var b=[],c=a.split(""),d=0;d<c.length;++d)b[d]=c[d].charCodeAt(0);return b}function ex(a,b){var c={};return c.Major=a.read_shift(2),c.Minor=a.read_shift(2),b>=4&&(a.l+=b-4),c}function ey(a,b){var c=a.l+b,d={};d.Flags=63&a.read_shift(4),a.l+=4,d.AlgID=a.read_shift(4);var e=!1;switch(d.AlgID){case 26126:case 26127:case 26128:e=36==d.Flags;break;case 26625:e=4==d.Flags;break;case 0:e=16==d.Flags||4==d.Flags||36==d.Flags;break;default:throw"Unrecognized encryption algorithm: "+d.AlgID}if(!e)throw Error("Encryption Flags/AlgID mismatch");return d.AlgIDHash=a.read_shift(4),d.KeySize=a.read_shift(4),d.ProviderType=a.read_shift(4),a.l+=8,d.CSPName=a.read_shift(c-a.l>>1,"utf16le"),a.l=c,d}function ez(a,b){var c={},d=a.l+b;return a.l+=4,c.Salt=a.slice(a.l,a.l+16),a.l+=16,c.Verifier=a.slice(a.l,a.l+16),a.l+=16,a.read_shift(4),c.VerifierHash=a.slice(a.l,d),a.l=d,c}function eA(a){var b,c,d=0,e=ew(a),f=e.length+1;for(c=1,(b=D(f))[0]=e.length;c!=f;++c)b[c]=e[c-1];for(c=f-1;c>=0;--c)d=((16384&d)!=0|d<<1&32767)^b[c];return 52811^d}var eB=function(){var a=[187,255,255,186,255,255,185,128,0,190,15,0,191,15,0],b=[57840,7439,52380,33984,4364,3600,61902,12606,6258,57657,54287,34041,10252,43370,20163],c=[44796,19929,39858,10053,20106,40212,10761,31585,63170,64933,60267,50935,40399,11199,17763,35526,1453,2906,5812,11624,23248,885,1770,3540,7080,14160,28320,56640,55369,41139,20807,41614,21821,43642,17621,28485,56970,44341,19019,38038,14605,29210,60195,50791,40175,10751,21502,43004,24537,18387,36774,3949,7898,15796,31592,63184,47201,24803,49606,37805,14203,28406,56812,17824,35648,1697,3394,6788,13576,27152,43601,17539,35078,557,1114,2228,4456,30388,60776,51953,34243,7079,14158,28316,14128,28256,56512,43425,17251,34502,7597,13105,26210,52420,35241,883,1766,3532,4129,8258,16516,33032,4657,9314,18628],d=function(a,b){var c;return((c=a^b)/2|128*c)&255},e=function(a){for(var d=b[a.length-1],e=104,f=a.length-1;f>=0;--f)for(var g=a[f],h=0;7!=h;++h)64&g&&(d^=c[e]),g*=2,--e;return d};return function(b){for(var c,f,g,h=ew(b),i=e(h),j=h.length,k=D(16),l=0;16!=l;++l)k[l]=0;for((1&j)==1&&(c=i>>8,k[j]=d(a[0],c),--j,c=255&i,f=h[h.length-1],k[j]=d(f,c));j>0;)--j,c=i>>8,k[j]=d(h[j],c),--j,c=255&i,k[j]=d(h[j],c);for(j=15,g=15-h.length;g>0;)c=i>>8,k[j]=d(a[g],c),--j,--g,c=255&i,k[j]=d(h[j],c),--j,--g;return k}}(),eC=function(a,b,c,d,e){var f,g;for(e||(e=b),d||(d=eB(a)),f=0;f!=b.length;++f)g=((g=b[f]^d[c])>>5|g<<3)&255,e[f]=g,++c;return[e,c,d]},eD=function(a){var b=0,c=eB(a);return function(a){var d=eC("",a,b,c);return b=d[1],d[0]}},eE=function(){function a(a,c){switch(c.type){case"base64":return b(A(a),c);case"binary":return b(a,c);case"buffer":return b(B&&Buffer.isBuffer(a)?a.toString("binary"):H(a),c);case"array":return b(aQ(a),c)}throw Error("Unrecognized type "+c.type)}function b(a,b){var c=(b||{}).dense?[]:{},d=a.match(/\\trowd.*?\\row\b/g);if(!d.length)throw Error("RTF missing table");var e={s:{c:0,r:0},e:{c:0,r:d.length-1}};return d.forEach(function(a,b){Array.isArray(c)&&(c[b]=[]);for(var d,f=/\\\w+\b/g,g=0,h=-1;d=f.exec(a);){if("\\cell"===d[0]){var i=a.slice(g,f.lastIndex-d[0].length);if(" "==i[0]&&(i=i.slice(1)),++h,i.length){var j={v:i,t:"s"};Array.isArray(c)?c[b][h]=j:c[cu({r:b,c:h})]=j}}g=f.lastIndex}h>e.e.c&&(e.e.c=h)}),c["!ref"]=cw(e),c}return{to_workbook:function(b,c){return cA(a(b,c),c)},to_sheet:a,from_sheet:function(a){for(var b,c=["{\\rtf1\\ansi"],d=cx(a["!ref"]),e=Array.isArray(a),f=d.s.r;f<=d.e.r;++f){c.push("\\trowd\\trautofit1");for(var g=d.s.c;g<=d.e.c;++g)c.push("\\cellx"+(g+1));for(c.push("\\pard\\intbl"),g=d.s.c;g<=d.e.c;++g){var h=cu({r:f,c:g});(b=e?(a[f]||[])[g]:a[h])&&(null!=b.v||b.f&&!b.F)&&(c.push(" "+(b.w||(cz(b),b.w))),c.push("\\cell"))}c.push("\\pard\\intbl\\row")}return c.join("")+"}"}}}();function eF(a){for(var b=0,c=1;3!=b;++b)c=256*c+(a[b]>255?255:a[b]<0?0:a[b]);return c.toString(16).toUpperCase().slice(1)}function eG(a,b){if(0===b)return a;var c,d=function(a){var b=a[0]/255,c=a[1]/255,d=a[2]/255,e=Math.max(b,c,d),f=Math.min(b,c,d),g=e-f;if(0===g)return[0,0,b];var h=0,i=0,j=e+f;switch(i=g/(j>1?2-j:j),e){case b:h=((c-d)/g+6)%6;break;case c:h=(d-b)/g+2;break;case d:h=(b-c)/g+4}return[h/6,i,j/2]}([parseInt((c=a.slice(+("#"===a[0])).slice(0,6)).slice(0,2),16),parseInt(c.slice(2,4),16),parseInt(c.slice(4,6),16)]);return b<0?d[2]=d[2]*(1+b):d[2]=1-(1-d[2])*(1-b),eF(function(a){var b,c=a[0],d=a[1],e=a[2],f=2*d*(e<.5?e:1-e),g=e-f/2,h=[g,g,g],i=6*c;if(0!==d)switch(0|i){case 0:case 6:b=f*i,h[0]+=f,h[1]+=b;break;case 1:b=f*(2-i),h[0]+=b,h[1]+=f;break;case 2:b=f*(i-2),h[1]+=f,h[2]+=b;break;case 3:b=f*(4-i),h[1]+=b,h[2]+=f;break;case 4:b=f*(i-4),h[2]+=f,h[0]+=b;break;case 5:b=f*(6-i),h[2]+=b,h[0]+=f}for(var j=0;3!=j;++j)h[j]=Math.round(255*h[j]);return h}(d))}var eH=6;function eI(a){return Math.floor((a+Math.round(128/eH)/256)*eH)}function eJ(a){return Math.floor((a-5)/eH*100+.5)/100}function eK(a){return Math.round((a*eH+5)/eH*256)/256}function eL(a){return eK(eJ(eI(a)))}function eM(a){var b=Math.abs(a-eL(a)),c=eH;if(b>.005)for(eH=1;eH<15;++eH)Math.abs(a-eL(a))<=b&&(b=Math.abs(a-eL(a)),c=eH);eH=c}function eN(a){a.width?(a.wpx=eI(a.width),a.wch=eJ(a.wpx),a.MDW=eH):a.wpx?(a.wch=eJ(a.wpx),a.width=eK(a.wch),a.MDW=eH):"number"==typeof a.wch&&(a.width=eK(a.wch),a.wpx=eI(a.width),a.MDW=eH),a.customWidth&&delete a.customWidth}function eO(a){return 96*a/96}function eP(a){return 96*a/96}var eQ={None:"none",Solid:"solid",Gray50:"mediumGray",Gray75:"darkGray",Gray25:"lightGray",HorzStripe:"darkHorizontal",VertStripe:"darkVertical",ReverseDiagStripe:"darkDown",DiagStripe:"darkUp",DiagCross:"darkGrid",ThickDiagCross:"darkTrellis",ThinHorzStripe:"lightHorizontal",ThinVertStripe:"lightVertical",ThinReverseDiagStripe:"lightDown",ThinHorzCross:"lightGrid"},eR=["numFmtId","fillId","fontId","borderId","xfId"],eS=["applyAlignment","applyBorder","applyFill","applyFont","applyNumberFormat","applyProtection","pivotButton","quotePrefix"],eT=function(){var a=/<(?:\w+:)?numFmts([^>]*)>[\S\s]*?<\/(?:\w+:)?numFmts>/,b=/<(?:\w+:)?cellXfs([^>]*)>[\S\s]*?<\/(?:\w+:)?cellXfs>/,c=/<(?:\w+:)?fills([^>]*)>[\S\s]*?<\/(?:\w+:)?fills>/,d=/<(?:\w+:)?fonts([^>]*)>[\S\s]*?<\/(?:\w+:)?fonts>/,e=/<(?:\w+:)?borders([^>]*)>[\S\s]*?<\/(?:\w+:)?borders>/;return function(f,g,h){var i,j,k,l,m,n,o,q,r,s,t,u,v,w={};return f?((v=(f=f.replace(/<!--([\s\S]*?)-->/mg,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"")).match(a))&&function(a,b,c){b.NumberFmt=[];for(var d=aC(U),e=0;e<d.length;++e)b.NumberFmt[d[e]]=U[d[e]];var f=a[0].match(a9);if(f)for(e=0;e<f.length;++e){var g=bc(f[e]);switch(bd(g[0])){case"<numFmts":case"</numFmts>":case"<numFmts/>":case"<numFmts>":case"</numFmt>":break;case"<numFmt":var h=bg(bt(g.formatCode)),i=parseInt(g.numFmtId,10);if(b.NumberFmt[i]=h,i>0){if(i>392){for(i=392;i>60&&null!=b.NumberFmt[i];--i);b.NumberFmt[i]=h}au(h,i)}break;default:if(c.WTF)throw Error("unrecognized "+g[0]+" in numFmts")}}}(v,w,h),(v=f.match(d))&&(i=v,w.Fonts=[],j={},k=!1,(i[0].match(a9)||[]).forEach(function(a){var b=bc(a);switch(bd(b[0])){case"<fonts":case"<fonts>":case"</fonts>":case"<font":case"<font>":case"<name/>":case"</name>":case"<sz/>":case"</sz>":case"<vertAlign/>":case"</vertAlign>":case"<family/>":case"</family>":case"<scheme/>":case"</scheme>":case"<color/>":case"</color>":case"<extLst":case"<extLst>":case"</extLst>":break;case"</font>":case"<font/>":w.Fonts.push(j),j={};break;case"<name":b.val&&(j.name=bt(b.val));break;case"<b":j.bold=b.val?bo(b.val):1;break;case"<b/>":j.bold=1;break;case"<i":j.italic=b.val?bo(b.val):1;break;case"<i/>":j.italic=1;break;case"<u":switch(b.val){case"none":j.underline=0;break;case"single":j.underline=1;break;case"double":j.underline=2;break;case"singleAccounting":j.underline=33;break;case"doubleAccounting":j.underline=34}break;case"<u/>":j.underline=1;break;case"<strike":j.strike=b.val?bo(b.val):1;break;case"<strike/>":j.strike=1;break;case"<outline":j.outline=b.val?bo(b.val):1;break;case"<outline/>":j.outline=1;break;case"<shadow":j.shadow=b.val?bo(b.val):1;break;case"<shadow/>":j.shadow=1;break;case"<condense":j.condense=b.val?bo(b.val):1;break;case"<condense/>":j.condense=1;break;case"<extend":j.extend=b.val?bo(b.val):1;break;case"<extend/>":j.extend=1;break;case"<sz":b.val&&(j.sz=+b.val);break;case"<vertAlign":b.val&&(j.vertAlign=b.val);break;case"<family":b.val&&(j.family=parseInt(b.val,10));break;case"<scheme":b.val&&(j.scheme=b.val);break;case"<charset":if("1"==b.val)break;b.codepage=p[parseInt(b.val,10)];break;case"<color":if(j.color||(j.color={}),b.auto&&(j.color.auto=bo(b.auto)),b.rgb)j.color.rgb=b.rgb.slice(-6);else if(b.indexed){j.color.index=parseInt(b.indexed,10);var c=cZ[j.color.index];81==j.color.index&&(c=cZ[1]),c||(c=cZ[1]),j.color.rgb=c[0].toString(16)+c[1].toString(16)+c[2].toString(16)}else b.theme&&(j.color.theme=parseInt(b.theme,10),b.tint&&(j.color.tint=parseFloat(b.tint)),b.theme&&g.themeElements&&g.themeElements.clrScheme&&(j.color.rgb=eG(g.themeElements.clrScheme[j.color.theme].rgb,j.color.tint||0)));break;case"<AlternateContent":case"<ext":k=!0;break;case"</AlternateContent>":case"</ext>":k=!1;break;default:if(h&&h.WTF&&!k)throw Error("unrecognized "+b[0]+" in fonts")}})),(v=f.match(c))&&(l=v,w.Fills=[],m={},n=!1,(l[0].match(a9)||[]).forEach(function(a){var b=bc(a);switch(bd(b[0])){case"<fills":case"<fills>":case"</fills>":case"</fill>":case"<gradientFill>":case"<patternFill/>":case"</patternFill>":case"<bgColor/>":case"</bgColor>":case"<fgColor/>":case"</fgColor>":case"<stop":case"<stop/>":case"</stop>":case"<color":case"<color/>":case"</color>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<fill>":case"<fill":case"<fill/>":m={},w.Fills.push(m);break;case"<gradientFill":case"</gradientFill>":w.Fills.push(m),m={};break;case"<patternFill":case"<patternFill>":b.patternType&&(m.patternType=b.patternType);break;case"<bgColor":m.bgColor||(m.bgColor={}),b.indexed&&(m.bgColor.indexed=parseInt(b.indexed,10)),b.theme&&(m.bgColor.theme=parseInt(b.theme,10)),b.tint&&(m.bgColor.tint=parseFloat(b.tint)),b.rgb&&(m.bgColor.rgb=b.rgb.slice(-6));break;case"<fgColor":m.fgColor||(m.fgColor={}),b.theme&&(m.fgColor.theme=parseInt(b.theme,10)),b.tint&&(m.fgColor.tint=parseFloat(b.tint)),null!=b.rgb&&(m.fgColor.rgb=b.rgb.slice(-6));break;case"<ext":n=!0;break;case"</ext>":n=!1;break;default:if(h&&h.WTF&&!n)throw Error("unrecognized "+b[0]+" in fills")}})),(v=f.match(e))&&(o=v,w.Borders=[],q={},r=!1,(o[0].match(a9)||[]).forEach(function(a){var b=bc(a);switch(bd(b[0])){case"<borders":case"<borders>":case"</borders>":case"</border>":case"<left/>":case"<left":case"<left>":case"</left>":case"<right/>":case"<right":case"<right>":case"</right>":case"<top/>":case"<top":case"<top>":case"</top>":case"<bottom/>":case"<bottom":case"<bottom>":case"</bottom>":case"<diagonal":case"<diagonal>":case"<diagonal/>":case"</diagonal>":case"<horizontal":case"<horizontal>":case"<horizontal/>":case"</horizontal>":case"<vertical":case"<vertical>":case"<vertical/>":case"</vertical>":case"<start":case"<start>":case"<start/>":case"</start>":case"<end":case"<end>":case"<end/>":case"</end>":case"<color":case"<color>":case"<color/>":case"</color>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<border":case"<border>":case"<border/>":q={},b.diagonalUp&&(q.diagonalUp=bo(b.diagonalUp)),b.diagonalDown&&(q.diagonalDown=bo(b.diagonalDown)),w.Borders.push(q);break;case"<ext":r=!0;break;case"</ext>":r=!1;break;default:if(h&&h.WTF&&!r)throw Error("unrecognized "+b[0]+" in borders")}})),(v=f.match(b))&&(s=v,w.CellXf=[],u=!1,(s[0].match(a9)||[]).forEach(function(a){var b=bc(a),c=0;switch(bd(b[0])){case"<cellXfs":case"<cellXfs>":case"<cellXfs/>":case"</cellXfs>":case"</xf>":case"</alignment>":case"<protection":case"</protection>":case"<protection/>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<xf":case"<xf/>":for(t=b,delete t[0],c=0;c<eR.length;++c)t[eR[c]]&&(t[eR[c]]=parseInt(t[eR[c]],10));for(c=0;c<eS.length;++c)t[eS[c]]&&(t[eS[c]]=bo(t[eS[c]]));if(w.NumberFmt&&t.numFmtId>392){for(c=392;c>60;--c)if(w.NumberFmt[t.numFmtId]==w.NumberFmt[c]){t.numFmtId=c;break}}w.CellXf.push(t);break;case"<alignment":case"<alignment/>":var d={};b.vertical&&(d.vertical=b.vertical),b.horizontal&&(d.horizontal=b.horizontal),null!=b.textRotation&&(d.textRotation=b.textRotation),b.indent&&(d.indent=b.indent),b.wrapText&&(d.wrapText=bo(b.wrapText)),t.alignment=d;break;case"<AlternateContent":case"<ext":u=!0;break;case"</AlternateContent>":case"</ext>":u=!1;break;default:if(h&&h.WTF&&!u)throw Error("unrecognized "+b[0]+" in cellXfs")}})),w):w}}();function eU(a,b){var c,d,e,f,g,h=[a6,bE("styleSheet",null,{xmlns:bJ[0],"xmlns:vt":bI.vt})];return a.SSF&&null!=(c=a.SSF,d=["<numFmts>"],[[5,8],[23,26],[41,44],[50,392]].forEach(function(a){for(var b=a[0];b<=a[1];++b)null!=c[b]&&(d[d.length]=bE("numFmt",null,{numFmtId:b,formatCode:bj(c[b])}))}),g=1===d.length?"":(d[d.length]="</numFmts>",d[0]=bE("numFmts",null,{count:d.length-2}).replace("/>",">"),d.join("")))&&(h[h.length]=g),h[h.length]='<fonts count="1"><font><sz val="12"/><color theme="1"/><name val="Calibri"/><family val="2"/><scheme val="minor"/></font></fonts>',h[h.length]='<fills count="2"><fill><patternFill patternType="none"/></fill><fill><patternFill patternType="gray125"/></fill></fills>',h[h.length]='<borders count="1"><border><left/><right/><top/><bottom/><diagonal/></border></borders>',h[h.length]='<cellStyleXfs count="1"><xf numFmtId="0" fontId="0" fillId="0" borderId="0"/></cellStyleXfs>',e=b.cellXfs,(f=[])[f.length]=bE("cellXfs",null),e.forEach(function(a){f[f.length]=bE("xf",null,a)}),f[f.length]="</cellXfs>",(g=2===f.length?"":(f[0]=bE("cellXfs",null,{count:f.length-2}).replace("/>",">"),f.join("")))&&(h[h.length]=g),h[h.length]='<cellStyles count="1"><cellStyle name="Normal" xfId="0" builtinId="0"/></cellStyles>',h[h.length]='<dxfs count="0"/>',h[h.length]='<tableStyles count="0" defaultTableStyle="TableStyleMedium9" defaultPivotStyle="PivotStyleMedium4"/>',h.length>2&&(h[h.length]="</styleSheet>",h[1]=h[1].replace("/>",">")),h.join("")}var eV=["none","solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"];function eW(a,b){b||(b=ch(84)),g||(g=aE(eV));var c=g[a.patternType];null==c&&(c=40),b.write_shift(4,c);var d=0;if(40!=c)for(cS({auto:1},b),cS({auto:1},b);d<12;++d)b.write_shift(4,0);else{for(;d<4;++d)b.write_shift(4,0);for(;d<12;++d)b.write_shift(4,0)}return b.length>b.l?b.slice(0,b.l):b}function eX(a,b,c){return c||(c=ch(16)),c.write_shift(2,b||0),c.write_shift(2,a.numFmtId||0),c.write_shift(2,0),c.write_shift(2,0),c.write_shift(2,0),c.write_shift(1,0),c.write_shift(1,0),c.write_shift(1,0),c.write_shift(1,0),c.write_shift(1,0),c.write_shift(1,0),c}function eY(a,b){return b||(b=ch(10)),b.write_shift(1,0),b.write_shift(1,0),b.write_shift(4,0),b.write_shift(4,0),b}var eZ=["</a:lt1>","</a:dk1>","</a:lt2>","</a:dk2>","</a:accent1>","</a:accent2>","</a:accent3>","</a:accent4>","</a:accent5>","</a:accent6>","</a:hlink>","</a:folHlink>"];function e$(a,b,c){b.themeElements.clrScheme=[];var d={};(a[0].match(a9)||[]).forEach(function(a){var e=bc(a);switch(e[0]){case"<a:clrScheme":case"</a:clrScheme>":break;case"<a:srgbClr":d.rgb=e.val;break;case"<a:sysClr":d.rgb=e.lastClr;break;case"<a:dk1>":case"</a:dk1>":case"<a:lt1>":case"</a:lt1>":case"<a:dk2>":case"</a:dk2>":case"<a:lt2>":case"</a:lt2>":case"<a:accent1>":case"</a:accent1>":case"<a:accent2>":case"</a:accent2>":case"<a:accent3>":case"</a:accent3>":case"<a:accent4>":case"</a:accent4>":case"<a:accent5>":case"</a:accent5>":case"<a:accent6>":case"</a:accent6>":case"<a:hlink>":case"</a:hlink>":case"<a:folHlink>":case"</a:folHlink>":"/"===e[0].charAt(1)?(b.themeElements.clrScheme[eZ.indexOf(e[0])]=d,d={}):d.name=e[0].slice(3,e[0].length-1);break;default:if(c&&c.WTF)throw Error("Unrecognized "+e[0]+" in clrScheme")}})}function e_(){}function e0(){}var e1=/<a:clrScheme([^>]*)>[\s\S]*<\/a:clrScheme>/,e2=/<a:fontScheme([^>]*)>[\s\S]*<\/a:fontScheme>/,e3=/<a:fmtScheme([^>]*)>[\s\S]*<\/a:fmtScheme>/,e4=/<a:themeElements([^>]*)>[\s\S]*<\/a:themeElements>/;function e5(a,b){a&&0!==a.length||(a=e6());var c,d,e,f={};if(!(e=a.match(e4)))throw Error("themeElements not found in theme");return c=e[0],f.themeElements={},[["clrScheme",e1,e$],["fontScheme",e2,e_],["fmtScheme",e3,e0]].forEach(function(a){if(!(d=c.match(a[1])))throw Error(a[0]+" not found in themeElements");a[2](d,f,b)}),f.raw=a,f}function e6(a,b){if(b&&b.themeXLSX)return b.themeXLSX;if(a&&"string"==typeof a.raw)return a.raw;var c=[a6];return c[c.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">',c[c.length]="<a:themeElements>",c[c.length]='<a:clrScheme name="Office">',c[c.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>',c[c.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>',c[c.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>',c[c.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>',c[c.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>',c[c.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>',c[c.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>',c[c.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>',c[c.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>',c[c.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>',c[c.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>',c[c.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>',c[c.length]="</a:clrScheme>",c[c.length]='<a:fontScheme name="Office">',c[c.length]="<a:majorFont>",c[c.length]='<a:latin typeface="Cambria"/>',c[c.length]='<a:ea typeface=""/>',c[c.length]='<a:cs typeface=""/>',c[c.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',c[c.length]='<a:font script="Hang" typeface="맑은 고딕"/>',c[c.length]='<a:font script="Hans" typeface="宋体"/>',c[c.length]='<a:font script="Hant" typeface="新細明體"/>',c[c.length]='<a:font script="Arab" typeface="Times New Roman"/>',c[c.length]='<a:font script="Hebr" typeface="Times New Roman"/>',c[c.length]='<a:font script="Thai" typeface="Tahoma"/>',c[c.length]='<a:font script="Ethi" typeface="Nyala"/>',c[c.length]='<a:font script="Beng" typeface="Vrinda"/>',c[c.length]='<a:font script="Gujr" typeface="Shruti"/>',c[c.length]='<a:font script="Khmr" typeface="MoolBoran"/>',c[c.length]='<a:font script="Knda" typeface="Tunga"/>',c[c.length]='<a:font script="Guru" typeface="Raavi"/>',c[c.length]='<a:font script="Cans" typeface="Euphemia"/>',c[c.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',c[c.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',c[c.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',c[c.length]='<a:font script="Thaa" typeface="MV Boli"/>',c[c.length]='<a:font script="Deva" typeface="Mangal"/>',c[c.length]='<a:font script="Telu" typeface="Gautami"/>',c[c.length]='<a:font script="Taml" typeface="Latha"/>',c[c.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',c[c.length]='<a:font script="Orya" typeface="Kalinga"/>',c[c.length]='<a:font script="Mlym" typeface="Kartika"/>',c[c.length]='<a:font script="Laoo" typeface="DokChampa"/>',c[c.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',c[c.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',c[c.length]='<a:font script="Viet" typeface="Times New Roman"/>',c[c.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',c[c.length]='<a:font script="Geor" typeface="Sylfaen"/>',c[c.length]="</a:majorFont>",c[c.length]="<a:minorFont>",c[c.length]='<a:latin typeface="Calibri"/>',c[c.length]='<a:ea typeface=""/>',c[c.length]='<a:cs typeface=""/>',c[c.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',c[c.length]='<a:font script="Hang" typeface="맑은 고딕"/>',c[c.length]='<a:font script="Hans" typeface="宋体"/>',c[c.length]='<a:font script="Hant" typeface="新細明體"/>',c[c.length]='<a:font script="Arab" typeface="Arial"/>',c[c.length]='<a:font script="Hebr" typeface="Arial"/>',c[c.length]='<a:font script="Thai" typeface="Tahoma"/>',c[c.length]='<a:font script="Ethi" typeface="Nyala"/>',c[c.length]='<a:font script="Beng" typeface="Vrinda"/>',c[c.length]='<a:font script="Gujr" typeface="Shruti"/>',c[c.length]='<a:font script="Khmr" typeface="DaunPenh"/>',c[c.length]='<a:font script="Knda" typeface="Tunga"/>',c[c.length]='<a:font script="Guru" typeface="Raavi"/>',c[c.length]='<a:font script="Cans" typeface="Euphemia"/>',c[c.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',c[c.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',c[c.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',c[c.length]='<a:font script="Thaa" typeface="MV Boli"/>',c[c.length]='<a:font script="Deva" typeface="Mangal"/>',c[c.length]='<a:font script="Telu" typeface="Gautami"/>',c[c.length]='<a:font script="Taml" typeface="Latha"/>',c[c.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',c[c.length]='<a:font script="Orya" typeface="Kalinga"/>',c[c.length]='<a:font script="Mlym" typeface="Kartika"/>',c[c.length]='<a:font script="Laoo" typeface="DokChampa"/>',c[c.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',c[c.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',c[c.length]='<a:font script="Viet" typeface="Arial"/>',c[c.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',c[c.length]='<a:font script="Geor" typeface="Sylfaen"/>',c[c.length]="</a:minorFont>",c[c.length]="</a:fontScheme>",c[c.length]='<a:fmtScheme name="Office">',c[c.length]="<a:fillStyleLst>",c[c.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',c[c.length]='<a:gradFill rotWithShape="1">',c[c.length]="<a:gsLst>",c[c.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',c[c.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',c[c.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',c[c.length]="</a:gsLst>",c[c.length]='<a:lin ang="16200000" scaled="1"/>',c[c.length]="</a:gradFill>",c[c.length]='<a:gradFill rotWithShape="1">',c[c.length]="<a:gsLst>",c[c.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>',c[c.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',c[c.length]="</a:gsLst>",c[c.length]='<a:lin ang="16200000" scaled="0"/>',c[c.length]="</a:gradFill>",c[c.length]="</a:fillStyleLst>",c[c.length]="<a:lnStyleLst>",c[c.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>',c[c.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',c[c.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',c[c.length]="</a:lnStyleLst>",c[c.length]="<a:effectStyleLst>",c[c.length]="<a:effectStyle>",c[c.length]="<a:effectLst>",c[c.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>',c[c.length]="</a:effectLst>",c[c.length]="</a:effectStyle>",c[c.length]="<a:effectStyle>",c[c.length]="<a:effectLst>",c[c.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',c[c.length]="</a:effectLst>",c[c.length]="</a:effectStyle>",c[c.length]="<a:effectStyle>",c[c.length]="<a:effectLst>",c[c.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',c[c.length]="</a:effectLst>",c[c.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>',c[c.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>',c[c.length]="</a:effectStyle>",c[c.length]="</a:effectStyleLst>",c[c.length]="<a:bgFillStyleLst>",c[c.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',c[c.length]='<a:gradFill rotWithShape="1">',c[c.length]="<a:gsLst>",c[c.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',c[c.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',c[c.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>',c[c.length]="</a:gsLst>",c[c.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>',c[c.length]="</a:gradFill>",c[c.length]='<a:gradFill rotWithShape="1">',c[c.length]="<a:gsLst>",c[c.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',c[c.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>',c[c.length]="</a:gsLst>",c[c.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>',c[c.length]="</a:gradFill>",c[c.length]="</a:bgFillStyleLst>",c[c.length]="</a:fmtScheme>",c[c.length]="</a:themeElements>",c[c.length]="<a:objectDefaults>",c[c.length]="<a:spDef>",c[c.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>',c[c.length]="</a:spDef>",c[c.length]="<a:lnDef>",c[c.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>',c[c.length]="</a:lnDef>",c[c.length]="</a:objectDefaults>",c[c.length]="<a:extraClrSchemeLst/>",c[c.length]="</a:theme>",c.join("")}function e7(){var a=[a6];return a.push('<metadata xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:xlrd="http://schemas.microsoft.com/office/spreadsheetml/2017/richdata" xmlns:xda="http://schemas.microsoft.com/office/spreadsheetml/2017/dynamicarray">\n  <metadataTypes count="1">\n    <metadataType name="XLDAPR" minSupportedVersion="120000" copy="1" pasteAll="1" pasteValues="1" merge="1" splitFirst="1" rowColShift="1" clearFormats="1" clearComments="1" assign="1" coerce="1" cellMeta="1"/>\n  </metadataTypes>\n  <futureMetadata name="XLDAPR" count="1">\n    <bk>\n      <extLst>\n        <ext uri="{bdbb8cdc-fa1e-496e-a857-3c3f30c029c3}">\n          <xda:dynamicArrayProperties fDynamic="1" fCollapsed="0"/>\n        </ext>\n      </extLst>\n    </bk>\n  </futureMetadata>\n  <cellMetadata count="1">\n    <bk>\n      <rc t="1" v="0"/>\n    </bk>\n  </cellMetadata>\n</metadata>'),a.join("")}var e8=1024;function e9(a,b){for(var c=[21600,21600],d=["m0,0l0",c[1],c[0],c[1],c[0],"0xe"].join(","),e=[bE("xml",null,{"xmlns:v":bK.v,"xmlns:o":bK.o,"xmlns:x":bK.x,"xmlns:mv":bK.mv}).replace(/\/>/,">"),bE("o:shapelayout",bE("o:idmap",null,{"v:ext":"edit",data:a}),{"v:ext":"edit"}),bE("v:shapetype",[bE("v:stroke",null,{joinstyle:"miter"}),bE("v:path",null,{gradientshapeok:"t","o:connecttype":"rect"})].join(""),{id:"_x0000_t202","o:spt":202,coordsize:c.join(","),path:d})];e8<1e3*a;)e8+=1e3;return b.forEach(function(a){var b=ct(a[0]),c={color2:"#BEFF82",type:"gradient"};"gradient"==c.type&&(c.angle="-180");var d="gradient"==c.type?bE("o:fill",null,{type:"gradientUnscaled","v:ext":"view"}):null,f=bE("v:fill",d,c);++e8,e=e.concat(["<v:shape"+bD({id:"_x0000_s"+e8,type:"#_x0000_t202",style:"position:absolute; margin-left:80pt;margin-top:5pt;width:104pt;height:64pt;z-index:10"+(a[1].hidden?";visibility:hidden":""),fillcolor:"#ECFAD4",strokecolor:"#edeaa1"})+">",f,bE("v:shadow",null,{on:"t",obscured:"t"}),bE("v:path",null,{"o:connecttype":"none"}),'<v:textbox><div style="text-align:left"></div></v:textbox>','<x:ClientData ObjectType="Note">',"<x:MoveWithCells/>","<x:SizeWithCells/>",bC("x:Anchor",[b.c+1,0,b.r+1,0,b.c+3,20,b.r+5,20].join(",")),bC("x:AutoFill","False"),bC("x:Row",String(b.r)),bC("x:Column",String(b.c)),a[1].hidden?"":"<x:Visible/>","</x:ClientData>","</v:shape>"])}),e.push("</xml>"),e.join("")}function fa(a,b,c,d){var e,f=Array.isArray(a);b.forEach(function(b){var g=ct(b.ref);if(f?(a[g.r]||(a[g.r]=[]),e=a[g.r][g.c]):e=a[b.ref],!e){e={t:"z"},f?a[g.r][g.c]=e:a[b.ref]=e;var h=cx(a["!ref"]||"BDWGO1000001:A1");h.s.r>g.r&&(h.s.r=g.r),h.e.r<g.r&&(h.e.r=g.r),h.s.c>g.c&&(h.s.c=g.c),h.e.c<g.c&&(h.e.c=g.c);var i=cw(h);i!==a["!ref"]&&(a["!ref"]=i)}e.c||(e.c=[]);var j={a:b.author,t:b.t,r:b.r,T:c};b.h&&(j.h=b.h);for(var k=e.c.length-1;k>=0;--k){if(!c&&e.c[k].T)return;c&&!e.c[k].T&&e.c.splice(k,1)}if(c&&d){for(k=0;k<d.length;++k)if(j.a==d[k].id){j.a=d[k].name||j.a;break}}e.c.push(j)})}function fb(a){var b=[a6,bE("comments",null,{xmlns:bJ[0]})],c=[];return b.push("<authors>"),a.forEach(function(a){a[1].forEach(function(a){var d=bj(a.a);-1==c.indexOf(d)&&(c.push(d),b.push("<author>"+d+"</author>")),a.T&&a.ID&&-1==c.indexOf("tc="+a.ID)&&(c.push("tc="+a.ID),b.push("<author>tc="+a.ID+"</author>"))})}),0==c.length&&(c.push("SheetJ5"),b.push("<author>SheetJ5</author>")),b.push("</authors>"),b.push("<commentList>"),a.forEach(function(a){var d=0,e=[];if(a[1][0]&&a[1][0].T&&a[1][0].ID?d=c.indexOf("tc="+a[1][0].ID):a[1].forEach(function(a){a.a&&(d=c.indexOf(bj(a.a))),e.push(a.t||"")}),b.push('<comment ref="'+a[0]+'" authorId="'+d+'"><text>'),e.length<=1)b.push(bC("t",bj(e[0]||"")));else{for(var f="Comment:\n    "+e[0]+"\n",g=1;g<e.length;++g)f+="Reply:\n    "+e[g]+"\n";b.push(bC("t",bj(f)))}b.push("</text></comment>")}),b.push("</commentList>"),b.length>2&&(b[b.length]="</comments>",b[1]=b[1].replace("/>",">")),b.join("")}var fc=["xlsb","xlsm","xlam","biff8","xla"],fd=function(){var a=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g,b={r:0,c:0};function c(a,c,d,e){var f=!1,g=!1;0==d.length?g=!0:"["==d.charAt(0)&&(g=!0,d=d.slice(1,-1)),0==e.length?f=!0:"["==e.charAt(0)&&(f=!0,e=e.slice(1,-1));var h=d.length>0?0|parseInt(d,10):0,i=e.length>0?0|parseInt(e,10):0;return f?i+=b.c:--i,g?h+=b.r:--h,c+(f?"":"$")+cs(i)+(g?"":"$")+cq(h)}return function(d,e){return b=e,d.replace(a,c)}}(),fe=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g,ff=function(a,b){return a.replace(fe,function(a,c,d,e,f,g){var h=cr(e)-(d?0:b.c),i=cp(g)-(f?0:b.r);return c+"R"+(0==i?"":f?i+1:"["+i+"]")+"C"+(0==h?"":d?h+1:"["+h+"]")})};function fg(a,b){return a.replace(fe,function(a,c,d,e,f,g){return c+("$"==d?d+e:cs(cr(e)+b.c))+("$"==f?f+g:cq(cp(g)+b.r))})}function fh(a){return a.replace(/_xlfn\./g,"")}function fi(a){a.l+=1}function fj(a,b){var c=a.read_shift(1==b?1:2);return[16383&c,c>>14&1,c>>15&1]}function fk(a,b,c){var d=2;if(c)if(c.biff>=2&&c.biff<=5)return fl(a,b,c);else 12==c.biff&&(d=4);var e=a.read_shift(d),f=a.read_shift(d),g=fj(a,2),h=fj(a,2);return{s:{r:e,c:g[0],cRel:g[1],rRel:g[2]},e:{r:f,c:h[0],cRel:h[1],rRel:h[2]}}}function fl(a){var b=fj(a,2),c=fj(a,2),d=a.read_shift(1),e=a.read_shift(1);return{s:{r:b[0],c:d,cRel:b[1],rRel:b[2]},e:{r:c[0],c:e,cRel:c[1],rRel:c[2]}}}function fm(a,b,c){if(c&&c.biff>=2&&c.biff<=5){var d,e,f;return e=fj(d=a,2),f=d.read_shift(1),{r:e[0],c:f,cRel:e[1],rRel:e[2]}}var g=a.read_shift(c&&12==c.biff?4:2),h=fj(a,2);return{r:g,c:h[0],cRel:h[1],rRel:h[2]}}function fn(a){var b=1&a[a.l+1];return a.l+=4,[b,1]}function fo(a){return[a.read_shift(1),a.read_shift(1)]}function fp(a,b,c){var d;return a.l+=2,[{r:a.read_shift(2),c:255&(d=a.read_shift(2)),fQuoted:!!(16384&d),cRel:d>>15,rRel:d>>15}]}function fq(a){return a.l+=6,[]}function fr(a){return a.l+=2,[dF(a),1&a.read_shift(2)]}var fs=["Data","All","Headers","??","?Data2","??","?DataHeaders","??","Totals","??","??","??","?DataTotals","??","??","??","?Current"],ft={1:{n:"PtgExp",f:function(a,b,c){return(a.l++,c&&12==c.biff)?[a.read_shift(4,"i"),0]:[a.read_shift(2),a.read_shift(c&&2==c.biff?1:2)]}},2:{n:"PtgTbl",f:cg},3:{n:"PtgAdd",f:fi},4:{n:"PtgSub",f:fi},5:{n:"PtgMul",f:fi},6:{n:"PtgDiv",f:fi},7:{n:"PtgPower",f:fi},8:{n:"PtgConcat",f:fi},9:{n:"PtgLt",f:fi},10:{n:"PtgLe",f:fi},11:{n:"PtgEq",f:fi},12:{n:"PtgGe",f:fi},13:{n:"PtgGt",f:fi},14:{n:"PtgNe",f:fi},15:{n:"PtgIsect",f:fi},16:{n:"PtgUnion",f:fi},17:{n:"PtgRange",f:fi},18:{n:"PtgUplus",f:fi},19:{n:"PtgUminus",f:fi},20:{n:"PtgPercent",f:fi},21:{n:"PtgParen",f:fi},22:{n:"PtgMissArg",f:fi},23:{n:"PtgStr",f:function(a,b,c){return a.l++,dJ(a,b-1,c)}},26:{n:"PtgSheet",f:function(a,b,c){return a.l+=5,a.l+=2,a.l+=2==c.biff?1:4,["PTGSHEET"]}},27:{n:"PtgEndSheet",f:function(a,b,c){return a.l+=2==c.biff?4:5,["PTGENDSHEET"]}},28:{n:"PtgErr",f:function(a){return a.l++,c$[a.read_shift(1)]}},29:{n:"PtgBool",f:function(a){return a.l++,0!==a.read_shift(1)}},30:{n:"PtgInt",f:function(a){return a.l++,a.read_shift(2)}},31:{n:"PtgNum",f:function(a){return a.l++,cQ(a,8)}},32:{n:"PtgArray",f:function(a,b,c){var d=(96&a[a.l++])>>5;return a.l+=2==c.biff?6:12==c.biff?14:7,[d]}},33:{n:"PtgFunc",f:function(a,b,c){var d=(96&a[a.l])>>5;a.l+=1;var e=a.read_shift(c&&c.biff<=3?1:2);return[fH[e],fG[e],d]}},34:{n:"PtgFuncVar",f:function(a,b,c){var d,e=a[a.l++],f=a.read_shift(1),g=c&&c.biff<=3?[88==e?-1:0,a.read_shift(1)]:[(d=a)[d.l+1]>>7,32767&d.read_shift(2)];return[f,(0===g[0]?fG:fF)[g[1]]]}},35:{n:"PtgName",f:function(a,b,c){var d=a.read_shift(1)>>>5&3,e=!c||c.biff>=8?4:2,f=a.read_shift(e);switch(c.biff){case 2:a.l+=5;break;case 3:case 4:a.l+=8;break;case 5:a.l+=12}return[d,0,f]}},36:{n:"PtgRef",f:function(a,b,c){var d=(96&a[a.l])>>5;return a.l+=1,[d,fm(a,0,c)]}},37:{n:"PtgArea",f:function(a,b,c){return[(96&a[a.l++])>>5,fk(a,c.biff>=2&&c.biff<=5?6:8,c)]}},38:{n:"PtgMemArea",f:function(a,b,c){var d=a.read_shift(1)>>>5&3;return a.l+=c&&2==c.biff?3:4,[d,a.read_shift(c&&2==c.biff?1:2)]}},39:{n:"PtgMemErr",f:cg},40:{n:"PtgMemNoMem",f:cg},41:{n:"PtgMemFunc",f:function(a,b,c){return[a.read_shift(1)>>>5&3,a.read_shift(c&&2==c.biff?1:2)]}},42:{n:"PtgRefErr",f:function(a,b,c){var d=a.read_shift(1)>>>5&3;return a.l+=4,c.biff<8&&a.l--,12==c.biff&&(a.l+=2),[d]}},43:{n:"PtgAreaErr",f:function(a,b,c){var d=(96&a[a.l++])>>5;return a.l+=c&&c.biff>8?12:c.biff<8?6:8,[d]}},44:{n:"PtgRefN",f:function(a,b,c){var d=(96&a[a.l])>>5;return a.l+=1,[d,function(a,b,c){var d,e,f,g,h,i=c&&c.biff?c.biff:8;if(i>=2&&i<=5){return e=(d=a).read_shift(2),f=d.read_shift(1),g=(32768&e)>>15,h=(16384&e)>>14,e&=16383,1==g&&e>=8192&&(e-=16384),1==h&&f>=128&&(f-=256),{r:e,c:f,cRel:h,rRel:g}}var j=a.read_shift(i>=12?4:2),k=a.read_shift(2),l=(16384&k)>>14,m=(32768&k)>>15;if(k&=16383,1==m)for(;j>524287;)j-=1048576;if(1==l)for(;k>8191;)k-=16384;return{r:j,c:k,cRel:l,rRel:m}}(a,0,c)]}},45:{n:"PtgAreaN",f:function(a,b,c){return[(96&a[a.l++])>>5,function(a,b,c){if(c.biff<8)return fl(a,b,c);var d=a.read_shift(12==c.biff?4:2),e=a.read_shift(12==c.biff?4:2),f=fj(a,2),g=fj(a,2);return{s:{r:d,c:f[0],cRel:f[1],rRel:f[2]},e:{r:e,c:g[0],cRel:g[1],rRel:g[2]}}}(a,b-1,c)]}},46:{n:"PtgMemAreaN",f:function(a){return[a.read_shift(1)>>>5&3,a.read_shift(2)]}},47:{n:"PtgMemNoMemN",f:function(a){return[a.read_shift(1)>>>5&3,a.read_shift(2)]}},57:{n:"PtgNameX",f:function(a,b,c){var d,e,f,g;return 5==c.biff?(e=(d=a).read_shift(1)>>>5&3,f=d.read_shift(2,"i"),d.l+=8,g=d.read_shift(2),d.l+=12,[e,f,g]):[a.read_shift(1)>>>5&3,a.read_shift(2),a.read_shift(4)]}},58:{n:"PtgRef3d",f:function(a,b,c){var d=(96&a[a.l])>>5;a.l+=1;var e=a.read_shift(2);return c&&5==c.biff&&(a.l+=12),[d,e,fm(a,0,c)]}},59:{n:"PtgArea3d",f:function(a,b,c){var d=(96&a[a.l++])>>5,e=a.read_shift(2,"i"),f=8;if(c)switch(c.biff){case 5:a.l+=12,f=6;break;case 12:f=12}return[d,e,fk(a,f,c)]}},60:{n:"PtgRefErr3d",f:function(a,b,c){var d=(96&a[a.l++])>>5,e=a.read_shift(2),f=4;if(c)switch(c.biff){case 5:f=15;break;case 12:f=6}return a.l+=f,[d,e]}},61:{n:"PtgAreaErr3d",f:function(a,b,c){var d=(96&a[a.l++])>>5,e=a.read_shift(2),f=8;if(c)switch(c.biff){case 5:a.l+=12,f=6;break;case 12:f=12}return a.l+=f,[d,e]}},255:{}},fu={64:32,96:32,65:33,97:33,66:34,98:34,67:35,99:35,68:36,100:36,69:37,101:37,70:38,102:38,71:39,103:39,72:40,104:40,73:41,105:41,74:42,106:42,75:43,107:43,76:44,108:44,77:45,109:45,78:46,110:46,79:47,111:47,88:34,120:34,89:57,121:57,90:58,122:58,91:59,123:59,92:60,124:60,93:61,125:61},fv={1:{n:"PtgElfLel",f:fr},2:{n:"PtgElfRw",f:fp},3:{n:"PtgElfCol",f:fp},6:{n:"PtgElfRwV",f:fp},7:{n:"PtgElfColV",f:fp},10:{n:"PtgElfRadical",f:fp},11:{n:"PtgElfRadicalS",f:fq},13:{n:"PtgElfColS",f:fq},15:{n:"PtgElfColSV",f:fq},16:{n:"PtgElfRadicalLel",f:fr},25:{n:"PtgList",f:function(a){a.l+=2;var b=a.read_shift(2),c=a.read_shift(2),d=a.read_shift(4),e=a.read_shift(2),f=a.read_shift(2),g=fs[c>>2&31];return{ixti:b,coltype:3&c,rt:g,idx:d,c:e,C:f}}},29:{n:"PtgSxName",f:function(a){return a.l+=2,[a.read_shift(4)]}},255:{}},fw={0:{n:"PtgAttrNoop",f:function(a){return a.l+=4,[0,0]}},1:{n:"PtgAttrSemi",f:function(a,b,c){var d=255&a[a.l+1]?1:0;return a.l+=c&&2==c.biff?3:4,[d]}},2:{n:"PtgAttrIf",f:function(a,b,c){var d=255&a[a.l+1]?1:0;return a.l+=2,[d,a.read_shift(c&&2==c.biff?1:2)]}},4:{n:"PtgAttrChoose",f:function(a,b,c){a.l+=2;for(var d=a.read_shift(c&&2==c.biff?1:2),e=[],f=0;f<=d;++f)e.push(a.read_shift(c&&2==c.biff?1:2));return e}},8:{n:"PtgAttrGoto",f:function(a,b,c){var d=255&a[a.l+1]?1:0;return a.l+=2,[d,a.read_shift(c&&2==c.biff?1:2)]}},16:{n:"PtgAttrSum",f:function(a,b,c){a.l+=c&&2==c.biff?3:4}},32:{n:"PtgAttrBaxcel",f:fn},33:{n:"PtgAttrBaxcel",f:fn},64:{n:"PtgAttrSpace",f:function(a){return a.read_shift(2),fo(a,2)}},65:{n:"PtgAttrSpaceSemi",f:function(a){return a.read_shift(2),fo(a,2)}},128:{n:"PtgAttrIfError",f:function(a){var b=255&a[a.l+1]?1:0;return a.l+=2,[b,a.read_shift(2)]}},255:{}};function fx(a,b,c,d){if(d.biff<8)return e=b,void(a.l+=e);for(var e,f,g=a.l+b,h=[],i=0;i!==c.length;++i)switch(c[i][0]){case"PtgArray":c[i][1]=function(a,b,c){var d=0,e=0;12==c.biff?(d=a.read_shift(4),e=a.read_shift(4)):(e=1+a.read_shift(1),d=1+a.read_shift(2)),c.biff>=2&&c.biff<8&&(--d,0==--e&&(e=256));for(var f=0,g=[];f!=d&&(g[f]=[]);++f)for(var h=0;h!=e;++h)g[f][h]=function(a,b){var c=[a.read_shift(1)];if(12==b)switch(c[0]){case 2:c[0]=4;break;case 4:c[0]=16;break;case 0:c[0]=1;break;case 1:c[0]=2}switch(c[0]){case 4:c[1]=dD(a,1)?"TRUE":"FALSE",12!=b&&(a.l+=7);break;case 37:case 16:c[1]=c$[a[a.l]],a.l+=12==b?4:8;break;case 0:a.l+=8;break;case 1:c[1]=cQ(a,8);break;case 2:c[1]=dM(a,0,{biff:b>0&&b<8?2:b});break;default:throw Error("Bad SerAr: "+c[0])}return c}(a,c.biff);return g}(a,0,d),h.push(c[i][1]);break;case"PtgMemArea":c[i][2]=function(a,b,c){for(var d=a.read_shift(12==c.biff?4:2),e=[],f=0;f!=d;++f)e.push((12==c.biff?cO:dV)(a,8));return e}(a,c[i][1],d),h.push(c[i][2]);break;case"PtgExp":d&&12==d.biff&&(c[i][1][1]=a.read_shift(4),h.push(c[i][1]));break;case"PtgList":case"PtgElfRadicalS":case"PtgElfColS":case"PtgElfColSV":throw"Unsupported "+c[i][0]}return 0!=(b=g-a.l)&&h.push((f=b,void(a.l+=f))),h}function fy(a,b,c){for(var d,e,f,g=a.l+b,h=[];g!=a.l;)(b=g-a.l,e=ft[f=a[a.l]]||ft[fu[f]],(24===f||25===f)&&(e=(24===f?fv:fw)[a[a.l+1]]),e&&e.f)?h.push([e.n,e.f(a,b,c)]):(d=b,a.l+=d);return h}var fz={PtgAdd:"+",PtgConcat:"&",PtgDiv:"/",PtgEq:"=",PtgGe:">=",PtgGt:">",PtgLe:"<=",PtgLt:"<",PtgMul:"*",PtgNe:"<>",PtgPower:"^",PtgSub:"-"};function fA(a,b,c){if(!a)return"SH33TJSERR0";if(c.biff>8&&(!a.XTI||!a.XTI[b]))return a.SheetNames[b];if(!a.XTI)return"SH33TJSERR6";var d=a.XTI[b];if(c.biff<8)return b>1e4&&(b-=65536),b<0&&(b=-b),0==b?"":a.XTI[b-1];if(!d)return"SH33TJSERR1";var e="";if(c.biff>8)switch(a[d[0]][0]){case 357:return e=-1==d[1]?"#REF":a.SheetNames[d[1]],d[1]==d[2]?e:e+":"+a.SheetNames[d[2]];case 358:if(null!=c.SID)return a.SheetNames[c.SID];return"SH33TJSSAME"+a[d[0]][0];default:return"SH33TJSSRC"+a[d[0]][0]}switch(a[d[0]][0][0]){case 1025:return e=-1==d[1]?"#REF":a.SheetNames[d[1]]||"SH33TJSERR3",d[1]==d[2]?e:e+":"+a.SheetNames[d[2]];case 14849:return a[d[0]].slice(1).map(function(a){return a.Name}).join(";;");default:if(!a[d[0]][0][3])return"SH33TJSERR2";return e=-1==d[1]?"#REF":a[d[0]][0][3][d[1]]||"SH33TJSERR4",d[1]==d[2]?e:e+":"+a[d[0]][0][3][d[2]]}}function fB(a,b,c){var d=fA(a,b,c);return"#REF"==d?d:function(a,b){if(!a&&!(b&&b.biff<=5&&b.biff>=2))throw Error("empty sheet name");return/[^\w\u4E00-\u9FFF\u3040-\u30FF]/.test(a)?"'"+a+"'":a}(d,c)}function fC(a,b,c,d,e){var f,g,h,i,j=e&&e.biff||8,k={s:{c:0,r:0},e:{c:0,r:0}},l=[],m=0,n=0,o="";if(!a[0]||!a[0][0])return"";for(var p=-1,q="",r=0,s=a[0].length;r<s;++r){var t=a[0][r];switch(t[0]){case"PtgUminus":l.push("-"+l.pop());break;case"PtgUplus":l.push("+"+l.pop());break;case"PtgPercent":l.push(l.pop()+"%");break;case"PtgAdd":case"PtgConcat":case"PtgDiv":case"PtgEq":case"PtgGe":case"PtgGt":case"PtgLe":case"PtgLt":case"PtgMul":case"PtgNe":case"PtgPower":case"PtgSub":if(f=l.pop(),g=l.pop(),p>=0){switch(a[0][p][1][0]){case 0:q=aS(" ",a[0][p][1][1]);break;case 1:q=aS("\r",a[0][p][1][1]);break;default:if(q="",e.WTF)throw Error("Unexpected PtgAttrSpaceType "+a[0][p][1][0])}g+=q,p=-1}l.push(g+fz[t[0]]+f);break;case"PtgIsect":f=l.pop(),g=l.pop(),l.push(g+" "+f);break;case"PtgUnion":f=l.pop(),g=l.pop(),l.push(g+","+f);break;case"PtgRange":f=l.pop(),g=l.pop(),l.push(g+":"+f);break;case"PtgAttrChoose":case"PtgAttrGoto":case"PtgAttrIf":case"PtgAttrIfError":case"PtgAttrBaxcel":case"PtgAttrSemi":case"PtgMemArea":case"PtgTbl":case"PtgMemErr":case"PtgMemAreaN":case"PtgMemNoMemN":case"PtgAttrNoop":case"PtgSheet":case"PtgEndSheet":case"PtgMemFunc":case"PtgMemNoMem":break;case"PtgRef":h=cl(t[1][1],k,e),l.push(cn(h,j));break;case"PtgRefN":h=c?cl(t[1][1],c,e):t[1][1],l.push(cn(h,j));break;case"PtgRef3d":m=t[1][1],h=cl(t[1][2],k,e),o=fB(d,m,e),l.push(o+"!"+cn(h,j));break;case"PtgFunc":case"PtgFuncVar":var u=t[1][0],v=t[1][1];u||(u=0);var w=0==(u&=127)?[]:l.slice(-u);l.length-=u,"User"===v&&(v=w.shift()),l.push(v+"("+w.join(",")+")");break;case"PtgBool":l.push(t[1]?"TRUE":"FALSE");break;case"PtgInt":case"PtgErr":l.push(t[1]);break;case"PtgNum":l.push(String(t[1]));break;case"PtgStr":l.push('"'+t[1].replace(/"/g,'""')+'"');break;case"PtgAreaN":i=cm(t[1][1],c?{s:c}:k,e),l.push(co(i,e));break;case"PtgArea":i=cm(t[1][1],k,e),l.push(co(i,e));break;case"PtgArea3d":m=t[1][1],i=t[1][2],o=fB(d,m,e),l.push(o+"!"+co(i,e));break;case"PtgAttrSum":l.push("SUM("+l.pop()+")");break;case"PtgName":n=t[1][2];var x=(d.names||[])[n-1]||(d[0]||[])[n],y=x?x.Name:"SH33TJSNAME"+String(n);y&&"_xlfn."==y.slice(0,6)&&!e.xlfn&&(y=y.slice(6)),l.push(y);break;case"PtgNameX":var z,A=t[1][1];if(n=t[1][2],e.biff<=5)A<0&&(A=-A),d[A]&&(z=d[A][n]);else{var B="";if(14849==((d[A]||[])[0]||[])[0]||(1025==((d[A]||[])[0]||[])[0]?d[A][n]&&d[A][n].itab>0&&(B=d.SheetNames[d[A][n].itab-1]+"!"):B=d.SheetNames[n-1]+"!"),d[A]&&d[A][n])B+=d[A][n].Name;else if(d[0]&&d[0][n])B+=d[0][n].Name;else{var C=(fA(d,A,e)||"").split(";;");C[n-1]?B=C[n-1]:B+="SH33TJSERRX"}l.push(B);break}z||(z={Name:"SH33TJSERRY"}),l.push(z.Name);break;case"PtgParen":var D="(",E=")";if(p>=0){switch(q="",a[0][p][1][0]){case 2:D=aS(" ",a[0][p][1][1])+D;break;case 3:D=aS("\r",a[0][p][1][1])+D;break;case 4:E=aS(" ",a[0][p][1][1])+E;break;case 5:E=aS("\r",a[0][p][1][1])+E;break;default:if(e.WTF)throw Error("Unexpected PtgAttrSpaceType "+a[0][p][1][0])}p=-1}l.push(D+l.pop()+E);break;case"PtgRefErr":case"PtgRefErr3d":case"PtgAreaErr":case"PtgAreaErr3d":l.push("#REF!");break;case"PtgExp":h={c:t[1][1],r:t[1][0]};var F={c:c.c,r:c.r};if(d.sharedf[cu(h)]){var G=d.sharedf[cu(h)];l.push(fC(G,k,F,d,e))}else{var H=!1;for(f=0;f!=d.arrayf.length;++f)if((g=d.arrayf[f],!(h.c<g[0].s.c)&&!(h.c>g[0].e.c))&&!(h.r<g[0].s.r)&&!(h.r>g[0].e.r)){l.push(fC(g[1],k,F,d,e)),H=!0;break}H||l.push(t[1])}break;case"PtgArray":l.push("{"+function(a){for(var b=[],c=0;c<a.length;++c){for(var d=a[c],e=[],f=0;f<d.length;++f){var g=d[f];g?2===g[0]?e.push('"'+g[1].replace(/"/g,'""')+'"'):e.push(g[1]):e.push("")}b.push(e.join(","))}return b.join(";")}(t[1])+"}");break;case"PtgAttrSpace":case"PtgAttrSpaceSemi":p=r;break;case"PtgMissArg":l.push("");break;case"PtgList":l.push("Table"+t[1].idx+"[#"+t[1].rt+"]");break;case"PtgElfCol":case"PtgElfColS":case"PtgElfColSV":case"PtgElfColV":case"PtgElfLel":case"PtgElfRadical":case"PtgElfRadicalLel":case"PtgElfRadicalS":case"PtgElfRw":case"PtgElfRwV":throw Error("Unsupported ELFs");default:throw Error("Unrecognized Formula Token: "+String(t))}var I=["PtgAttrSpace","PtgAttrSpaceSemi","PtgAttrGoto"];if(3!=e.biff&&p>=0&&-1==I.indexOf(a[0][r][0])){t=a[0][p];var J=!0;switch(t[1][0]){case 4:J=!1;case 0:q=aS(" ",t[1][1]);break;case 5:J=!1;case 1:q=aS("\r",t[1][1]);break;default:if(q="",e.WTF)throw Error("Unexpected PtgAttrSpaceType "+t[1][0])}l.push((J?q:"")+l.pop()+(J?"":q)),p=-1}}if(l.length>1&&e.WTF)throw Error("bad formula stack");return l[0]}function fD(a,b,c){var d=a.l+b,e=dS(a,6);2==c.biff&&++a.l;var f=function(a){var b;if(65535!==b5(a,a.l+6))return[cQ(a),"n"];switch(a[a.l]){case 0:return a.l+=8,["String","s"];case 1:return b=1===a[a.l+2],a.l+=8,[b,"b"];case 2:return b=a[a.l+2],a.l+=8,[b,"e"];case 3:return a.l+=8,["","s"]}return[]}(a,8),g=a.read_shift(1);2!=c.biff&&(a.read_shift(1),c.biff>=5&&a.read_shift(4));var h=function(a,b,c){var d,e,f=a.l+b,g=2==c.biff?1:2,h=a.read_shift(g);if(65535==h)return[[],(d=b-2,void(a.l+=d))];var i=fy(a,h,c);return b!==h+g&&(e=fx(a,b-h-g,i,c)),a.l=f,[i,e]}(a,d-a.l,c);return{cell:e,val:f[0],formula:h,shared:g>>3&1,tt:f[1]}}function fE(a,b,c){var d=a.read_shift(4),e=fy(a,d,c),f=a.read_shift(4),g=f>0?fx(a,f,e,c):null;return[e,g]}var fF={0:"BEEP",1:"OPEN",2:"OPEN.LINKS",3:"CLOSE.ALL",4:"SAVE",5:"SAVE.AS",6:"FILE.DELETE",7:"PAGE.SETUP",8:"PRINT",9:"PRINTER.SETUP",10:"QUIT",11:"NEW.WINDOW",12:"ARRANGE.ALL",13:"WINDOW.SIZE",14:"WINDOW.MOVE",15:"FULL",16:"CLOSE",17:"RUN",22:"SET.PRINT.AREA",23:"SET.PRINT.TITLES",24:"SET.PAGE.BREAK",25:"REMOVE.PAGE.BREAK",26:"FONT",27:"DISPLAY",28:"PROTECT.DOCUMENT",29:"PRECISION",30:"A1.R1C1",31:"CALCULATE.NOW",32:"CALCULATION",34:"DATA.FIND",35:"EXTRACT",36:"DATA.DELETE",37:"SET.DATABASE",38:"SET.CRITERIA",39:"SORT",40:"DATA.SERIES",41:"TABLE",42:"FORMAT.NUMBER",43:"ALIGNMENT",44:"STYLE",45:"BORDER",46:"CELL.PROTECTION",47:"COLUMN.WIDTH",48:"UNDO",49:"CUT",50:"COPY",51:"PASTE",52:"CLEAR",53:"PASTE.SPECIAL",54:"EDIT.DELETE",55:"INSERT",56:"FILL.RIGHT",57:"FILL.DOWN",61:"DEFINE.NAME",62:"CREATE.NAMES",63:"FORMULA.GOTO",64:"FORMULA.FIND",65:"SELECT.LAST.CELL",66:"SHOW.ACTIVE.CELL",67:"GALLERY.AREA",68:"GALLERY.BAR",69:"GALLERY.COLUMN",70:"GALLERY.LINE",71:"GALLERY.PIE",72:"GALLERY.SCATTER",73:"COMBINATION",74:"PREFERRED",75:"ADD.OVERLAY",76:"GRIDLINES",77:"SET.PREFERRED",78:"AXES",79:"LEGEND",80:"ATTACH.TEXT",81:"ADD.ARROW",82:"SELECT.CHART",83:"SELECT.PLOT.AREA",84:"PATTERNS",85:"MAIN.CHART",86:"OVERLAY",87:"SCALE",88:"FORMAT.LEGEND",89:"FORMAT.TEXT",90:"EDIT.REPEAT",91:"PARSE",92:"JUSTIFY",93:"HIDE",94:"UNHIDE",95:"WORKSPACE",96:"FORMULA",97:"FORMULA.FILL",98:"FORMULA.ARRAY",99:"DATA.FIND.NEXT",100:"DATA.FIND.PREV",101:"FORMULA.FIND.NEXT",102:"FORMULA.FIND.PREV",103:"ACTIVATE",104:"ACTIVATE.NEXT",105:"ACTIVATE.PREV",106:"UNLOCKED.NEXT",107:"UNLOCKED.PREV",108:"COPY.PICTURE",109:"SELECT",110:"DELETE.NAME",111:"DELETE.FORMAT",112:"VLINE",113:"HLINE",114:"VPAGE",115:"HPAGE",116:"VSCROLL",117:"HSCROLL",118:"ALERT",119:"NEW",120:"CANCEL.COPY",121:"SHOW.CLIPBOARD",122:"MESSAGE",124:"PASTE.LINK",125:"APP.ACTIVATE",126:"DELETE.ARROW",127:"ROW.HEIGHT",128:"FORMAT.MOVE",129:"FORMAT.SIZE",130:"FORMULA.REPLACE",131:"SEND.KEYS",132:"SELECT.SPECIAL",133:"APPLY.NAMES",134:"REPLACE.FONT",135:"FREEZE.PANES",136:"SHOW.INFO",137:"SPLIT",138:"ON.WINDOW",139:"ON.DATA",140:"DISABLE.INPUT",142:"OUTLINE",143:"LIST.NAMES",144:"FILE.CLOSE",145:"SAVE.WORKBOOK",146:"DATA.FORM",147:"COPY.CHART",148:"ON.TIME",149:"WAIT",150:"FORMAT.FONT",151:"FILL.UP",152:"FILL.LEFT",153:"DELETE.OVERLAY",155:"SHORT.MENUS",159:"SET.UPDATE.STATUS",161:"COLOR.PALETTE",162:"DELETE.STYLE",163:"WINDOW.RESTORE",164:"WINDOW.MAXIMIZE",166:"CHANGE.LINK",167:"CALCULATE.DOCUMENT",168:"ON.KEY",169:"APP.RESTORE",170:"APP.MOVE",171:"APP.SIZE",172:"APP.MINIMIZE",173:"APP.MAXIMIZE",174:"BRING.TO.FRONT",175:"SEND.TO.BACK",185:"MAIN.CHART.TYPE",186:"OVERLAY.CHART.TYPE",187:"SELECT.END",188:"OPEN.MAIL",189:"SEND.MAIL",190:"STANDARD.FONT",191:"CONSOLIDATE",192:"SORT.SPECIAL",193:"GALLERY.3D.AREA",194:"GALLERY.3D.COLUMN",195:"GALLERY.3D.LINE",196:"GALLERY.3D.PIE",197:"VIEW.3D",198:"GOAL.SEEK",199:"WORKGROUP",200:"FILL.GROUP",201:"UPDATE.LINK",202:"PROMOTE",203:"DEMOTE",204:"SHOW.DETAIL",206:"UNGROUP",207:"OBJECT.PROPERTIES",208:"SAVE.NEW.OBJECT",209:"SHARE",210:"SHARE.NAME",211:"DUPLICATE",212:"APPLY.STYLE",213:"ASSIGN.TO.OBJECT",214:"OBJECT.PROTECTION",215:"HIDE.OBJECT",216:"SET.EXTRACT",217:"CREATE.PUBLISHER",218:"SUBSCRIBE.TO",219:"ATTRIBUTES",220:"SHOW.TOOLBAR",222:"PRINT.PREVIEW",223:"EDIT.COLOR",224:"SHOW.LEVELS",225:"FORMAT.MAIN",226:"FORMAT.OVERLAY",227:"ON.RECALC",228:"EDIT.SERIES",229:"DEFINE.STYLE",240:"LINE.PRINT",243:"ENTER.DATA",249:"GALLERY.RADAR",250:"MERGE.STYLES",251:"EDITION.OPTIONS",252:"PASTE.PICTURE",253:"PASTE.PICTURE.LINK",254:"SPELLING",256:"ZOOM",259:"INSERT.OBJECT",260:"WINDOW.MINIMIZE",265:"SOUND.NOTE",266:"SOUND.PLAY",267:"FORMAT.SHAPE",268:"EXTEND.POLYGON",269:"FORMAT.AUTO",272:"GALLERY.3D.BAR",273:"GALLERY.3D.SURFACE",274:"FILL.AUTO",276:"CUSTOMIZE.TOOLBAR",277:"ADD.TOOL",278:"EDIT.OBJECT",279:"ON.DOUBLECLICK",280:"ON.ENTRY",281:"WORKBOOK.ADD",282:"WORKBOOK.MOVE",283:"WORKBOOK.COPY",284:"WORKBOOK.OPTIONS",285:"SAVE.WORKSPACE",288:"CHART.WIZARD",289:"DELETE.TOOL",290:"MOVE.TOOL",291:"WORKBOOK.SELECT",292:"WORKBOOK.ACTIVATE",293:"ASSIGN.TO.TOOL",295:"COPY.TOOL",296:"RESET.TOOL",297:"CONSTRAIN.NUMERIC",298:"PASTE.TOOL",302:"WORKBOOK.NEW",305:"SCENARIO.CELLS",306:"SCENARIO.DELETE",307:"SCENARIO.ADD",308:"SCENARIO.EDIT",309:"SCENARIO.SHOW",310:"SCENARIO.SHOW.NEXT",311:"SCENARIO.SUMMARY",312:"PIVOT.TABLE.WIZARD",313:"PIVOT.FIELD.PROPERTIES",314:"PIVOT.FIELD",315:"PIVOT.ITEM",316:"PIVOT.ADD.FIELDS",318:"OPTIONS.CALCULATION",319:"OPTIONS.EDIT",320:"OPTIONS.VIEW",321:"ADDIN.MANAGER",322:"MENU.EDITOR",323:"ATTACH.TOOLBARS",324:"VBAActivate",325:"OPTIONS.CHART",328:"VBA.INSERT.FILE",330:"VBA.PROCEDURE.DEFINITION",336:"ROUTING.SLIP",338:"ROUTE.DOCUMENT",339:"MAIL.LOGON",342:"INSERT.PICTURE",343:"EDIT.TOOL",344:"GALLERY.DOUGHNUT",350:"CHART.TREND",352:"PIVOT.ITEM.PROPERTIES",354:"WORKBOOK.INSERT",355:"OPTIONS.TRANSITION",356:"OPTIONS.GENERAL",370:"FILTER.ADVANCED",373:"MAIL.ADD.MAILER",374:"MAIL.DELETE.MAILER",375:"MAIL.REPLY",376:"MAIL.REPLY.ALL",377:"MAIL.FORWARD",378:"MAIL.NEXT.LETTER",379:"DATA.LABEL",380:"INSERT.TITLE",381:"FONT.PROPERTIES",382:"MACRO.OPTIONS",383:"WORKBOOK.HIDE",384:"WORKBOOK.UNHIDE",385:"WORKBOOK.DELETE",386:"WORKBOOK.NAME",388:"GALLERY.CUSTOM",390:"ADD.CHART.AUTOFORMAT",391:"DELETE.CHART.AUTOFORMAT",392:"CHART.ADD.DATA",393:"AUTO.OUTLINE",394:"TAB.ORDER",395:"SHOW.DIALOG",396:"SELECT.ALL",397:"UNGROUP.SHEETS",398:"SUBTOTAL.CREATE",399:"SUBTOTAL.REMOVE",400:"RENAME.OBJECT",412:"WORKBOOK.SCROLL",413:"WORKBOOK.NEXT",414:"WORKBOOK.PREV",415:"WORKBOOK.TAB.SPLIT",416:"FULL.SCREEN",417:"WORKBOOK.PROTECT",420:"SCROLLBAR.PROPERTIES",421:"PIVOT.SHOW.PAGES",422:"TEXT.TO.COLUMNS",423:"FORMAT.CHARTTYPE",424:"LINK.FORMAT",425:"TRACER.DISPLAY",430:"TRACER.NAVIGATE",431:"TRACER.CLEAR",432:"TRACER.ERROR",433:"PIVOT.FIELD.GROUP",434:"PIVOT.FIELD.UNGROUP",435:"CHECKBOX.PROPERTIES",436:"LABEL.PROPERTIES",437:"LISTBOX.PROPERTIES",438:"EDITBOX.PROPERTIES",439:"PIVOT.REFRESH",440:"LINK.COMBO",441:"OPEN.TEXT",442:"HIDE.DIALOG",443:"SET.DIALOG.FOCUS",444:"ENABLE.OBJECT",445:"PUSHBUTTON.PROPERTIES",446:"SET.DIALOG.DEFAULT",447:"FILTER",448:"FILTER.SHOW.ALL",449:"CLEAR.OUTLINE",450:"FUNCTION.WIZARD",451:"ADD.LIST.ITEM",452:"SET.LIST.ITEM",453:"REMOVE.LIST.ITEM",454:"SELECT.LIST.ITEM",455:"SET.CONTROL.VALUE",456:"SAVE.COPY.AS",458:"OPTIONS.LISTS.ADD",459:"OPTIONS.LISTS.DELETE",460:"SERIES.AXES",461:"SERIES.X",462:"SERIES.Y",463:"ERRORBAR.X",464:"ERRORBAR.Y",465:"FORMAT.CHART",466:"SERIES.ORDER",467:"MAIL.LOGOFF",468:"CLEAR.ROUTING.SLIP",469:"APP.ACTIVATE.MICROSOFT",470:"MAIL.EDIT.MAILER",471:"ON.SHEET",472:"STANDARD.WIDTH",473:"SCENARIO.MERGE",474:"SUMMARY.INFO",475:"FIND.FILE",476:"ACTIVE.CELL.FONT",477:"ENABLE.TIPWIZARD",478:"VBA.MAKE.ADDIN",480:"INSERTDATATABLE",481:"WORKGROUP.OPTIONS",482:"MAIL.SEND.MAILER",485:"AUTOCORRECT",489:"POST.DOCUMENT",491:"PICKLIST",493:"VIEW.SHOW",494:"VIEW.DEFINE",495:"VIEW.DELETE",509:"SHEET.BACKGROUND",510:"INSERT.MAP.OBJECT",511:"OPTIONS.MENONO",517:"MSOCHECKS",518:"NORMAL",519:"LAYOUT",520:"RM.PRINT.AREA",521:"CLEAR.PRINT.AREA",522:"ADD.PRINT.AREA",523:"MOVE.BRK",545:"HIDECURR.NOTE",546:"HIDEALL.NOTES",547:"DELETE.NOTE",548:"TRAVERSE.NOTES",549:"ACTIVATE.NOTES",620:"PROTECT.REVISIONS",621:"UNPROTECT.REVISIONS",647:"OPTIONS.ME",653:"WEB.PUBLISH",667:"NEWWEBQUERY",673:"PIVOT.TABLE.CHART",753:"OPTIONS.SAVE",755:"OPTIONS.SPELL",808:"HIDEALL.INKANNOTS"},fG={0:"COUNT",1:"IF",2:"ISNA",3:"ISERROR",4:"SUM",5:"AVERAGE",6:"MIN",7:"MAX",8:"ROW",9:"COLUMN",10:"NA",11:"NPV",12:"STDEV",13:"DOLLAR",14:"FIXED",15:"SIN",16:"COS",17:"TAN",18:"ATAN",19:"PI",20:"SQRT",21:"EXP",22:"LN",23:"LOG10",24:"ABS",25:"INT",26:"SIGN",27:"ROUND",28:"LOOKUP",29:"INDEX",30:"REPT",31:"MID",32:"LEN",33:"VALUE",34:"TRUE",35:"FALSE",36:"AND",37:"OR",38:"NOT",39:"MOD",40:"DCOUNT",41:"DSUM",42:"DAVERAGE",43:"DMIN",44:"DMAX",45:"DSTDEV",46:"VAR",47:"DVAR",48:"TEXT",49:"LINEST",50:"TREND",51:"LOGEST",52:"GROWTH",53:"GOTO",54:"HALT",55:"RETURN",56:"PV",57:"FV",58:"NPER",59:"PMT",60:"RATE",61:"MIRR",62:"IRR",63:"RAND",64:"MATCH",65:"DATE",66:"TIME",67:"DAY",68:"MONTH",69:"YEAR",70:"WEEKDAY",71:"HOUR",72:"MINUTE",73:"SECOND",74:"NOW",75:"AREAS",76:"ROWS",77:"COLUMNS",78:"OFFSET",79:"ABSREF",80:"RELREF",81:"ARGUMENT",82:"SEARCH",83:"TRANSPOSE",84:"ERROR",85:"STEP",86:"TYPE",87:"ECHO",88:"SET.NAME",89:"CALLER",90:"DEREF",91:"WINDOWS",92:"SERIES",93:"DOCUMENTS",94:"ACTIVE.CELL",95:"SELECTION",96:"RESULT",97:"ATAN2",98:"ASIN",99:"ACOS",100:"CHOOSE",101:"HLOOKUP",102:"VLOOKUP",103:"LINKS",104:"INPUT",105:"ISREF",106:"GET.FORMULA",107:"GET.NAME",108:"SET.VALUE",109:"LOG",110:"EXEC",111:"CHAR",112:"LOWER",113:"UPPER",114:"PROPER",115:"LEFT",116:"RIGHT",117:"EXACT",118:"TRIM",119:"REPLACE",120:"SUBSTITUTE",121:"CODE",122:"NAMES",123:"DIRECTORY",124:"FIND",125:"CELL",126:"ISERR",127:"ISTEXT",128:"ISNUMBER",129:"ISBLANK",130:"T",131:"N",132:"FOPEN",133:"FCLOSE",134:"FSIZE",135:"FREADLN",136:"FREAD",137:"FWRITELN",138:"FWRITE",139:"FPOS",140:"DATEVALUE",141:"TIMEVALUE",142:"SLN",143:"SYD",144:"DDB",145:"GET.DEF",146:"REFTEXT",147:"TEXTREF",148:"INDIRECT",149:"REGISTER",150:"CALL",151:"ADD.BAR",152:"ADD.MENU",153:"ADD.COMMAND",154:"ENABLE.COMMAND",155:"CHECK.COMMAND",156:"RENAME.COMMAND",157:"SHOW.BAR",158:"DELETE.MENU",159:"DELETE.COMMAND",160:"GET.CHART.ITEM",161:"DIALOG.BOX",162:"CLEAN",163:"MDETERM",164:"MINVERSE",165:"MMULT",166:"FILES",167:"IPMT",168:"PPMT",169:"COUNTA",170:"CANCEL.KEY",171:"FOR",172:"WHILE",173:"BREAK",174:"NEXT",175:"INITIATE",176:"REQUEST",177:"POKE",178:"EXECUTE",179:"TERMINATE",180:"RESTART",181:"HELP",182:"GET.BAR",183:"PRODUCT",184:"FACT",185:"GET.CELL",186:"GET.WORKSPACE",187:"GET.WINDOW",188:"GET.DOCUMENT",189:"DPRODUCT",190:"ISNONTEXT",191:"GET.NOTE",192:"NOTE",193:"STDEVP",194:"VARP",195:"DSTDEVP",196:"DVARP",197:"TRUNC",198:"ISLOGICAL",199:"DCOUNTA",200:"DELETE.BAR",201:"UNREGISTER",204:"USDOLLAR",205:"FINDB",206:"SEARCHB",207:"REPLACEB",208:"LEFTB",209:"RIGHTB",210:"MIDB",211:"LENB",212:"ROUNDUP",213:"ROUNDDOWN",214:"ASC",215:"DBCS",216:"RANK",219:"ADDRESS",220:"DAYS360",221:"TODAY",222:"VDB",223:"ELSE",224:"ELSE.IF",225:"END.IF",226:"FOR.CELL",227:"MEDIAN",228:"SUMPRODUCT",229:"SINH",230:"COSH",231:"TANH",232:"ASINH",233:"ACOSH",234:"ATANH",235:"DGET",236:"CREATE.OBJECT",237:"VOLATILE",238:"LAST.ERROR",239:"CUSTOM.UNDO",240:"CUSTOM.REPEAT",241:"FORMULA.CONVERT",242:"GET.LINK.INFO",243:"TEXT.BOX",244:"INFO",245:"GROUP",246:"GET.OBJECT",247:"DB",248:"PAUSE",251:"RESUME",252:"FREQUENCY",253:"ADD.TOOLBAR",254:"DELETE.TOOLBAR",255:"User",256:"RESET.TOOLBAR",257:"EVALUATE",258:"GET.TOOLBAR",259:"GET.TOOL",260:"SPELLING.CHECK",261:"ERROR.TYPE",262:"APP.TITLE",263:"WINDOW.TITLE",264:"SAVE.TOOLBAR",265:"ENABLE.TOOL",266:"PRESS.TOOL",267:"REGISTER.ID",268:"GET.WORKBOOK",269:"AVEDEV",270:"BETADIST",271:"GAMMALN",272:"BETAINV",273:"BINOMDIST",274:"CHIDIST",275:"CHIINV",276:"COMBIN",277:"CONFIDENCE",278:"CRITBINOM",279:"EVEN",280:"EXPONDIST",281:"FDIST",282:"FINV",283:"FISHER",284:"FISHERINV",285:"FLOOR",286:"GAMMADIST",287:"GAMMAINV",288:"CEILING",289:"HYPGEOMDIST",290:"LOGNORMDIST",291:"LOGINV",292:"NEGBINOMDIST",293:"NORMDIST",294:"NORMSDIST",295:"NORMINV",296:"NORMSINV",297:"STANDARDIZE",298:"ODD",299:"PERMUT",300:"POISSON",301:"TDIST",302:"WEIBULL",303:"SUMXMY2",304:"SUMX2MY2",305:"SUMX2PY2",306:"CHITEST",307:"CORREL",308:"COVAR",309:"FORECAST",310:"FTEST",311:"INTERCEPT",312:"PEARSON",313:"RSQ",314:"STEYX",315:"SLOPE",316:"TTEST",317:"PROB",318:"DEVSQ",319:"GEOMEAN",320:"HARMEAN",321:"SUMSQ",322:"KURT",323:"SKEW",324:"ZTEST",325:"LARGE",326:"SMALL",327:"QUARTILE",328:"PERCENTILE",329:"PERCENTRANK",330:"MODE",331:"TRIMMEAN",332:"TINV",334:"MOVIE.COMMAND",335:"GET.MOVIE",336:"CONCATENATE",337:"POWER",338:"PIVOT.ADD.DATA",339:"GET.PIVOT.TABLE",340:"GET.PIVOT.FIELD",341:"GET.PIVOT.ITEM",342:"RADIANS",343:"DEGREES",344:"SUBTOTAL",345:"SUMIF",346:"COUNTIF",347:"COUNTBLANK",348:"SCENARIO.GET",349:"OPTIONS.LISTS.GET",350:"ISPMT",351:"DATEDIF",352:"DATESTRING",353:"NUMBERSTRING",354:"ROMAN",355:"OPEN.DIALOG",356:"SAVE.DIALOG",357:"VIEW.GET",358:"GETPIVOTDATA",359:"HYPERLINK",360:"PHONETIC",361:"AVERAGEA",362:"MAXA",363:"MINA",364:"STDEVPA",365:"VARPA",366:"STDEVA",367:"VARA",368:"BAHTTEXT",369:"THAIDAYOFWEEK",370:"THAIDIGIT",371:"THAIMONTHOFYEAR",372:"THAINUMSOUND",373:"THAINUMSTRING",374:"THAISTRINGLENGTH",375:"ISTHAIDIGIT",376:"ROUNDBAHTDOWN",377:"ROUNDBAHTUP",378:"THAIYEAR",379:"RTD",380:"CUBEVALUE",381:"CUBEMEMBER",382:"CUBEMEMBERPROPERTY",383:"CUBERANKEDMEMBER",384:"HEX2BIN",385:"HEX2DEC",386:"HEX2OCT",387:"DEC2BIN",388:"DEC2HEX",389:"DEC2OCT",390:"OCT2BIN",391:"OCT2HEX",392:"OCT2DEC",393:"BIN2DEC",394:"BIN2OCT",395:"BIN2HEX",396:"IMSUB",397:"IMDIV",398:"IMPOWER",399:"IMABS",400:"IMSQRT",401:"IMLN",402:"IMLOG2",403:"IMLOG10",404:"IMSIN",405:"IMCOS",406:"IMEXP",407:"IMARGUMENT",408:"IMCONJUGATE",409:"IMAGINARY",410:"IMREAL",411:"COMPLEX",412:"IMSUM",413:"IMPRODUCT",414:"SERIESSUM",415:"FACTDOUBLE",416:"SQRTPI",417:"QUOTIENT",418:"DELTA",419:"GESTEP",420:"ISEVEN",421:"ISODD",422:"MROUND",423:"ERF",424:"ERFC",425:"BESSELJ",426:"BESSELK",427:"BESSELY",428:"BESSELI",429:"XIRR",430:"XNPV",431:"PRICEMAT",432:"YIELDMAT",433:"INTRATE",434:"RECEIVED",435:"DISC",436:"PRICEDISC",437:"YIELDDISC",438:"TBILLEQ",439:"TBILLPRICE",440:"TBILLYIELD",441:"PRICE",442:"YIELD",443:"DOLLARDE",444:"DOLLARFR",445:"NOMINAL",446:"EFFECT",447:"CUMPRINC",448:"CUMIPMT",449:"EDATE",450:"EOMONTH",451:"YEARFRAC",452:"COUPDAYBS",453:"COUPDAYS",454:"COUPDAYSNC",455:"COUPNCD",456:"COUPNUM",457:"COUPPCD",458:"DURATION",459:"MDURATION",460:"ODDLPRICE",461:"ODDLYIELD",462:"ODDFPRICE",463:"ODDFYIELD",464:"RANDBETWEEN",465:"WEEKNUM",466:"AMORDEGRC",467:"AMORLINC",468:"CONVERT",724:"SHEETJS",469:"ACCRINT",470:"ACCRINTM",471:"WORKDAY",472:"NETWORKDAYS",473:"GCD",474:"MULTINOMIAL",475:"LCM",476:"FVSCHEDULE",477:"CUBEKPIMEMBER",478:"CUBESET",479:"CUBESETCOUNT",480:"IFERROR",481:"COUNTIFS",482:"SUMIFS",483:"AVERAGEIF",484:"AVERAGEIFS"},fH={2:1,3:1,10:0,15:1,16:1,17:1,18:1,19:0,20:1,21:1,22:1,23:1,24:1,25:1,26:1,27:2,30:2,31:3,32:1,33:1,34:0,35:0,38:1,39:2,40:3,41:3,42:3,43:3,44:3,45:3,47:3,48:2,53:1,61:3,63:0,65:3,66:3,67:1,68:1,69:1,70:1,71:1,72:1,73:1,74:0,75:1,76:1,77:1,79:2,80:2,83:1,85:0,86:1,89:0,90:1,94:0,95:0,97:2,98:1,99:1,101:3,102:3,105:1,106:1,108:2,111:1,112:1,113:1,114:1,117:2,118:1,119:4,121:1,126:1,127:1,128:1,129:1,130:1,131:1,133:1,134:1,135:1,136:2,137:2,138:2,140:1,141:1,142:3,143:4,144:4,161:1,162:1,163:1,164:1,165:2,172:1,175:2,176:2,177:3,178:2,179:1,184:1,186:1,189:3,190:1,195:3,196:3,197:1,198:1,199:3,201:1,207:4,210:3,211:1,212:2,213:2,214:1,215:1,225:0,229:1,230:1,231:1,232:1,233:1,234:1,235:3,244:1,247:4,252:2,257:1,261:1,271:1,273:4,274:2,275:2,276:2,277:3,278:3,279:1,280:3,281:3,282:3,283:1,284:1,285:2,286:4,287:3,288:2,289:4,290:3,291:3,292:3,293:4,294:1,295:3,296:1,297:3,298:1,299:2,300:3,301:3,302:4,303:2,304:2,305:2,306:2,307:2,308:2,309:3,310:2,311:2,312:2,313:2,314:2,315:2,316:4,325:2,326:2,327:2,328:2,331:2,332:2,337:2,342:1,343:1,346:2,347:1,350:4,351:3,352:1,353:2,360:1,368:1,369:1,370:1,371:1,372:1,373:1,374:1,375:1,376:1,377:1,378:1,382:3,385:1,392:1,393:1,396:2,397:2,398:2,399:1,400:1,401:1,402:1,403:1,404:1,405:1,406:1,407:1,408:1,409:1,410:1,414:4,415:1,416:1,417:2,420:1,421:1,422:2,424:1,425:2,426:2,427:2,428:2,430:3,438:3,439:3,440:3,443:2,444:2,445:2,446:2,447:6,448:6,449:2,450:2,464:2,468:3,476:2,479:1,480:2,65535:0};function fI(a){return"of:"==a.slice(0,3)&&(a=a.slice(3)),61==a.charCodeAt(0)&&61==(a=a.slice(1)).charCodeAt(0)&&(a=a.slice(1)),(a=(a=(a=a.replace(/COM\.MICROSOFT\./g,"")).replace(/\[((?:\.[A-Z]+[0-9]+)(?::\.[A-Z]+[0-9]+)?)\]/g,function(a,b){return b.replace(/\./g,"")})).replace(/\[.(#[A-Z]*[?!])\]/g,"$1")).replace(/[;~]/g,",").replace(/\|/g,";")}function fJ(a){var b=a.split(":");return[b[0].split(".")[0],b[0].split(".")[1]+(b.length>1?":"+(b[1].split(".")[1]||b[1].split(".")[0]):"")]}var fK={},fL={},fM="undefined"!=typeof Map;function fN(a,b,c){var d=0,e=a.length;if(c){if(fM?c.has(b):Object.prototype.hasOwnProperty.call(c,b)){for(var f=fM?c.get(b):c[b];d<f.length;++d)if(a[f[d]].t===b)return a.Count++,f[d]}}else for(;d<e;++d)if(a[d].t===b)return a.Count++,d;return a[e]={t:b},a.Count++,a.Unique++,c&&(fM?(c.has(b)||c.set(b,[]),c.get(b).push(e)):(Object.prototype.hasOwnProperty.call(c,b)||(c[b]=[]),c[b].push(e))),e}function fO(a,b){var c={min:a+1,max:a+1},d=-1;return b.MDW&&(eH=b.MDW),null!=b.width?c.customWidth=1:null!=b.wpx?d=eJ(b.wpx):null!=b.wch&&(d=b.wch),d>-1?(c.width=eK(d),c.customWidth=1):null!=b.width&&(c.width=b.width),b.hidden&&(c.hidden=!0),null!=b.level&&(c.outlineLevel=c.level=b.level),c}function fP(a,b){if(a){var c=[.7,.7,.75,.75,.3,.3];"xlml"==b&&(c=[1,1,1,1,.5,.5]),null==a.left&&(a.left=c[0]),null==a.right&&(a.right=c[1]),null==a.top&&(a.top=c[2]),null==a.bottom&&(a.bottom=c[3]),null==a.header&&(a.header=c[4]),null==a.footer&&(a.footer=c[5])}}function fQ(a,b,c){var d=c.revssf[null!=b.z?b.z:"General"],e=60,f=a.length;if(null==d&&c.ssf){for(;e<392;++e)if(null==c.ssf[e]){au(b.z,e),c.ssf[e]=b.z,c.revssf[b.z]=d=e;break}}for(e=0;e!=f;++e)if(a[e].numFmtId===d)return e;return a[f]={numFmtId:d,fontId:0,fillId:0,borderId:0,xfId:0,applyNumberFormat:1},f}function fR(a,b,c,d,e,f){try{d.cellNF&&(a.z=U[b])}catch(a){if(d.WTF)throw a}if("z"!==a.t||d.cellStyles){if("d"===a.t&&"string"==typeof a.v&&(a.v=aP(a.v)),(!d||!1!==d.cellText)&&"z"!==a.t)try{if(null==U[b]&&au(ax[b]||"General",b),"e"===a.t)a.w=a.w||c$[a.v];else if(0===b)if("n"===a.t)(0|a.v)===a.v?a.w=a.v.toString(10):a.w=ad(a.v);else if("d"===a.t){var g=aH(a.v);(0|g)===g?a.w=g.toString(10):a.w=ad(g)}else{if(void 0===a.v)return"";a.w=ae(a.v,fL)}else"d"===a.t?a.w=at(b,aH(a.v),fL):a.w=at(b,a.v,fL)}catch(a){if(d.WTF)throw a}if(d.cellStyles&&null!=c)try{a.s=f.Fills[c],a.s.fgColor&&a.s.fgColor.theme&&!a.s.fgColor.rgb&&(a.s.fgColor.rgb=eG(e.themeElements.clrScheme[a.s.fgColor.theme].rgb,a.s.fgColor.tint||0),d.WTF&&(a.s.fgColor.raw_rgb=e.themeElements.clrScheme[a.s.fgColor.theme].rgb)),a.s.bgColor&&a.s.bgColor.theme&&(a.s.bgColor.rgb=eG(e.themeElements.clrScheme[a.s.bgColor.theme].rgb,a.s.bgColor.tint||0),d.WTF&&(a.s.bgColor.raw_rgb=e.themeElements.clrScheme[a.s.bgColor.theme].rgb))}catch(a){if(d.WTF&&f.Fills)throw a}}}var fS=/<(?:\w:)?mergeCell ref="[A-Z0-9:]+"\s*[\/]?>/g,fT=/<(?:\w+:)?sheetData[^>]*>([\s\S]*)<\/(?:\w+:)?sheetData>/,fU=/<(?:\w:)?hyperlink [^>]*>/mg,fV=/"(\w*:\w*)"/,fW=/<(?:\w:)?col\b[^>]*[\/]?>/g,fX=/<(?:\w:)?autoFilter[^>]*([\/]|>([\s\S]*)<\/(?:\w:)?autoFilter)>/g,fY=/<(?:\w:)?pageMargins[^>]*\/>/g,fZ=/<(?:\w:)?sheetPr\b(?:[^>a-z][^>]*)?\/>/,f$=/<(?:\w:)?sheetPr[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetPr)>/,f_=/<(?:\w:)?sheetViews[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetViews)>/;function f0(a,b,c,d){var e=bc(a);c.Sheets[d]||(c.Sheets[d]={}),e.codeName&&(c.Sheets[d].CodeName=bg(bt(e.codeName)))}var f1=["objects","scenarios","selectLockedCells","selectUnlockedCells"],f2=["formatColumns","formatRows","formatCells","insertColumns","insertRows","insertHyperlinks","deleteColumns","deleteRows","sort","autoFilter","pivotTables"],f3=/<(?:\w:)?sheetView(?:[^>a-z][^>]*)?\/?>/,f4=function(){var a=/<(?:\w+:)?c[ \/>]/,b=/<\/(?:\w+:)?row>/,c=/r=["']([^"']*)["']/,d=/<(?:\w+:)?is>([\S\s]*?)<\/(?:\w+:)?is>/,e=/ref=["']([^"']*)["']/,f=bv("v"),g=bv("f");return function(h,i,j,k,l,m){for(var n,o,p,q,r,s=0,t="",u=[],v=[],w=0,x=0,y=0,z="",A=0,B=0,C=0,D=0,E=Array.isArray(m.CellXf),F=[],G=[],H=Array.isArray(i),I=[],J={},K=!1,L=!!j.sheetStubs,M=h.split(b),N=0,O=M.length;N!=O;++N){var P=(t=M[N].trim()).length;if(0!==P){var Q=0;b:for(s=0;s<P;++s)switch(t[s]){case">":if("/"!=t[s-1]){++s;break b}if(j&&j.cellStyles){if(A=null!=(o=bc(t.slice(Q,s),!0)).r?parseInt(o.r,10):A+1,B=-1,j.sheetRows&&j.sheetRows<A)continue;J={},K=!1,o.ht&&(K=!0,J.hpt=parseFloat(o.ht),J.hpx=eP(J.hpt)),"1"==o.hidden&&(K=!0,J.hidden=!0),null!=o.outlineLevel&&(K=!0,J.level=+o.outlineLevel),K&&(I[A-1]=J)}break;case"<":Q=s}if(Q>=s)break;if(A=null!=(o=bc(t.slice(Q,s),!0)).r?parseInt(o.r,10):A+1,B=-1,!j.sheetRows||!(j.sheetRows<A)){k.s.r>A-1&&(k.s.r=A-1),k.e.r<A-1&&(k.e.r=A-1),j&&j.cellStyles&&(J={},K=!1,o.ht&&(K=!0,J.hpt=parseFloat(o.ht),J.hpx=eP(J.hpt)),"1"==o.hidden&&(K=!0,J.hidden=!0),null!=o.outlineLevel&&(K=!0,J.level=+o.outlineLevel),K&&(I[A-1]=J)),u=t.slice(s).split(a);for(var R=0;R!=u.length&&"<"==u[R].trim().charAt(0);++R);for(s=0,u=u.slice(R);s!=u.length;++s)if(0!==(t=u[s].trim()).length){if(v=t.match(c),w=s,x=0,y=0,t="<c "+("<"==t.slice(0,1)?">":"")+t,null!=v&&2===v.length){for(x=0,w=0,z=v[1];x!=z.length&&!((y=z.charCodeAt(x)-64)<1)&&!(y>26);++x)w=26*w+y;B=--w}else++B;for(x=0;x!=t.length&&62!==t.charCodeAt(x);++x);if(++x,(o=bc(t.slice(0,x),!0)).r||(o.r=cu({r:A-1,c:B})),z=t.slice(x),n={t:""},null!=(v=z.match(f))&&""!==v[1]&&(n.v=bg(v[1])),j.cellFormula){if(null!=(v=z.match(g))&&""!==v[1]){if(n.f=bg(bt(v[1])).replace(/\r\n/g,"\n"),j.xlfn||(n.f=fh(n.f)),v[0].indexOf('t="array"')>-1)n.F=(z.match(e)||[])[1],n.F.indexOf(":")>-1&&F.push([cx(n.F),n.F]);else if(v[0].indexOf('t="shared"')>-1){q=bc(v[0]);var S=bg(bt(v[1]));j.xlfn||(S=fh(S)),G[parseInt(q.si,10)]=[q,S,o.r]}}else(v=z.match(/<f[^>]*\/>/))&&G[(q=bc(v[0])).si]&&(n.f=function(a,b,c){var d=cv(b).s,e=ct(c);return fg(a,{r:e.r-d.r,c:e.c-d.c})}(G[q.si][1],G[q.si][2],o.r));var T=ct(o.r);for(x=0;x<F.length;++x)T.r>=F[x][0].s.r&&T.r<=F[x][0].e.r&&T.c>=F[x][0].s.c&&T.c<=F[x][0].e.c&&(n.F=F[x][1])}if(null==o.t&&void 0===n.v)if(n.f||n.F)n.v=0,n.t="n";else{if(!L)continue;n.t="z"}else n.t=o.t||"n";switch(k.s.c>B&&(k.s.c=B),k.e.c<B&&(k.e.c=B),n.t){case"n":if(""==n.v||null==n.v){if(!L)continue;n.t="z"}else n.v=parseFloat(n.v);break;case"s":if(void 0===n.v){if(!L)continue;n.t="z"}else p=fK[parseInt(n.v,10)],n.v=p.t,n.r=p.r,j.cellHTML&&(n.h=p.h);break;case"str":n.t="s",n.v=null!=n.v?bt(n.v):"",j.cellHTML&&(n.h=bm(n.v));break;case"inlineStr":v=z.match(d),n.t="s",null!=v&&(p=ep(v[1]))?(n.v=p.t,j.cellHTML&&(n.h=p.h)):n.v="";break;case"b":n.v=bo(n.v);break;case"d":j.cellDates?n.v=aP(n.v,1):(n.v=aH(aP(n.v,1)),n.t="n");break;case"e":j&&!1===j.cellText||(n.w=n.v),n.v=c_[n.v]}if(C=D=0,r=null,E&&void 0!==o.s&&null!=(r=m.CellXf[o.s])&&(null!=r.numFmtId&&(C=r.numFmtId),j.cellStyles&&null!=r.fillId&&(D=r.fillId)),fR(n,C,D,j,l,m),j.cellDates&&E&&"n"==n.t&&aq(U[C])&&(n.t="d",n.v=aL(n.v)),o.cm&&j.xlmeta){var V=(j.xlmeta.Cell||[])[o.cm-1];V&&"XLDAPR"==V.type&&(n.D=!0)}if(H){var W=ct(o.r);i[W.r]||(i[W.r]=[]),i[W.r][W.c]=n}else i[o.r]=n}}}}I.length>0&&(i["!rows"]=I)}}();function f5(a,b,c,d){var e,f=[a6,bE("worksheet",null,{xmlns:bJ[0],"xmlns:r":bI.r})],g=c.SheetNames[a],h=0,i="",j=c.Sheets[g];null==j&&(j={});var k=j["!ref"]||"A1",l=cx(k);if(l.e.c>16383||l.e.r>1048575){if(b.WTF)throw Error("Range "+k+" exceeds format limit A1:XFD1048576");l.e.c=Math.min(l.e.c,16383),l.e.r=Math.min(l.e.c,1048575),k=cw(l)}d||(d={}),j["!comments"]=[];var m=[];!function(a,b,c,d,e){var f=!1,g={},h=null;if("xlsx"!==d.bookType&&b.vbaraw){var i=b.SheetNames[c];try{b.Workbook&&(i=b.Workbook.Sheets[c].CodeName||i)}catch(a){}f=!0,g.codeName=bu(bj(i))}if(a&&a["!outline"]){var j={summaryBelow:1,summaryRight:1};a["!outline"].above&&(j.summaryBelow=0),a["!outline"].left&&(j.summaryRight=0),h=(h||"")+bE("outlinePr",null,j)}(f||h)&&(e[e.length]=bE("sheetPr",h,g))}(j,c,a,b,f),f[f.length]=bE("dimension",null,{ref:k}),f[f.length]=(n={workbookViewId:"0"},(((c||{}).Workbook||{}).Views||[])[0]&&(n.rightToLeft=c.Workbook.Views[0].RTL?"1":"0"),bE("sheetViews",bE("sheetView",null,n),{})),b.sheetFormat&&(f[f.length]=bE("sheetFormatPr",null,{defaultRowHeight:b.sheetFormat.defaultRowHeight||"16",baseColWidth:b.sheetFormat.baseColWidth||"10",outlineLevelRow:b.sheetFormat.outlineLevelRow||"7"})),null!=j["!cols"]&&j["!cols"].length>0&&(f[f.length]=function(a,b){for(var c,d=["<cols>"],e=0;e!=b.length;++e)(c=b[e])&&(d[d.length]=bE("col",null,fO(e,c)));return d[d.length]="</cols>",d.join("")}(0,j["!cols"])),f[h=f.length]="<sheetData/>",j["!links"]=[],null!=j["!ref"]&&(i=function(a,b,c,d){var e,f,g=[],h=[],i=cx(a["!ref"]),j="",k="",l=[],m=0,n=0,o=a["!rows"],p=Array.isArray(a),q={r:k},r=-1;for(n=i.s.c;n<=i.e.c;++n)l[n]=cs(n);for(m=i.s.r;m<=i.e.r;++m){for(h=[],k=cq(m),n=i.s.c;n<=i.e.c;++n){e=l[n]+k;var s=p?(a[m]||[])[n]:a[e];void 0!==s&&null!=(j=function(a,b,c,d){if(a.c&&c["!comments"].push([b,a.c]),void 0===a.v&&"string"!=typeof a.f||"z"===a.t&&!a.f)return"";var e="",f=a.t,g=a.v;if("z"!==a.t)switch(a.t){case"b":e=a.v?"1":"0";break;case"n":e=""+a.v;break;case"e":e=c$[a.v];break;case"d":d&&d.cellDates?e=aP(a.v,-1).toISOString():((a=aR(a)).t="n",e=""+(a.v=aH(aP(a.v)))),void 0===a.z&&(a.z=U[14]);break;default:e=a.v}var h=bC("v",bj(e)),i={r:b},j=fQ(d.cellXfs,a,d);switch(0!==j&&(i.s=j),a.t){case"n":case"z":break;case"d":i.t="d";break;case"b":i.t="b";break;case"e":i.t="e";break;default:if(null==a.v){delete a.t;break}if(a.v.length>32767)throw Error("Text length must not exceed 32767 characters");if(d&&d.bookSST){h=bC("v",""+fN(d.Strings,a.v,d.revStrings)),i.t="s";break}i.t="str"}if(a.t!=f&&(a.t=f,a.v=g),"string"==typeof a.f&&a.f){var k=a.F&&a.F.slice(0,b.length)==b?{t:"array",ref:a.F}:null;h=bE("f",bj(a.f),k)+(null!=a.v?h:"")}return a.l&&c["!links"].push([b,a.l]),a.D&&(i.cm=1),bE("c",h,i)}(s,e,a,b,c,d))&&h.push(j)}(h.length>0||o&&o[m])&&(q={r:k},o&&o[m]&&((f=o[m]).hidden&&(q.hidden=1),r=-1,f.hpx?r=eO(f.hpx):f.hpt&&(r=f.hpt),r>-1&&(q.ht=r,q.customHeight=1),f.level&&(q.outlineLevel=f.level)),g[g.length]=bE("row",h.join(""),q))}if(o)for(;m<o.length;++m)o&&o[m]&&(q={r:m+1},(f=o[m]).hidden&&(q.hidden=1),r=-1,f.hpx?r=eO(f.hpx):f.hpt&&(r=f.hpt),r>-1&&(q.ht=r,q.customHeight=1),f.level&&(q.outlineLevel=f.level),g[g.length]=bE("row","",q));return g.join("")}(j,b,a,c,d)).length>0&&(f[f.length]=i),f.length>h+1&&(f[f.length]="</sheetData>",f[h]=f[h].replace("/>",">")),j["!protect"]&&(f[f.length]=(o=j["!protect"],p={sheet:1},f1.forEach(function(a){null!=o[a]&&o[a]&&(p[a]="1")}),f2.forEach(function(a){null==o[a]||o[a]||(p[a]="0")}),o.password&&(p.password=eA(o.password).toString(16).toUpperCase()),bE("sheetProtection",null,p))),null!=j["!autofilter"]&&(f[f.length]=function(a,b,c,d){var e="string"==typeof a.ref?a.ref:cw(a.ref);c.Workbook||(c.Workbook={Sheets:[]}),c.Workbook.Names||(c.Workbook.Names=[]);var f=c.Workbook.Names,g=cv(e);g.s.r==g.e.r&&(g.e.r=cv(b["!ref"]).e.r,e=cw(g));for(var h=0;h<f.length;++h){var i=f[h];if("_xlnm._FilterDatabase"==i.Name&&i.Sheet==d){i.Ref="'"+c.SheetNames[d]+"'!"+e;break}}return h==f.length&&f.push({Name:"_xlnm._FilterDatabase",Sheet:d,Ref:"'"+c.SheetNames[d]+"'!"+e}),bE("autoFilter",null,{ref:e})}(j["!autofilter"],j,c,a)),null!=j["!merges"]&&j["!merges"].length>0&&(f[f.length]=function(a){if(0===a.length)return"";for(var b='<mergeCells count="'+a.length+'">',c=0;c!=a.length;++c)b+='<mergeCell ref="'+cw(a[c])+'"/>';return b+"</mergeCells>"}(j["!merges"]));var n,o,p,q,r=-1,s=-1;return j["!links"].length>0&&(f[f.length]="<hyperlinks>",j["!links"].forEach(function(a){a[1].Target&&(q={ref:a[0]},"#"!=a[1].Target.charAt(0)&&(s=c8(d,-1,bj(a[1].Target).replace(/#.*$/,""),c4.HLINK),q["r:id"]="rId"+s),(r=a[1].Target.indexOf("#"))>-1&&(q.location=bj(a[1].Target.slice(r+1))),a[1].Tooltip&&(q.tooltip=bj(a[1].Tooltip)),f[f.length]=bE("hyperlink",null,q))}),f[f.length]="</hyperlinks>"),delete j["!links"],null!=j["!margins"]&&(f[f.length]=(fP(e=j["!margins"]),bE("pageMargins",null,e))),(!b||b.ignoreEC||void 0==b.ignoreEC)&&(f[f.length]=bC("ignoredErrors",bE("ignoredError",null,{numberStoredAsText:1,sqref:k}))),m.length>0&&(s=c8(d,-1,"../drawings/drawing"+(a+1)+".xml",c4.DRAW),f[f.length]=bE("drawing",null,{"r:id":"rId"+s}),j["!drawing"]=m),j["!comments"].length>0&&(s=c8(d,-1,"../drawings/vmlDrawing"+(a+1)+".vml",c4.VML),f[f.length]=bE("legacyDrawing",null,{"r:id":"rId"+s}),j["!legacy"]=s),f.length>1&&(f[f.length]="</worksheet>",f[1]=f[1].replace("/>",">")),f.join("")}function f6(a){return[cI(a),cQ(a),"n"]}var f7=["left","right","top","bottom","header","footer"],f8=[["allowRefreshQuery",!1,"bool"],["autoCompressPictures",!0,"bool"],["backupFile",!1,"bool"],["checkCompatibility",!1,"bool"],["CodeName",""],["date1904",!1,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",!1,"bool"],["hidePivotFieldList",!1,"bool"],["promptedSolutions",!1,"bool"],["publishItems",!1,"bool"],["refreshAllConnections",!1,"bool"],["saveExternalLinkValues",!0,"bool"],["showBorderUnselectedTables",!0,"bool"],["showInkAnnotation",!0,"bool"],["showObjects","all"],["showPivotChartFilter",!1,"bool"],["updateLinks","userSet"]],f9=[["activeTab",0,"int"],["autoFilterDateGrouping",!0,"bool"],["firstSheet",0,"int"],["minimized",!1,"bool"],["showHorizontalScroll",!0,"bool"],["showSheetTabs",!0,"bool"],["showVerticalScroll",!0,"bool"],["tabRatio",600,"int"],["visibility","visible"]],ga=[],gb=[["calcCompleted","true"],["calcMode","auto"],["calcOnSave","true"],["concurrentCalc","true"],["fullCalcOnLoad","false"],["fullPrecision","true"],["iterate","false"],["iterateCount","100"],["iterateDelta","0.001"],["refMode","A1"]];function gc(a,b){for(var c=0;c!=a.length;++c)for(var d=a[c],e=0;e!=b.length;++e){var f=b[e];if(null==d[f[0]])d[f[0]]=f[1];else switch(f[2]){case"bool":"string"==typeof d[f[0]]&&(d[f[0]]=bo(d[f[0]]));break;case"int":"string"==typeof d[f[0]]&&(d[f[0]]=parseInt(d[f[0]],10))}}}function gd(a,b){for(var c=0;c!=b.length;++c){var d=b[c];if(null==a[d[0]])a[d[0]]=d[1];else switch(d[2]){case"bool":"string"==typeof a[d[0]]&&(a[d[0]]=bo(a[d[0]]));break;case"int":"string"==typeof a[d[0]]&&(a[d[0]]=parseInt(a[d[0]],10))}}}function ge(a){gd(a.WBProps,f8),gd(a.CalcPr,gb),gc(a.WBView,f9),gc(a.Sheets,ga),fL.date1904=bo(a.WBProps.date1904)}var gf="][*?/\\".split("");function gg(a,b){if(a.length>31){if(b)return!1;throw Error("Sheet names cannot exceed 31 chars")}var c=!0;return gf.forEach(function(d){if(-1!=a.indexOf(d)){if(!b)throw Error("Sheet name cannot contain : \\ / ? * [ ]");c=!1}}),c}var gh=/<\w+:workbook/;function gi(a){var b=[a6];b[b.length]=bE("workbook",null,{xmlns:bJ[0],"xmlns:r":bI.r});var c=a.Workbook&&(a.Workbook.Names||[]).length>0,d={codeName:"ThisWorkbook"};a.Workbook&&a.Workbook.WBProps&&(f8.forEach(function(b){null!=a.Workbook.WBProps[b[0]]&&a.Workbook.WBProps[b[0]]!=b[1]&&(d[b[0]]=a.Workbook.WBProps[b[0]])}),a.Workbook.WBProps.CodeName&&(d.codeName=a.Workbook.WBProps.CodeName,delete d.CodeName)),b[b.length]=bE("workbookPr",null,d);var e=a.Workbook&&a.Workbook.Sheets||[],f=0;if(e&&e[0]&&e[0].Hidden){for(f=0,b[b.length]="<bookViews>";f!=a.SheetNames.length&&e[f]&&e[f].Hidden;++f);f==a.SheetNames.length&&(f=0),b[b.length]='<workbookView firstSheet="'+f+'" activeTab="'+f+'"/>',b[b.length]="</bookViews>"}for(f=0,b[b.length]="<sheets>";f!=a.SheetNames.length;++f){var g={name:bj(a.SheetNames[f].slice(0,31))};if(g.sheetId=""+(f+1),g["r:id"]="rId"+(f+1),e[f])switch(e[f].Hidden){case 1:g.state="hidden";break;case 2:g.state="veryHidden"}b[b.length]=bE("sheet",null,g)}return b[b.length]="</sheets>",c&&(b[b.length]="<definedNames>",a.Workbook&&a.Workbook.Names&&a.Workbook.Names.forEach(function(a){var c={name:a.Name};a.Comment&&(c.comment=a.Comment),null!=a.Sheet&&(c.localSheetId=""+a.Sheet),a.Hidden&&(c.hidden="1"),a.Ref&&(b[b.length]=bE("definedName",bj(a.Ref),c))}),b[b.length]="</definedNames>"),b.length>2&&(b[b.length]="</workbook>",b[1]=b[1].replace("/>",">")),b.join("")}function gj(a,b){var c={};return a.read_shift(4),c.ArchID=a.read_shift(4),a.l+=b-8,c}var gk=/([\w:]+)=((?:")([^"]*)(?:")|(?:')([^']*)(?:'))/g,gl=/([\w:]+)=((?:")(?:[^"]*)(?:")|(?:')(?:[^']*)(?:'))/;function gm(a,b){var c=a.split(/\s+/),d=[];if(b||(d[0]=c[0]),1===c.length)return d;var e,f,g,h=a.match(gk);if(h)for(g=0;g!=h.length;++g)-1===(f=(e=h[g].match(gl))[1].indexOf(":"))?d[e[1]]=e[2].slice(1,e[2].length-1):d["xmlns:"===e[1].slice(0,6)?"xmlns"+e[1].slice(6):e[1].slice(f+1)]=e[2].slice(1,e[2].length-1);return d}function gn(a,b){var c,d,g,i=b||{};aw();var j=v(bG(a));("binary"==i.type||"array"==i.type||"base64"==i.type)&&(j=void 0!==e?e.utils.decode(65001,t(j)):bt(j));var k=j.slice(0,1024).toLowerCase(),l=!1;if((1023&(k=k.replace(/".*?"/g,"")).indexOf(">"))>Math.min(1023&k.indexOf(","),1023&k.indexOf(";"))){var m=aR(i);return m.type="string",ei.to_workbook(j,m)}if(-1==k.indexOf("<?xml")&&["html","table","head","meta","script","style","div"].forEach(function(a){k.indexOf("<"+a)>=0&&(l=!0)}),l){var n=j,o=i,p=n.match(/<table[\s\S]*?>[\s\S]*?<\/table>/gi);if(!p||0==p.length)throw Error("Invalid HTML: could not find <table>");if(1==p.length)return cA(gA(p[0],o),o);var q=ha();return p.forEach(function(a,b){hb(q,gA(a,o),"Sheet"+(b+1))}),q}h={"General Number":"General","General Date":U[22],"Long Date":"dddd, mmmm dd, yyyy","Medium Date":U[15],"Short Date":U[14],"Long Time":U[19],"Medium Time":U[18],"Short Time":U[20],Currency:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',Fixed:U[2],Standard:U[4],Percent:U[10],Scientific:U[11],"Yes/No":'"Yes";"Yes";"No";@',"True/False":'"True";"True";"False";@',"On/Off":'"Yes";"Yes";"No";@'};var r,s,u,w=[],x={},y=[],z=i.dense?[]:{},A="",B={},C={},D=gm('<Data ss:Type="String">'),E=0,F=0,G=0,H={s:{r:2e6,c:2e6},e:{r:0,c:0}},I={},J={},K="",L=0,M=[],N={},O={},P=0,Q=[],R=[],S={},T=[],V=!1,W=[],X=[],Z={},_=0,aa=0,ab={Sheets:[],WBProps:{date1904:!1}},ac={};bH.lastIndex=0,j=j.replace(/<!--([\s\S]*?)-->/mg,"");for(var af="";r=bH.exec(j);)switch(r[3]=(af=r[3]).toLowerCase()){case"data":if("data"==af){if("/"===r[1]){if((s=w.pop())[0]!==r[3])throw Error("Bad state: "+s.join("|"))}else"/"!==r[0].charAt(r[0].length-2)&&w.push([r[3],!0]);break}if(w[w.length-1][1])break;"/"===r[1]?function(a,b,c,d,e,f,g,i,j,k){var l="General",m=d.StyleID,n={};k=k||{};var o=[],p=0;for(void 0===m&&i&&(m=i.StyleID),void 0===m&&g&&(m=g.StyleID);void 0!==f[m]&&(f[m].nf&&(l=f[m].nf),f[m].Interior&&o.push(f[m].Interior),f[m].Parent);)m=f[m].Parent;switch(c.Type){case"Boolean":d.t="b",d.v=bo(a);break;case"String":d.t="s",d.r=bn(bg(a)),d.v=a.indexOf("<")>-1?bg(b||a).replace(/<.*?>/g,""):d.r;break;case"DateTime":"Z"!=a.slice(-1)&&(a+="Z"),d.v=(aP(a)-new Date(Date.UTC(1899,11,30)))/864e5,d.v!=d.v?d.v=bg(a):d.v<60&&(d.v=d.v-1),l&&"General"!=l||(l="yyyy-mm-dd");case"Number":void 0===d.v&&(d.v=+a),d.t||(d.t="n");break;case"Error":d.t="e",d.v=c_[a],!1!==k.cellText&&(d.w=a);break;default:""==a&&""==b?d.t="z":(d.t="s",d.v=bn(b||a))}if(!function(a,b,c){if("z"!==a.t){if(!c||!1!==c.cellText)try{if("e"===a.t)a.w=a.w||c$[a.v];else if("General"===b)"n"===a.t?(0|a.v)===a.v?a.w=a.v.toString(10):a.w=ad(a.v):a.w=ae(a.v);else{var d,e,f;d=b||"General",e=a.v,f=h[d]||bg(d),a.w="General"===f?ae(e):at(f,e)}}catch(a){if(c.WTF)throw a}try{var g=h[b]||b||"General";if(c.cellNF&&(a.z=g),c.cellDates&&"n"==a.t&&aq(g)){var i=Y(a.v);i&&(a.t="d",a.v=new Date(i.y,i.m-1,i.d,i.H,i.M,i.S,i.u))}}catch(a){if(c.WTF)throw a}}}(d,l,k),!1!==k.cellFormula)if(d.Formula){var q=bg(d.Formula);61==q.charCodeAt(0)&&(q=q.slice(1)),d.f=fd(q,e),delete d.Formula,"RC"==d.ArrayRange?d.F=fd("RC:RC",e):d.ArrayRange&&(d.F=fd(d.ArrayRange,e),j.push([cx(d.F),d.F]))}else for(p=0;p<j.length;++p)e.r>=j[p][0].s.r&&e.r<=j[p][0].e.r&&e.c>=j[p][0].s.c&&e.c<=j[p][0].e.c&&(d.F=j[p][1]);k.cellStyles&&(o.forEach(function(a){!n.patternType&&a.patternType&&(n.patternType=a.patternType)}),d.s=n),void 0!==d.StyleID&&(d.ixfe=d.StyleID)}(j.slice(E,r.index),K,D,"comment"==w[w.length-1][0]?S:B,{c:F,r:G},I,T[F],C,W,i):(K="",D=gm(r[0]),E=r.index+r[0].length);break;case"cell":if("/"===r[1])if(R.length>0&&(B.c=R),(!i.sheetRows||i.sheetRows>G)&&void 0!==B.v&&(i.dense?(z[G]||(z[G]=[]),z[G][F]=B):z[cs(F)+cq(G)]=B),B.HRef&&(B.l={Target:bg(B.HRef)},B.HRefScreenTip&&(B.l.Tooltip=B.HRefScreenTip),delete B.HRef,delete B.HRefScreenTip),(B.MergeAcross||B.MergeDown)&&(_=F+(0|parseInt(B.MergeAcross,10)),aa=G+(0|parseInt(B.MergeDown,10)),M.push({s:{c:F,r:G},e:{c:_,r:aa}})),i.sheetStubs)if(B.MergeAcross||B.MergeDown){for(var ag=F;ag<=_;++ag)for(var ah=G;ah<=aa;++ah)(ag>F||ah>G)&&(i.dense?(z[ah]||(z[ah]=[]),z[ah][ag]={t:"z"}):z[cs(ag)+cq(ah)]={t:"z"});F=_+1}else++F;else B.MergeAcross?F=_+1:++F;else(B=function(a){var b=a.split(/\s+/),c={};if(1===b.length)return c;var d,e,f,g=a.match(gk);if(g)for(f=0;f!=g.length;++f)-1===(e=(d=g[f].match(gl))[1].indexOf(":"))?c[d[1]]=d[2].slice(1,d[2].length-1):c["xmlns:"===d[1].slice(0,6)?"xmlns"+d[1].slice(6):d[1].slice(e+1)]=d[2].slice(1,d[2].length-1);return c}(r[0])).Index&&(F=B.Index-1),F<H.s.c&&(H.s.c=F),F>H.e.c&&(H.e.c=F),"/>"===r[0].slice(-2)&&++F,R=[];break;case"row":"/"===r[1]||"/>"===r[0].slice(-2)?(G<H.s.r&&(H.s.r=G),G>H.e.r&&(H.e.r=G),"/>"===r[0].slice(-2)&&(C=gm(r[0])).Index&&(G=C.Index-1),F=0,++G):((C=gm(r[0])).Index&&(G=C.Index-1),Z={},("0"==C.AutoFitHeight||C.Height)&&(Z.hpx=parseInt(C.Height,10),Z.hpt=eO(Z.hpx),X[G]=Z),"1"==C.Hidden&&(Z.hidden=!0,X[G]=Z));break;case"worksheet":if("/"===r[1]){if((s=w.pop())[0]!==r[3])throw Error("Bad state: "+s.join("|"));y.push(A),H.s.r<=H.e.r&&H.s.c<=H.e.c&&(z["!ref"]=cw(H),i.sheetRows&&i.sheetRows<=H.e.r&&(z["!fullref"]=z["!ref"],H.e.r=i.sheetRows-1,z["!ref"]=cw(H))),M.length&&(z["!merges"]=M),T.length>0&&(z["!cols"]=T),X.length>0&&(z["!rows"]=X),x[A]=z}else H={s:{r:2e6,c:2e6},e:{r:0,c:0}},G=F=0,w.push([r[3],!1]),A=bg((s=gm(r[0])).Name),z=i.dense?[]:{},M=[],W=[],X=[],ac={name:A,Hidden:0},ab.Sheets.push(ac);break;case"table":if("/"===r[1]){if((s=w.pop())[0]!==r[3])throw Error("Bad state: "+s.join("|"))}else"/>"==r[0].slice(-2)||(w.push([r[3],!1]),T=[],V=!1);break;case"style":"/"===r[1]?function(a,b,c){if(c.cellStyles&&b.Interior){var d=b.Interior;d.Pattern&&(d.patternType=eQ[d.Pattern]||d.Pattern)}a[b.ID]=b}(I,J,i):J=gm(r[0]);break;case"numberformat":J.nf=bg(gm(r[0]).Format||"General"),h[J.nf]&&(J.nf=h[J.nf]);for(var ai=0;392!=ai&&U[ai]!=J.nf;++ai);if(392==ai){for(ai=57;392!=ai;++ai)if(null==U[ai]){au(J.nf,ai);break}}break;case"column":if("table"!==w[w.length-1][0])break;if((u=gm(r[0])).Hidden&&(u.hidden=!0,delete u.Hidden),u.Width&&(u.wpx=parseInt(u.Width,10)),!V&&u.wpx>10){V=!0,eH=6;for(var aj=0;aj<T.length;++aj)T[aj]&&eN(T[aj])}V&&eN(u),T[u.Index-1||T.length]=u;for(var ak=0;ak<+u.Span;++ak)T[T.length]=aR(u);break;case"namedrange":if("/"===r[1])break;ab.Names||(ab.Names=[]);var al=bc(r[0]),am={Name:al.Name,Ref:fd(al.RefersTo.slice(1),{r:0,c:0})};ab.Sheets.length>0&&(am.Sheet=ab.Sheets.length-1),ab.Names.push(am);break;case"namedcell":case"b":case"i":case"u":case"s":case"em":case"h2":case"h3":case"sub":case"sup":case"span":case"alignment":case"borders":case"border":case"protection":case"paragraphs":case"name":case"pixelsperinch":case"null":break;case"font":"/>"===r[0].slice(-2)||("/"===r[1]?K+=j.slice(L,r.index):L=r.index+r[0].length);break;case"interior":if(!i.cellStyles)break;J.Interior=gm(r[0]);break;case"author":case"title":case"description":case"created":case"keywords":case"subject":case"category":case"company":case"lastauthor":case"lastsaved":case"lastprinted":case"version":case"revision":case"totaltime":case"hyperlinkbase":case"manager":case"contentstatus":case"identifier":case"language":case"appname":"/>"===r[0].slice(-2)||("/"===r[1]?(c=af,d=j.slice(P,r.index),f||(f=aE(dm)),N[c=f[c]||c]=d):P=r.index+r[0].length);break;case"styles":case"workbook":if("/"===r[1]){if((s=w.pop())[0]!==r[3])throw Error("Bad state: "+s.join("|"))}else w.push([r[3],!1]);break;case"comment":if("/"===r[1]){if((s=w.pop())[0]!==r[3])throw Error("Bad state: "+s.join("|"));(g=S).t=g.v||"",g.t=g.t.replace(/\r\n/g,"\n").replace(/\r/g,"\n"),g.v=g.w=g.ixfe=void 0,R.push(S)}else w.push([r[3],!1]),S={a:(s=gm(r[0])).Author};break;case"autofilter":if("/"===r[1]){if((s=w.pop())[0]!==r[3])throw Error("Bad state: "+s.join("|"))}else if("/"!==r[0].charAt(r[0].length-2)){var an=gm(r[0]);z["!autofilter"]={ref:fd(an.Range).replace(/\$/g,"")},w.push([r[3],!0])}break;case"datavalidation":case"componentoptions":case"documentproperties":case"customdocumentproperties":case"officedocumentsettings":case"pivottable":case"pivotcache":case"names":case"mapinfo":case"pagebreaks":case"querytable":case"sorting":case"schema":case"conditionalformatting":case"smarttagtype":case"smarttags":case"excelworkbook":case"workbookoptions":case"worksheetoptions":if("/"===r[1]){if((s=w.pop())[0]!==r[3])throw Error("Bad state: "+s.join("|"))}else"/"!==r[0].charAt(r[0].length-2)&&w.push([r[3],!0]);break;default:if(0==w.length&&"document"==r[3]||0==w.length&&"uof"==r[3])return gC(j,i);var ao=!0;switch(w[w.length-1][0]){case"officedocumentsettings":switch(r[3]){case"allowpng":case"removepersonalinformation":case"downloadcomponents":case"locationofcomponents":case"colors":case"color":case"index":case"rgb":case"targetscreensize":case"readonlyrecommended":break;default:ao=!1}break;case"componentoptions":switch(r[3]){case"toolbar":case"hideofficelogo":case"spreadsheetautofit":case"label":case"caption":case"maxheight":case"maxwidth":case"nextsheetnumber":break;default:ao=!1}break;case"excelworkbook":switch(r[3]){case"date1904":ab.WBProps.date1904=!0;break;case"windowheight":case"windowwidth":case"windowtopx":case"windowtopy":case"tabratio":case"protectstructure":case"protectwindow":case"protectwindows":case"activesheet":case"displayinknotes":case"firstvisiblesheet":case"supbook":case"sheetname":case"sheetindex":case"sheetindexfirst":case"sheetindexlast":case"dll":case"acceptlabelsinformulas":case"donotsavelinkvalues":case"iteration":case"maxiterations":case"maxchange":case"path":case"xct":case"count":case"selectedsheets":case"calculation":case"uncalced":case"startupprompt":case"crn":case"externname":case"formula":case"colfirst":case"collast":case"wantadvise":case"boolean":case"error":case"text":case"ole":case"noautorecover":case"publishobjects":case"donotcalculatebeforesave":case"number":case"refmoder1c1":case"embedsavesmarttags":break;default:ao=!1}break;case"workbookoptions":switch(r[3]){case"owcversion":case"height":case"width":break;default:ao=!1}break;case"worksheetoptions":switch(r[3]){case"visible":if("/>"===r[0].slice(-2));else if("/"===r[1])switch(j.slice(P,r.index)){case"SheetHidden":ac.Hidden=1;break;case"SheetVeryHidden":ac.Hidden=2}else P=r.index+r[0].length;break;case"header":z["!margins"]||fP(z["!margins"]={},"xlml"),isNaN(+bc(r[0]).Margin)||(z["!margins"].header=+bc(r[0]).Margin);break;case"footer":z["!margins"]||fP(z["!margins"]={},"xlml"),isNaN(+bc(r[0]).Margin)||(z["!margins"].footer=+bc(r[0]).Margin);break;case"pagemargins":var ap=bc(r[0]);z["!margins"]||fP(z["!margins"]={},"xlml"),isNaN(+ap.Top)||(z["!margins"].top=+ap.Top),isNaN(+ap.Left)||(z["!margins"].left=+ap.Left),isNaN(+ap.Right)||(z["!margins"].right=+ap.Right),isNaN(+ap.Bottom)||(z["!margins"].bottom=+ap.Bottom);break;case"displayrighttoleft":ab.Views||(ab.Views=[]),ab.Views[0]||(ab.Views[0]={}),ab.Views[0].RTL=!0;break;case"freezepanes":case"frozennosplit":case"splithorizontal":case"splitvertical":case"donotdisplaygridlines":case"activerow":case"activecol":case"toprowbottompane":case"leftcolumnrightpane":case"unsynced":case"print":case"printerrors":case"panes":case"scale":case"pane":case"number":case"layout":case"pagesetup":case"selected":case"protectobjects":case"enableselection":case"protectscenarios":case"validprinterinfo":case"horizontalresolution":case"verticalresolution":case"numberofcopies":case"activepane":case"toprowvisible":case"leftcolumnvisible":case"fittopage":case"rangeselection":case"papersizeindex":case"pagelayoutzoom":case"pagebreakzoom":case"filteron":case"fitwidth":case"fitheight":case"commentslayout":case"zoom":case"lefttoright":case"gridlines":case"allowsort":case"allowfilter":case"allowinsertrows":case"allowdeleterows":case"allowinsertcols":case"allowdeletecols":case"allowinserthyperlinks":case"allowformatcells":case"allowsizecols":case"allowsizerows":case"tabcolorindex":case"donotdisplayheadings":case"showpagelayoutzoom":case"blackandwhite":case"donotdisplayzeros":case"displaypagebreak":case"rowcolheadings":case"donotdisplayoutline":case"noorientation":case"allowusepivottables":case"zeroheight":case"viewablerange":case"selection":case"protectcontents":break;case"nosummaryrowsbelowdetail":z["!outline"]||(z["!outline"]={}),z["!outline"].above=!0;break;case"nosummarycolumnsrightdetail":z["!outline"]||(z["!outline"]={}),z["!outline"].left=!0;break;default:ao=!1}break;case"pivottable":case"pivotcache":switch(r[3]){case"immediateitemsondrop":case"showpagemultipleitemlabel":case"compactrowindent":case"location":case"pivotfield":case"orientation":case"layoutform":case"layoutsubtotallocation":case"layoutcompactrow":case"position":case"pivotitem":case"datatype":case"datafield":case"sourcename":case"parentfield":case"ptlineitems":case"ptlineitem":case"countofsameitems":case"item":case"itemtype":case"ptsource":case"cacheindex":case"consolidationreference":case"filename":case"reference":case"nocolumngrand":case"norowgrand":case"blanklineafteritems":case"hidden":case"subtotal":case"basefield":case"mapchilditems":case"function":case"refreshonfileopen":case"printsettitles":case"mergelabels":case"defaultversion":case"refreshname":case"refreshdate":case"refreshdatecopy":case"versionlastrefresh":case"versionlastupdate":case"versionupdateablemin":case"versionrefreshablemin":case"calculation":break;default:ao=!1}break;case"pagebreaks":switch(r[3]){case"colbreaks":case"colbreak":case"rowbreaks":case"rowbreak":case"colstart":case"colend":case"rowend":break;default:ao=!1}break;case"autofilter":switch(r[3]){case"autofiltercolumn":case"autofiltercondition":case"autofilterand":case"autofilteror":break;default:ao=!1}break;case"querytable":switch(r[3]){case"id":case"autoformatfont":case"autoformatpattern":case"querysource":case"querytype":case"enableredirections":case"refreshedinxl9":case"urlstring":case"htmltables":case"connection":case"commandtext":case"refreshinfo":case"notitles":case"nextid":case"columninfo":case"overwritecells":case"donotpromptforfile":case"textwizardsettings":case"source":case"number":case"decimal":case"thousandseparator":case"trailingminusnumbers":case"formatsettings":case"fieldtype":case"delimiters":case"tab":case"comma":case"autoformatname":case"versionlastedit":case"versionlastrefresh":break;default:ao=!1}break;case"datavalidation":switch(r[3]){case"range":case"type":case"min":case"max":case"sort":case"descending":case"order":case"casesensitive":case"value":case"errorstyle":case"errormessage":case"errortitle":case"inputmessage":case"inputtitle":case"combohide":case"inputhide":case"condition":case"qualifier":case"useblank":case"value1":case"value2":case"format":case"cellrangelist":break;default:ao=!1}break;case"sorting":case"conditionalformatting":switch(r[3]){case"range":case"type":case"min":case"max":case"sort":case"descending":case"order":case"casesensitive":case"value":case"errorstyle":case"errormessage":case"errortitle":case"cellrangelist":case"inputmessage":case"inputtitle":case"combohide":case"inputhide":case"condition":case"qualifier":case"useblank":case"value1":case"value2":case"format":break;default:ao=!1}break;case"mapinfo":case"schema":case"data":switch(r[3]){case"map":case"entry":case"range":case"xpath":case"field":case"xsdtype":case"filteron":case"aggregate":case"elementtype":case"attributetype":case"schema":case"element":case"complextype":case"datatype":case"all":case"attribute":case"extends":case"row":break;default:ao=!1}break;case"smarttags":break;default:ao=!1}if(ao||r[3].match(/!\[CDATA/))break;if(!w[w.length-1][1])throw"Unrecognized tag: "+r[3]+"|"+w.join("|");if("customdocumentproperties"===w[w.length-1][0]){"/>"===r[0].slice(-2)||("/"===r[1]?function(a,b,c,d){var e=d;switch((c[0].match(/dt:dt="([\w.]+)"/)||["",""])[1]){case"boolean":e=bo(d);break;case"i2":case"int":e=parseInt(d,10);break;case"r4":case"float":e=parseFloat(d);break;case"date":case"dateTime.tz":e=aP(d);break;case"i8":case"string":case"fixed":case"uuid":case"bin.base64":break;default:throw Error("bad custprop:"+c[0])}a[bg(b)]=e}(O,af,Q,j.slice(P,r.index)):(Q=r,P=r.index+r[0].length));break}if(i.WTF)throw"Unrecognized tag: "+r[3]+"|"+w.join("|")}var ar={};return i.bookSheets||i.bookProps||(ar.Sheets=x),ar.SheetNames=y,ar.Workbook=ab,ar.SSF=aR(U),ar.Props=N,ar.Custprops=O,ar}function go(a,b){switch(g$(b=b||{}),b.type||"base64"){case"base64":return gn(A(a),b);case"binary":case"buffer":case"file":return gn(a,b);case"array":return gn(H(a),b)}}function gp(a){return bE("NamedRange",null,{"ss:Name":a.Name,"ss:RefersTo":"="+ff(a.Ref,{r:0,c:0})})}var gq=[60,1084,2066,2165,2175];function gr(a,b,c){if("z"!==a.t&&a.XF){var d=0;try{d=a.z||a.XF.numFmtId||0,b.cellNF&&(a.z=U[d])}catch(a){if(b.WTF)throw a}if(!b||!1!==b.cellText)try{"e"===a.t?a.w=a.w||c$[a.v]:0===d||"General"==d?"n"===a.t?(0|a.v)===a.v?a.w=a.v.toString(10):a.w=ad(a.v):a.w=ae(a.v):a.w=at(d,a.v,{date1904:!!c,dateNF:b&&b.dateNF})}catch(a){if(b.WTF)throw a}if(b.cellDates&&d&&"n"==a.t&&aq(U[d]||String(d))){var e=Y(a.v);e&&(a.t="d",a.v=new Date(e.y,e.m-1,e.d,e.H,e.M,e.S,e.u))}}}function gs(a,b,c){return{v:a,ixfe:b,t:c}}var gt={SI:"e0859ff2f94f6810ab9108002b27b3d9",DSI:"02d5cdd59c2e1b10939708002b2cf9ae",UDI:"05d5cdd59c2e1b10939708002b2cf9ae"};function gu(a,b){if(b||(b={}),g$(b),s(),b.codepage&&q(b.codepage),a.FullPaths){if(aA.find(a,"/encryption"))throw Error("File is password-protected");e=aA.find(a,"!CompObj"),f=aA.find(a,"/Workbook")||aA.find(a,"/Book")}else{switch(b.type){case"base64":a=F(A(a));break;case"binary":a=F(a);break;case"buffer":break;case"array":Array.isArray(a)||(a=Array.prototype.slice.call(a))}cf(a,0),f={content:a}}if(e&&function(a){var b={},c=a.content;if(c.l=28,b.AnsiUserType=c.read_shift(0,"lpstr-ansi"),b.AnsiClipboardFormat=cT(c,1),c.length-c.l<=4)return;var d=c.read_shift(4);if(0!=d&&!(d>40)&&(c.l-=4,b.Reserved1=c.read_shift(0,"lpstr-ansi"),!(c.length-c.l<=4)&&0x71b239f4===(d=c.read_shift(4)))&&(b.UnicodeClipboardFormat=cT(c,2),0!=(d=c.read_shift(4))&&!(d>40)))c.l-=4,b.Reserved2=c.read_shift(0,"lpwstr")}(e),b.bookProps&&!b.bookSheets)g={};else{var c,d,e,f,g,h,i=B?"buffer":"array";if(f&&f.content)g=function(a,b){var c,d,e,f,g={opts:{}},h={},i,j,k,l,m,n=b.dense?[]:{},o={},p={},q=null,s=[],t="",u={},v="",w={},x=[],y=[],z=[],A={Sheets:[],WBProps:{date1904:!1},Views:[{}]},B={},C=function(a){return a<8?cZ[a]:a<64&&z[a-8]||cZ[a]},D=function(a,b,c){var d,e=b.XF.data;e&&e.patternType&&c&&c.cellStyles&&(b.s={},b.s.patternType=e.patternType,(d=eF(C(e.icvFore)))&&(b.s.fgColor={rgb:d}),(d=eF(C(e.icvBack)))&&(b.s.bgColor={rgb:d}))},E=function(a,b,c){if(!(O>1)&&(!c.sheetRows||!(a.r>=c.sheetRows))){if(c.cellStyles&&b.XF&&b.XF.data&&D(a,b,c),delete b.ixfe,delete b.XF,i=a,v=cu(a),p&&p.s&&p.e||(p={s:{r:0,c:0},e:{r:0,c:0}}),a.r<p.s.r&&(p.s.r=a.r),a.c<p.s.c&&(p.s.c=a.c),a.r+1>p.e.r&&(p.e.r=a.r+1),a.c+1>p.e.c&&(p.e.c=a.c+1),c.cellFormula&&b.f){for(var d=0;d<x.length;++d)if(!(x[d][0].s.c>a.c)&&!(x[d][0].s.r>a.r)&&!(x[d][0].e.c<a.c)&&!(x[d][0].e.r<a.r)){b.F=cw(x[d][0]),(x[d][0].s.c!=a.c||x[d][0].s.r!=a.r)&&delete b.f,b.f&&(b.f=""+fC(x[d][1],p,a,M,F));break}}c.dense?(n[a.r]||(n[a.r]=[]),n[a.r][a.c]=b):n[v]=b}},F={enc:!1,sbcch:0,snames:[],sharedf:w,arrayf:x,rrtabid:[],lastuser:"",biff:8,codepage:0,winlocked:0,cellStyles:!!b&&!!b.cellStyles,WTF:!!b&&!!b.wtf};b.password&&(F.password=b.password);var G=[],H=[],I=[],K=[],L=!1,M=[];M.SheetNames=F.snames,M.sharedf=F.sharedf,M.arrayf=F.arrayf,M.names=[],M.XTI=[];var N=0,O=0,P=0,Q=[],R=[];F.codepage=1200,r(1200);for(var S=!1;a.l<a.length-1;){var T=a.l,V=a.read_shift(2);if(0===V&&10===N)break;var W=a.l===a.length?0:a.read_shift(2),X=gw[V];if(X&&X.f){if(b.bookSheets&&133===N&&133!==V)break;if(N=V,2===X.r||12==X.r){var Y=a.read_shift(2);if(W-=2,!F.enc&&Y!==V&&((255&Y)<<8|Y>>8)!==V)throw Error("rt mismatch: "+Y+"!="+V);12==X.r&&(a.l+=10,W-=10)}var Z={};if(Z=10===V?X.f(a,W,F):function(a,b,c,d,e){var f=d,g=[],h=c.slice(c.l,c.l+f);if(e&&e.enc&&e.enc.insitu&&h.length>0)switch(a){case 9:case 521:case 1033:case 2057:case 47:case 405:case 225:case 406:case 312:case 404:case 10:case 133:break;default:e.enc.insitu(h)}g.push(h),c.l+=f;for(var i=b5(c,c.l),j=gw[i],k=0;null!=j&&gq.indexOf(i)>-1;)f=b5(c,c.l+2),k=c.l+4,2066==i?k+=4:(2165==i||2175==i)&&(k+=12),h=c.slice(k,c.l+4+f),g.push(h),c.l+=4+f,j=gw[i=b5(c,c.l)];var l=J(g);cf(l,0);var m=0;l.lens=[];for(var n=0;n<g.length;++n)l.lens.push(m),m+=g[n].length;if(l.length<d)throw"XLS Record 0x"+a.toString(16)+" Truncated: "+l.length+" < "+d;return b.f(l,l.length,e)}(V,X,a,W,F),0==O&&-1===[9,521,1033,2057].indexOf(N))continue;switch(V){case 34:g.opts.Date1904=A.WBProps.date1904=Z;break;case 134:g.opts.WriteProtect=!0;break;case 47:if(F.enc||(a.l=0),F.enc=Z,!b.password)throw Error("File is password-protected");if(null==Z.valid)throw Error("Encryption scheme unsupported");if(!Z.valid)throw Error("Password is incorrect");break;case 92:F.lastuser=Z;break;case 66:var _=Number(Z);switch(_){case 21010:_=1200;break;case 32768:_=1e4;break;case 32769:_=1252}r(F.codepage=_),S=!0;break;case 317:F.rrtabid=Z;break;case 25:F.winlocked=Z;break;case 439:g.opts.RefreshAll=Z;break;case 12:g.opts.CalcCount=Z;break;case 16:g.opts.CalcDelta=Z;break;case 17:g.opts.CalcIter=Z;break;case 13:g.opts.CalcMode=Z;break;case 14:g.opts.CalcPrecision=Z;break;case 95:g.opts.CalcSaveRecalc=Z;break;case 15:F.CalcRefMode=Z;break;case 2211:g.opts.FullCalc=Z;break;case 129:Z.fDialog&&(n["!type"]="dialog"),Z.fBelow||((n["!outline"]||(n["!outline"]={})).above=!0),Z.fRight||((n["!outline"]||(n["!outline"]={})).left=!0);break;case 224:y.push(Z);break;case 430:M.push([Z]),M[M.length-1].XTI=[];break;case 35:case 547:M[M.length-1].push(Z);break;case 24:case 536:f={Name:Z.Name,Ref:fC(Z.rgce,p,null,M,F)},Z.itab>0&&(f.Sheet=Z.itab-1),M.names.push(f),M[0]||(M[0]=[],M[0].XTI=[]),M[M.length-1].push(Z),"_xlnm._FilterDatabase"==Z.Name&&Z.itab>0&&Z.rgce&&Z.rgce[0]&&Z.rgce[0][0]&&"PtgArea3d"==Z.rgce[0][0][0]&&(R[Z.itab-1]={ref:cw(Z.rgce[0][0][1][2])});break;case 22:F.ExternCount=Z;break;case 23:0==M.length&&(M[0]=[],M[0].XTI=[]),M[M.length-1].XTI=M[M.length-1].XTI.concat(Z),M.XTI=M.XTI.concat(Z);break;case 2196:if(F.biff<8)break;null!=f&&(f.Comment=Z[1]);break;case 18:n["!protect"]=Z;break;case 19:0!==Z&&F.WTF&&console.error("Password verifier: "+Z);break;case 133:o[Z.pos]=Z,F.snames.push(Z.name);break;case 10:if(--O)break;if(p.e){if(p.e.r>0&&p.e.c>0){if(p.e.r--,p.e.c--,n["!ref"]=cw(p),b.sheetRows&&b.sheetRows<=p.e.r){var aa=p.e.r;p.e.r=b.sheetRows-1,n["!fullref"]=n["!ref"],n["!ref"]=cw(p),p.e.r=aa}p.e.r++,p.e.c++}G.length>0&&(n["!merges"]=G),H.length>0&&(n["!objects"]=H),I.length>0&&(n["!cols"]=I),K.length>0&&(n["!rows"]=K),A.Sheets.push(B)}""===t?u=n:h[t]=n,n=b.dense?[]:{};break;case 9:case 521:case 1033:case 2057:if(8===F.biff&&(F.biff=({9:2,521:3,1033:4})[V]||({512:2,768:3,1024:4,1280:5,1536:8,2:2,7:2})[Z.BIFFVer]||8),F.biffguess=0==Z.BIFFVer,0==Z.BIFFVer&&4096==Z.dt&&(F.biff=5,S=!0,r(F.codepage=28591)),8==F.biff&&0==Z.BIFFVer&&16==Z.dt&&(F.biff=2),O++)break;if(n=b.dense?[]:{},F.biff<8&&!S&&(S=!0,r(F.codepage=b.codepage||1252)),F.biff<5||0==Z.BIFFVer&&4096==Z.dt){""===t&&(t="Sheet1"),p={s:{r:0,c:0},e:{r:0,c:0}};var ab={pos:a.l-W,name:t};o[ab.pos]=ab,F.snames.push(t)}else t=(o[T]||{name:""}).name;32==Z.dt&&(n["!type"]="chart"),64==Z.dt&&(n["!type"]="macro"),G=[],H=[],F.arrayf=x=[],I=[],K=[],L=!1,B={Hidden:(o[T]||{hs:0}).hs,name:t};break;case 515:case 3:case 2:"chart"==n["!type"]&&(b.dense?(n[Z.r]||[])[Z.c]:n[cu({c:Z.c,r:Z.r})])&&++Z.c,c={ixfe:Z.ixfe,XF:y[Z.ixfe]||{},v:Z.val,t:"n"},P>0&&(c.z=Q[c.ixfe>>8&63]),gr(c,b,g.opts.Date1904),E({c:Z.c,r:Z.r},c,b);break;case 5:case 517:c={ixfe:Z.ixfe,XF:y[Z.ixfe],v:Z.val,t:Z.t},P>0&&(c.z=Q[c.ixfe>>8&63]),gr(c,b,g.opts.Date1904),E({c:Z.c,r:Z.r},c,b);break;case 638:c={ixfe:Z.ixfe,XF:y[Z.ixfe],v:Z.rknum,t:"n"},P>0&&(c.z=Q[c.ixfe>>8&63]),gr(c,b,g.opts.Date1904),E({c:Z.c,r:Z.r},c,b);break;case 189:for(var ac=Z.c;ac<=Z.C;++ac){var ad=Z.rkrec[ac-Z.c][0];c={ixfe:ad,XF:y[ad],v:Z.rkrec[ac-Z.c][1],t:"n"},P>0&&(c.z=Q[c.ixfe>>8&63]),gr(c,b,g.opts.Date1904),E({c:ac,r:Z.r},c,b)}break;case 6:case 518:case 1030:if("String"==Z.val){q=Z;break}if((c=gs(Z.val,Z.cell.ixfe,Z.tt)).XF=y[c.ixfe],b.cellFormula){var ae=Z.formula;if(ae&&ae[0]&&ae[0][0]&&"PtgExp"==ae[0][0][0]){var af=ae[0][0][1][0],ag=ae[0][0][1][1],ah=cu({r:af,c:ag});w[ah]?c.f=""+fC(Z.formula,p,Z.cell,M,F):c.F=((b.dense?(n[af]||[])[ag]:n[ah])||{}).F}else c.f=""+fC(Z.formula,p,Z.cell,M,F)}P>0&&(c.z=Q[c.ixfe>>8&63]),gr(c,b,g.opts.Date1904),E(Z.cell,c,b),q=Z;break;case 7:case 519:if(q)q.val=Z,(c=gs(Z,q.cell.ixfe,"s")).XF=y[c.ixfe],b.cellFormula&&(c.f=""+fC(q.formula,p,q.cell,M,F)),P>0&&(c.z=Q[c.ixfe>>8&63]),gr(c,b,g.opts.Date1904),E(q.cell,c,b),q=null;else throw Error("String record expects Formula");break;case 33:case 545:x.push(Z);var ai=cu(Z[0].s);if(j=b.dense?(n[Z[0].s.r]||[])[Z[0].s.c]:n[ai],b.cellFormula&&j){if(!q||!ai||!j)break;j.f=""+fC(Z[1],p,Z[0],M,F),j.F=cw(Z[0])}break;case 1212:if(!b.cellFormula)break;if(v){if(!q)break;w[cu(q.cell)]=Z[0],((j=b.dense?(n[q.cell.r]||[])[q.cell.c]:n[cu(q.cell)])||{}).f=""+fC(Z[0],p,i,M,F)}break;case 253:c=gs(s[Z.isst].t,Z.ixfe,"s"),s[Z.isst].h&&(c.h=s[Z.isst].h),c.XF=y[c.ixfe],P>0&&(c.z=Q[c.ixfe>>8&63]),gr(c,b,g.opts.Date1904),E({c:Z.c,r:Z.r},c,b);break;case 513:b.sheetStubs&&(c={ixfe:Z.ixfe,XF:y[Z.ixfe],t:"z"},P>0&&(c.z=Q[c.ixfe>>8&63]),gr(c,b,g.opts.Date1904),E({c:Z.c,r:Z.r},c,b));break;case 190:if(b.sheetStubs)for(var aj=Z.c;aj<=Z.C;++aj){var ak=Z.ixfe[aj-Z.c];c={ixfe:ak,XF:y[ak],t:"z"},P>0&&(c.z=Q[c.ixfe>>8&63]),gr(c,b,g.opts.Date1904),E({c:aj,r:Z.r},c,b)}break;case 214:case 516:case 4:(c=gs(Z.val,Z.ixfe,"s")).XF=y[c.ixfe],P>0&&(c.z=Q[c.ixfe>>8&63]),gr(c,b,g.opts.Date1904),E({c:Z.c,r:Z.r},c,b);break;case 0:case 512:1===O&&(p=Z);break;case 252:s=Z;break;case 1054:if(4==F.biff){Q[P++]=Z[1];for(var al=0;al<P+163&&U[al]!=Z[1];++al);al>=163&&au(Z[1],P+163)}else au(Z[1],Z[0]);break;case 30:Q[P++]=Z;for(var am=0;am<P+163&&U[am]!=Z;++am);am>=163&&au(Z,P+163);break;case 229:G=G.concat(Z);break;case 93:H[Z.cmo[0]]=F.lastobj=Z;break;case 438:F.lastobj.TxO=Z;break;case 127:F.lastobj.ImData=Z;break;case 440:for(m=Z[0].s.r;m<=Z[0].e.r;++m)for(l=Z[0].s.c;l<=Z[0].e.c;++l)(j=b.dense?(n[m]||[])[l]:n[cu({c:l,r:m})])&&(j.l=Z[1]);break;case 2048:for(m=Z[0].s.r;m<=Z[0].e.r;++m)for(l=Z[0].s.c;l<=Z[0].e.c;++l)(j=b.dense?(n[m]||[])[l]:n[cu({c:l,r:m})])&&j.l&&(j.l.Tooltip=Z[1]);break;case 28:if(F.biff<=5&&F.biff>=2)break;j=b.dense?(n[Z[0].r]||[])[Z[0].c]:n[cu(Z[0])];var an=H[Z[2]];j||(b.dense?(n[Z[0].r]||(n[Z[0].r]=[]),j=n[Z[0].r][Z[0].c]={t:"z"}):j=n[cu(Z[0])]={t:"z"},p.e.r=Math.max(p.e.r,Z[0].r),p.s.r=Math.min(p.s.r,Z[0].r),p.e.c=Math.max(p.e.c,Z[0].c),p.s.c=Math.min(p.s.c,Z[0].c)),j.c||(j.c=[]),k={a:Z[1],t:an.TxO.t},j.c.push(k);break;case 2173:y[Z.ixfe],Z.ext.forEach(function(a){a[0]});break;case 125:if(!F.cellStyles)break;for(;Z.e>=Z.s;)I[Z.e--]={width:Z.w/256,level:Z.level||0,hidden:!!(1&Z.flags)},L||(L=!0,eM(Z.w/256)),eN(I[Z.e+1]);break;case 520:var ao={};null!=Z.level&&(K[Z.r]=ao,ao.level=Z.level),Z.hidden&&(K[Z.r]=ao,ao.hidden=!0),Z.hpt&&(K[Z.r]=ao,ao.hpt=Z.hpt,ao.hpx=eP(Z.hpt));break;case 38:case 39:case 40:case 41:n["!margins"]||fP(n["!margins"]={}),n["!margins"][({38:"left",39:"right",40:"top",41:"bottom"})[V]]=Z;break;case 161:n["!margins"]||fP(n["!margins"]={}),n["!margins"].header=Z.header,n["!margins"].footer=Z.footer;break;case 574:Z.RTL&&(A.Views[0].RTL=!0);break;case 146:z=Z;break;case 2198:e=Z;break;case 140:d=Z;break;case 442:t?B.CodeName=Z||B.name:A.WBProps.CodeName=Z||"ThisWorkbook"}}else X||console.error("Missing Info for XLS Record 0x"+V.toString(16)),a.l+=W}return g.SheetNames=aC(o).sort(function(a,b){return Number(a)-Number(b)}).map(function(a){return o[a].name}),b.bookSheets||(g.Sheets=h),!g.SheetNames.length&&u["!ref"]?(g.SheetNames.push("Sheet1"),g.Sheets&&(g.Sheets.Sheet1=u)):g.Preamble=u,g.Sheets&&R.forEach(function(a,b){g.Sheets[g.SheetNames[b]]["!autofilter"]=a}),g.Strings=s,g.SSF=aR(U),F.enc&&(g.Encryption=F.enc),e&&(g.Themes=e),g.Metadata={},void 0!==d&&(g.Metadata.Country=d),M.names.length>0&&(A.Names=M.names),g.Workbook=A,g}(f.content,b);else if((h=aA.find(a,"PerfectOffice_MAIN"))&&h.content)g=ej.to_workbook(h.content,(b.type=i,b));else if((h=aA.find(a,"NativeContent_MAIN"))&&h.content)g=ej.to_workbook(h.content,(b.type=i,b));else if((h=aA.find(a,"MN0"))&&h.content)throw Error("Unsupported Works 4 for Mac file");else throw Error("Cannot find Workbook stream");b.bookVBA&&a.FullPaths&&aA.find(a,"/_VBA_PROJECT_CUR/VBA/dir")&&(g.vbaraw=(c=a,d=aA.utils.cfb_new({root:"R"}),c.FullPaths.forEach(function(a,b){if("/"!==a.slice(-1)&&a.match(/_VBA_PROJECT_CUR/)){var e=a.replace(/^[^\/]*/,"R").replace(/\/_VBA_PROJECT_CUR\u0000*/,"");aA.utils.cfb_add(d,e,c.FileIndex[b].content)}}),aA.write(d)))}var j={};return a.FullPaths&&function(a,b,c){var d=aA.find(a,"/!DocumentSummaryInformation");if(d&&d.size>0)try{var e=dA(d,cV,gt.DSI);for(var f in e)b[f]=e[f]}catch(a){if(c.WTF)throw a}var g=aA.find(a,"/!SummaryInformation");if(g&&g.size>0)try{var h=dA(g,cW,gt.SI);for(var i in h)null==b[i]&&(b[i]=h[i])}catch(a){if(c.WTF)throw a}b.HeadingPairs&&b.TitlesOfParts&&(di(b.HeadingPairs,b.TitlesOfParts,b,c),delete b.HeadingPairs,delete b.TitlesOfParts)}(a,j,b),g.Props=g.Custprops=j,b.bookFiles&&(g.cfb=a),g}var gv={0:{f:function(a,b){var c={},d=a.l+b;c.r=a.read_shift(4),a.l+=4;var e=a.read_shift(2);a.l+=1;var f=a.read_shift(1);return a.l=d,7&f&&(c.level=7&f),16&f&&(c.hidden=!0),32&f&&(c.hpt=e/20),c}},1:{f:function(a){return[cG(a)]}},2:{f:function(a){return[cG(a),cM(a),"n"]}},3:{f:function(a){return[cG(a),a.read_shift(1),"e"]}},4:{f:function(a){return[cG(a),a.read_shift(1),"b"]}},5:{f:function(a){return[cG(a),cQ(a),"n"]}},6:{f:function(a){return[cG(a),cD(a),"str"]}},7:{f:function(a){return[cG(a),a.read_shift(4),"s"]}},8:{f:function(a,b,c){var d=a.l+b,e=cG(a);e.r=c["!row"];var f=[e,cD(a),"str"];if(c.cellFormula){a.l+=2;var g=fE(a,d-a.l,c);f[3]=fC(g,null,e,c.supbooks,c)}else a.l=d;return f}},9:{f:function(a,b,c){var d=a.l+b,e=cG(a);e.r=c["!row"];var f=[e,cQ(a),"n"];if(c.cellFormula){a.l+=2;var g=fE(a,d-a.l,c);f[3]=fC(g,null,e,c.supbooks,c)}else a.l=d;return f}},10:{f:function(a,b,c){var d=a.l+b,e=cG(a);e.r=c["!row"];var f=[e,a.read_shift(1),"b"];if(c.cellFormula){a.l+=2;var g=fE(a,d-a.l,c);f[3]=fC(g,null,e,c.supbooks,c)}else a.l=d;return f}},11:{f:function(a,b,c){var d=a.l+b,e=cG(a);e.r=c["!row"];var f=[e,a.read_shift(1),"e"];if(c.cellFormula){a.l+=2;var g=fE(a,d-a.l,c);f[3]=fC(g,null,e,c.supbooks,c)}else a.l=d;return f}},12:{f:function(a){return[cI(a)]}},13:{f:function(a){return[cI(a),cM(a),"n"]}},14:{f:function(a){return[cI(a),a.read_shift(1),"e"]}},15:{f:function(a){return[cI(a),a.read_shift(1),"b"]}},16:{f:f6},17:{f:function(a){return[cI(a),cD(a),"str"]}},18:{f:function(a){return[cI(a),a.read_shift(4),"s"]}},19:{f:cF},20:{},21:{},22:{},23:{},24:{},25:{},26:{},27:{},28:{},29:{},30:{},31:{},32:{},33:{},34:{},35:{T:1},36:{T:-1},37:{T:1},38:{T:-1},39:{f:function(a,b,c){var d=a.l+b;a.l+=4,a.l+=1;var e=a.read_shift(4),f=cD(a),g=fE(a,0,c),h=cK(a);a.l=d;var i={Name:f,Ptg:g};return e<0xfffffff&&(i.Sheet=e),h&&(i.Comment=h),i}},40:{},42:{},43:{f:function(a,b,c){var d,e={};e.sz=a.read_shift(2)/20;var f=(d=a.read_shift(1),a.l++,{fBold:1&d,fItalic:2&d,fUnderline:4&d,fStrikeout:8&d,fOutline:16&d,fShadow:32&d,fCondense:64&d,fExtend:128&d});switch(f.fItalic&&(e.italic=1),f.fCondense&&(e.condense=1),f.fExtend&&(e.extend=1),f.fShadow&&(e.shadow=1),f.fOutline&&(e.outline=1),f.fStrikeout&&(e.strike=1),700===a.read_shift(2)&&(e.bold=1),a.read_shift(2)){case 1:e.vertAlign="superscript";break;case 2:e.vertAlign="subscript"}var g=a.read_shift(1);0!=g&&(e.underline=g);var h=a.read_shift(1);h>0&&(e.family=h);var i=a.read_shift(1);switch(i>0&&(e.charset=i),a.l++,e.color=function(a){var b={},c=a.read_shift(1),d=a.read_shift(1),e=a.read_shift(2,"i"),f=a.read_shift(1),g=a.read_shift(1),h=a.read_shift(1);switch(a.l++,c>>>1){case 0:b.auto=1;break;case 1:b.index=d;var i=cZ[d];i&&(b.rgb=eF(i));break;case 2:b.rgb=eF([f,g,h]);break;case 3:b.theme=d}return 0!=e&&(b.tint=e>0?e/32767:e/32768),b}(a,8),a.read_shift(1)){case 1:e.scheme="major";break;case 2:e.scheme="minor"}return e.name=cD(a,b-21),e}},44:{f:function(a,b){return[a.read_shift(2),cD(a,b-2)]}},45:{f:cg},46:{f:cg},47:{f:function(a,b){var c=a.l+b,d=a.read_shift(2),e=a.read_shift(2);return a.l=c,{ixfe:d,numFmtId:e}}},48:{},49:{f:function(a){return a.read_shift(4,"i")}},50:{},51:{f:function(a){for(var b=[],c=a.read_shift(4);c-- >0;)b.push([a.read_shift(4),a.read_shift(4)]);return b}},52:{T:1},53:{T:-1},54:{T:1},55:{T:-1},56:{T:1},57:{T:-1},58:{},59:{},60:{f:ec},62:{f:function(a){return[cG(a),cF(a),"is"]}},63:{f:function(a){var b={};b.i=a.read_shift(4);var c={};c.r=a.read_shift(4),c.c=a.read_shift(4),b.r=cu(c);var d=a.read_shift(1);return 2&d&&(b.l="1"),8&d&&(b.a="1"),b}},64:{f:function(){}},65:{},66:{},67:{},68:{},69:{},70:{},128:{},129:{T:1},130:{T:-1},131:{T:1,f:cg,p:0},132:{T:-1},133:{T:1},134:{T:-1},135:{T:1},136:{T:-1},137:{T:1,f:function(a){var b=a.read_shift(2);return a.l+=28,{RTL:32&b}}},138:{T:-1},139:{T:1},140:{T:-1},141:{T:1},142:{T:-1},143:{T:1},144:{T:-1},145:{T:1},146:{T:-1},147:{f:function(a,b){var c={},d=a[a.l];return++a.l,c.above=!(64&d),c.left=!(128&d),a.l+=18,c.name=cD(a,b-19),c}},148:{f:cO,p:16},151:{f:function(){}},152:{},153:{f:function(a,b){var c={},d=a.read_shift(4);c.defaultThemeVersion=a.read_shift(4);var e=b>8?cD(a):"";return e.length>0&&(c.CodeName=e),c.autoCompressPictures=!!(65536&d),c.backupFile=!!(64&d),c.checkCompatibility=!!(4096&d),c.date1904=!!(1&d),c.filterPrivacy=!!(8&d),c.hidePivotFieldList=!!(1024&d),c.promptedSolutions=!!(16&d),c.publishItems=!!(2048&d),c.refreshAllConnections=!!(262144&d),c.saveExternalLinkValues=!!(128&d),c.showBorderUnselectedTables=!!(4&d),c.showInkAnnotation=!!(32&d),c.showObjects=["all","placeholders","none"][d>>13&3],c.showPivotChartFilter=!!(32768&d),c.updateLinks=["userSet","never","always"][d>>8&3],c}},154:{},155:{},156:{f:function(a,b){var c={};return c.Hidden=a.read_shift(4),c.iTabID=a.read_shift(4),c.strRelID=cK(a,b-8),c.name=cD(a),c}},157:{},158:{},159:{T:1,f:function(a){return[a.read_shift(4),a.read_shift(4)]}},160:{T:-1},161:{T:1,f:cO},162:{T:-1},163:{T:1},164:{T:-1},165:{T:1},166:{T:-1},167:{},168:{},169:{},170:{},171:{},172:{T:1},173:{T:-1},174:{},175:{},176:{f:cO},177:{T:1},178:{T:-1},179:{T:1},180:{T:-1},181:{T:1},182:{T:-1},183:{T:1},184:{T:-1},185:{T:1},186:{T:-1},187:{T:1},188:{T:-1},189:{T:1},190:{T:-1},191:{T:1},192:{T:-1},193:{T:1},194:{T:-1},195:{T:1},196:{T:-1},197:{T:1},198:{T:-1},199:{T:1},200:{T:-1},201:{T:1},202:{T:-1},203:{T:1},204:{T:-1},205:{T:1},206:{T:-1},207:{T:1},208:{T:-1},209:{T:1},210:{T:-1},211:{T:1},212:{T:-1},213:{T:1},214:{T:-1},215:{T:1},216:{T:-1},217:{T:1},218:{T:-1},219:{T:1},220:{T:-1},221:{T:1},222:{T:-1},223:{T:1},224:{T:-1},225:{T:1},226:{T:-1},227:{T:1},228:{T:-1},229:{T:1},230:{T:-1},231:{T:1},232:{T:-1},233:{T:1},234:{T:-1},235:{T:1},236:{T:-1},237:{T:1},238:{T:-1},239:{T:1},240:{T:-1},241:{T:1},242:{T:-1},243:{T:1},244:{T:-1},245:{T:1},246:{T:-1},247:{T:1},248:{T:-1},249:{T:1},250:{T:-1},251:{T:1},252:{T:-1},253:{T:1},254:{T:-1},255:{T:1},256:{T:-1},257:{T:1},258:{T:-1},259:{T:1},260:{T:-1},261:{T:1},262:{T:-1},263:{T:1},264:{T:-1},265:{T:1},266:{T:-1},267:{T:1},268:{T:-1},269:{T:1},270:{T:-1},271:{T:1},272:{T:-1},273:{T:1},274:{T:-1},275:{T:1},276:{T:-1},277:{},278:{T:1},279:{T:-1},280:{T:1},281:{T:-1},282:{T:1},283:{T:1},284:{T:-1},285:{T:1},286:{T:-1},287:{T:1},288:{T:-1},289:{T:1},290:{T:-1},291:{T:1},292:{T:-1},293:{T:1},294:{T:-1},295:{T:1},296:{T:-1},297:{T:1},298:{T:-1},299:{T:1},300:{T:-1},301:{T:1},302:{T:-1},303:{T:1},304:{T:-1},305:{T:1},306:{T:-1},307:{T:1},308:{T:-1},309:{T:1},310:{T:-1},311:{T:1},312:{T:-1},313:{T:-1},314:{T:1},315:{T:-1},316:{T:1},317:{T:-1},318:{T:1},319:{T:-1},320:{T:1},321:{T:-1},322:{T:1},323:{T:-1},324:{T:1},325:{T:-1},326:{T:1},327:{T:-1},328:{T:1},329:{T:-1},330:{T:1},331:{T:-1},332:{T:1},333:{T:-1},334:{T:1},335:{f:function(a,b){return{flags:a.read_shift(4),version:a.read_shift(4),name:cD(a,b-8)}}},336:{T:-1},337:{f:function(a){return a.l+=4,0!=a.read_shift(4)},T:1},338:{T:-1},339:{T:1},340:{T:-1},341:{T:1},342:{T:-1},343:{T:1},344:{T:-1},345:{T:1},346:{T:-1},347:{T:1},348:{T:-1},349:{T:1},350:{T:-1},351:{},352:{},353:{T:1},354:{T:-1},355:{f:cK},357:{},358:{},359:{},360:{T:1},361:{},362:{f:d9},363:{},364:{},366:{},367:{},368:{},369:{},370:{},371:{},372:{T:1},373:{T:-1},374:{T:1},375:{T:-1},376:{T:1},377:{T:-1},378:{T:1},379:{T:-1},380:{T:1},381:{T:-1},382:{T:1},383:{T:-1},384:{T:1},385:{T:-1},386:{T:1},387:{T:-1},388:{T:1},389:{T:-1},390:{T:1},391:{T:-1},392:{T:1},393:{T:-1},394:{T:1},395:{T:-1},396:{},397:{},398:{},399:{},400:{},401:{T:1},403:{},404:{},405:{},406:{},407:{},408:{},409:{},410:{},411:{},412:{},413:{},414:{},415:{},416:{},417:{},418:{},419:{},420:{},421:{},422:{T:1},423:{T:1},424:{T:-1},425:{T:-1},426:{f:function(a,b,c){var d=a.l+b,e=cO(a,16),f=a.read_shift(1),g=[e];if(g[2]=f,c.cellFormula){var h=fE(a,d-a.l,c);g[1]=h}else a.l=d;return g}},427:{f:function(a,b,c){var d=a.l+b,e=[cO(a,16)];if(c.cellFormula){var f=fE(a,d-a.l,c);e[1]=f,a.l=d}else a.l=d;return e}},428:{},429:{T:1},430:{T:-1},431:{T:1},432:{T:-1},433:{T:1},434:{T:-1},435:{T:1},436:{T:-1},437:{T:1},438:{T:-1},439:{T:1},440:{T:-1},441:{T:1},442:{T:-1},443:{T:1},444:{T:-1},445:{T:1},446:{T:-1},447:{T:1},448:{T:-1},449:{T:1},450:{T:-1},451:{T:1},452:{T:-1},453:{T:1},454:{T:-1},455:{T:1},456:{T:-1},457:{T:1},458:{T:-1},459:{T:1},460:{T:-1},461:{T:1},462:{T:-1},463:{T:1},464:{T:-1},465:{T:1},466:{T:-1},467:{T:1},468:{T:-1},469:{T:1},470:{T:-1},471:{},472:{},473:{T:1},474:{T:-1},475:{},476:{f:function(a){var b={};return f7.forEach(function(c){b[c]=cQ(a,8)}),b}},477:{},478:{},479:{T:1},480:{T:-1},481:{T:1},482:{T:-1},483:{T:1},484:{T:-1},485:{f:function(){}},486:{T:1},487:{T:-1},488:{T:1},489:{T:-1},490:{T:1},491:{T:-1},492:{T:1},493:{T:-1},494:{f:function(a,b){var c=a.l+b,d=cO(a,16),e=cK(a),f=cD(a),g=cD(a),h=cD(a);a.l=c;var i={rfx:d,relId:e,loc:f,display:h};return g&&(i.Tooltip=g),i}},495:{T:1},496:{T:-1},497:{T:1},498:{T:-1},499:{},500:{T:1},501:{T:-1},502:{T:1},503:{T:-1},504:{},505:{T:1},506:{T:-1},507:{},508:{T:1},509:{T:-1},510:{T:1},511:{T:-1},512:{},513:{},514:{T:1},515:{T:-1},516:{T:1},517:{T:-1},518:{T:1},519:{T:-1},520:{T:1},521:{T:-1},522:{},523:{},524:{},525:{},526:{},527:{},528:{T:1},529:{T:-1},530:{T:1},531:{T:-1},532:{T:1},533:{T:-1},534:{},535:{},536:{},537:{},538:{T:1},539:{T:-1},540:{T:1},541:{T:-1},542:{T:1},548:{},549:{},550:{f:cK},551:{},552:{},553:{},554:{T:1},555:{T:-1},556:{T:1},557:{T:-1},558:{T:1},559:{T:-1},560:{T:1},561:{T:-1},562:{},564:{},565:{T:1},566:{T:-1},569:{T:1},570:{T:-1},572:{},573:{T:1},574:{T:-1},577:{},578:{},579:{},580:{},581:{},582:{},583:{},584:{},585:{},586:{},587:{},588:{T:-1},589:{},590:{T:1},591:{T:-1},592:{T:1},593:{T:-1},594:{T:1},595:{T:-1},596:{},597:{T:1},598:{T:-1},599:{T:1},600:{T:-1},601:{T:1},602:{T:-1},603:{T:1},604:{T:-1},605:{T:1},606:{T:-1},607:{},608:{T:1},609:{T:-1},610:{},611:{T:1},612:{T:-1},613:{T:1},614:{T:-1},615:{T:1},616:{T:-1},617:{T:1},618:{T:-1},619:{T:1},620:{T:-1},625:{},626:{T:1},627:{T:-1},628:{T:1},629:{T:-1},630:{T:1},631:{T:-1},632:{f:cD},633:{T:1},634:{T:-1},635:{T:1,f:function(a){var b={};b.iauthor=a.read_shift(4);var c=cO(a,16);return b.rfx=c.s,b.ref=cu(c.s),a.l+=16,b}},636:{T:-1},637:{f:cF},638:{T:1},639:{},640:{T:-1},641:{T:1},642:{T:-1},643:{T:1},644:{},645:{T:-1},646:{T:1},648:{T:1},649:{},650:{T:-1},651:{f:function(a,b){return a.l+=10,{name:cD(a,b-10)}}},652:{},653:{T:1},654:{T:-1},655:{T:1},656:{T:-1},657:{T:1},658:{T:-1},659:{},660:{T:1},661:{},662:{T:-1},663:{},664:{T:1},665:{},666:{T:-1},667:{},668:{},669:{},671:{T:1},672:{T:-1},673:{T:1},674:{T:-1},675:{},676:{},677:{},678:{},679:{},680:{},681:{},1024:{},1025:{},1026:{T:1},1027:{T:-1},1028:{T:1},1029:{T:-1},1030:{},1031:{T:1},1032:{T:-1},1033:{T:1},1034:{T:-1},1035:{},1036:{},1037:{},1038:{T:1},1039:{T:-1},1040:{},1041:{T:1},1042:{T:-1},1043:{},1044:{},1045:{},1046:{T:1},1047:{T:-1},1048:{T:1},1049:{T:-1},1050:{},1051:{T:1},1052:{T:1},1053:{f:function(){}},1054:{T:1},1055:{},1056:{T:1},1057:{T:-1},1058:{T:1},1059:{T:-1},1061:{},1062:{T:1},1063:{T:-1},1064:{T:1},1065:{T:-1},1066:{T:1},1067:{T:-1},1068:{T:1},1069:{T:-1},1070:{T:1},1071:{T:-1},1072:{T:1},1073:{T:-1},1075:{T:1},1076:{T:-1},1077:{T:1},1078:{T:-1},1079:{T:1},1080:{T:-1},1081:{T:1},1082:{T:-1},1083:{T:1},1084:{T:-1},1085:{},1086:{T:1},1087:{T:-1},1088:{T:1},1089:{T:-1},1090:{T:1},1091:{T:-1},1092:{T:1},1093:{T:-1},1094:{T:1},1095:{T:-1},1096:{},1097:{T:1},1098:{},1099:{T:-1},1100:{T:1},1101:{T:-1},1102:{},1103:{},1104:{},1105:{},1111:{},1112:{},1113:{T:1},1114:{T:-1},1115:{T:1},1116:{T:-1},1117:{},1118:{T:1},1119:{T:-1},1120:{T:1},1121:{T:-1},1122:{T:1},1123:{T:-1},1124:{T:1},1125:{T:-1},1126:{},1128:{T:1},1129:{T:-1},1130:{},1131:{T:1},1132:{T:-1},1133:{T:1},1134:{T:-1},1135:{T:1},1136:{T:-1},1137:{T:1},1138:{T:-1},1139:{T:1},1140:{T:-1},1141:{},1142:{T:1},1143:{T:-1},1144:{T:1},1145:{T:-1},1146:{},1147:{T:1},1148:{T:-1},1149:{T:1},1150:{T:-1},1152:{T:1},1153:{T:-1},1154:{T:-1},1155:{T:-1},1156:{T:-1},1157:{T:1},1158:{T:-1},1159:{T:1},1160:{T:-1},1161:{T:1},1162:{T:-1},1163:{T:1},1164:{T:-1},1165:{T:1},1166:{T:-1},1167:{T:1},1168:{T:-1},1169:{T:1},1170:{T:-1},1171:{},1172:{T:1},1173:{T:-1},1177:{},1178:{T:1},1180:{},1181:{},1182:{},2048:{T:1},2049:{T:-1},2050:{},2051:{T:1},2052:{T:-1},2053:{},2054:{},2055:{T:1},2056:{T:-1},2057:{T:1},2058:{T:-1},2060:{},2067:{},2068:{T:1},2069:{T:-1},2070:{},2071:{},2072:{T:1},2073:{T:-1},2075:{},2076:{},2077:{T:1},2078:{T:-1},2079:{},2080:{T:1},2081:{T:-1},2082:{},2083:{T:1},2084:{T:-1},2085:{T:1},2086:{T:-1},2087:{T:1},2088:{T:-1},2089:{T:1},2090:{T:-1},2091:{},2092:{},2093:{T:1},2094:{T:-1},2095:{},2096:{T:1},2097:{T:-1},2098:{T:1},2099:{T:-1},2100:{T:1},2101:{T:-1},2102:{},2103:{T:1},2104:{T:-1},2105:{},2106:{T:1},2107:{T:-1},2108:{},2109:{T:1},2110:{T:-1},2111:{T:1},2112:{T:-1},2113:{T:1},2114:{T:-1},2115:{},2116:{},2117:{},2118:{T:1},2119:{T:-1},2120:{},2121:{T:1},2122:{T:-1},2123:{T:1},2124:{T:-1},2125:{},2126:{T:1},2127:{T:-1},2128:{},2129:{T:1},2130:{T:-1},2131:{T:1},2132:{T:-1},2133:{T:1},2134:{},2135:{},2136:{},2137:{T:1},2138:{T:-1},2139:{T:1},2140:{T:-1},2141:{},3072:{},3073:{},4096:{T:1},4097:{T:-1},5002:{T:1},5003:{T:-1},5081:{T:1},5082:{T:-1},5083:{},5084:{T:1},5085:{T:-1},5086:{T:1},5087:{T:-1},5088:{},5089:{},5090:{},5092:{T:1},5093:{T:-1},5094:{},5095:{T:1},5096:{T:-1},5097:{},5099:{},65535:{n:""}},gw={6:{f:fD},10:{f:dC},12:{f:dF},13:{f:dF},14:{f:dD},15:{f:dD},16:{f:cQ},17:{f:dD},18:{f:dD},19:{f:dF},20:{f:d5},21:{f:d5},23:{f:d9},24:{f:d8},25:{f:dD},26:{},27:{},28:{f:function(a,b,c){return function(a,b,c){if(!(c.biff<8)){var d=a.read_shift(2),e=a.read_shift(2),f=a.read_shift(2),g=a.read_shift(2),h=dM(a,0,c);return c.biff<8&&a.read_shift(1),[{r:d,c:e},h,g,f]}}(a,0,c)}},29:{},34:{f:dD},35:{f:d6},38:{f:cQ},39:{f:cQ},40:{f:cQ},41:{f:cQ},42:{f:dD},43:{f:dD},47:{f:function(a,b,c){var d,e,f,g={Type:c.biff>=8?a.read_shift(2):0};return g.Type?(d=b-2,(e=g||{}).Info=a.read_shift(2),a.l-=2,1===e.Info?e.Data=function(a){var b={},c=b.EncryptionVersionInfo=ex(a,4);if(1!=c.Major||1!=c.Minor)throw"unrecognized version code "+c.Major+" : "+c.Minor;return b.Salt=a.read_shift(16),b.EncryptedVerifier=a.read_shift(16),b.EncryptedVerifierHash=a.read_shift(16),b}(a,d):e.Data=function(a,b){var c={},d=c.EncryptionVersionInfo=ex(a,4);if(b-=4,2!=d.Minor)throw Error("unrecognized minor version code: "+d.Minor);if(d.Major>4||d.Major<2)throw Error("unrecognized major version code: "+d.Major);c.Flags=a.read_shift(4),b-=4;var e=a.read_shift(4);return b-=4,c.EncryptionHeader=ey(a,e),c.EncryptionVerifier=ez(a,b-=e),c}(a,d)):(c.biff,f={key:dF(a),verificationBytes:dF(a)},c.password&&(f.verifier=eA(c.password)),g.valid=f.verificationBytes===f.verifier,g.valid&&(g.insitu=eD(c.password))),g}},49:{f:function(a,b,c){var d={dyHeight:a.read_shift(2),fl:a.read_shift(2)};switch(c&&c.biff||8){case 2:break;case 3:case 4:a.l+=2;break;default:a.l+=10}return d.name=dJ(a,0,c),d}},51:{f:dF},60:{},61:{f:function(a){return{Pos:[a.read_shift(2),a.read_shift(2)],Dim:[a.read_shift(2),a.read_shift(2)],Flags:a.read_shift(2),CurTab:a.read_shift(2),FirstTab:a.read_shift(2),Selected:a.read_shift(2),TabRatio:a.read_shift(2)}}},64:{f:dD},65:{f:function(){}},66:{f:dF},77:{},80:{},81:{},82:{},85:{f:dF},89:{},90:{},91:{},92:{f:function(a,b,c){if(c.enc)return a.l+=b,"";var d=a.l,e=dM(a,0,c);return a.read_shift(b+d-a.l),e}},93:{f:function(a,b,c){if(c&&c.biff<8){var d,e,f,g,h,i,j;return d=a,e=b,f=c,d.l+=4,g=d.read_shift(2),h=d.read_shift(2),i=d.read_shift(2),d.l+=2,d.l+=2,d.l+=2,d.l+=2,d.l+=2,d.l+=2,d.l+=2,d.l+=2,d.l+=2,d.l+=6,e-=36,(j=[]).push((eb[g]||cg)(d,e,f)),{cmo:[h,g,i],ft:j}}var k=dY(a,22),l=function(a,b){for(var c=a.l+b,d=[];a.l<c;){var e=a.read_shift(2);a.l-=2;try{d.push(d$[e](a,c-a.l))}catch(b){return a.l=c,d}}return a.l!=c&&(a.l=c),d}(a,b-22,k[1]);return{cmo:k,ft:l}}},94:{},95:{f:dD},96:{},97:{},99:{f:dD},125:{f:ec},128:{f:function(a){a.l+=4;var b=[a.read_shift(2),a.read_shift(2)];if(0!==b[0]&&b[0]--,0!==b[1]&&b[1]--,b[0]>7||b[1]>7)throw Error("Bad Gutters: "+b.join("|"));return b}},129:{f:function(a,b,c){var d=c&&8==c.biff||2==b?a.read_shift(2):(a.l+=b,0);return{fDialog:16&d,fBelow:64&d,fRight:128&d}}},130:{f:dF},131:{f:dD},132:{f:dD},133:{f:function(a,b,c){var d=a.read_shift(4),e=3&a.read_shift(1),f=a.read_shift(1);switch(f){case 0:f="Worksheet";break;case 1:f="Macrosheet";break;case 2:f="Chartsheet";break;case 6:f="VBAModule"}var g=dJ(a,0,c);return 0===g.length&&(g="Sheet1"),{pos:d,hs:e,dt:f,name:g}}},134:{},140:{f:function(a){var b,c=[0,0];return b=a.read_shift(2),c[0]=cX[b]||b,b=a.read_shift(2),c[1]=cX[b]||b,c}},141:{f:dF},144:{},146:{f:function(a){for(var b=a.read_shift(2),c=[];b-- >0;)c.push(dR(a,8));return c}},151:{},152:{},153:{},154:{},155:{},156:{f:dF},157:{},158:{},160:{f:dH},161:{f:function(a,b){var c={};return b<32||(a.l+=16,c.header=cQ(a,8),c.footer=cQ(a,8),a.l+=2),c}},174:{},175:{},176:{},177:{},178:{},180:{},181:{},182:{},184:{},185:{},189:{f:function(a,b){for(var c=a.l+b-2,d=a.read_shift(2),e=a.read_shift(2),f=[];a.l<c;)f.push(dU(a));if(a.l!==c)throw Error("MulRK read error");var g=a.read_shift(2);if(f.length!=g-e+1)throw Error("MulRK length mismatch");return{r:d,c:e,C:g,rkrec:f}}},190:{f:function(a,b){for(var c=a.l+b-2,d=a.read_shift(2),e=a.read_shift(2),f=[];a.l<c;)f.push(a.read_shift(2));if(a.l!==c)throw Error("MulBlank read error");var g=a.read_shift(2);if(f.length!=g-e+1)throw Error("MulBlank length mismatch");return{r:d,c:e,C:g,ixfe:f}}},193:{f:dC},197:{},198:{},199:{},200:{},201:{},202:{f:dD},203:{},204:{},205:{},206:{},207:{},208:{},209:{},210:{},211:{},213:{},215:{},216:{},217:{},218:{f:dF},220:{},221:{f:dD},222:{},224:{f:function(a,b,c){var d,e,f,g,h,i={};return i.ifnt=a.read_shift(2),i.numFmtId=a.read_shift(2),i.flags=a.read_shift(2),i.fStyle=i.flags>>2&1,b-=6,i.fStyle,d={},e=a.read_shift(4),f=a.read_shift(4),g=a.read_shift(4),h=a.read_shift(2),d.patternType=cY[g>>26],c.cellStyles&&(d.alc=7&e,d.fWrap=e>>3&1,d.alcV=e>>4&7,d.fJustLast=e>>7&1,d.trot=e>>8&255,d.cIndent=e>>16&15,d.fShrinkToFit=e>>20&1,d.iReadOrder=e>>22&2,d.fAtrNum=e>>26&1,d.fAtrFnt=e>>27&1,d.fAtrAlc=e>>28&1,d.fAtrBdr=e>>29&1,d.fAtrPat=e>>30&1,d.fAtrProt=e>>31&1,d.dgLeft=15&f,d.dgRight=f>>4&15,d.dgTop=f>>8&15,d.dgBottom=f>>12&15,d.icvLeft=f>>16&127,d.icvRight=f>>23&127,d.grbitDiag=f>>30&3,d.icvTop=127&g,d.icvBottom=g>>7&127,d.icvDiag=g>>14&127,d.dgDiag=g>>21&15,d.icvFore=127&h,d.icvBack=h>>7&127,d.fsxButton=h>>14&1),i.data=d,i}},225:{f:function(a,b){return 0===b||a.read_shift(2),1200}},226:{f:dC},227:{},229:{f:function(a,b){for(var c=[],d=a.read_shift(2);d--;)c.push(dV(a,b));return c}},233:{},235:{},236:{},237:{},239:{},240:{},241:{},242:{},244:{},245:{},246:{},247:{},248:{},249:{},251:{},252:{f:function(a,b){for(var c=a.l+b,d=a.read_shift(4),e=a.read_shift(4),f=[],g=0;g!=e&&a.l<c;++g)f.push(function(a){var b=m;m=1200;var c,d=a.read_shift(2),e=a.read_shift(1),f=4&e,g=8&e,h=0,i={};g&&(h=a.read_shift(2)),f&&(c=a.read_shift(4));var j=0===d?"":a.read_shift(d,2==1+(1&e)?"dbcs-cont":"sbcs-cont");return g&&(a.l+=4*h),f&&(a.l+=c),i.t=j,g||(i.raw="<t>"+i.t+"</t>",i.r=i.t),m=b,i}(a));return f.Count=d,f.Unique=e,f}},253:{f:function(a){var b=dS(a);return b.isst=a.read_shift(4),b}},255:{f:function(a,b){var c={};return c.dsst=a.read_shift(2),a.l+=b-2,c}},256:{},259:{},290:{},311:{},312:{},315:{},317:{f:dH},318:{},319:{},320:{},330:{},331:{},333:{},334:{},335:{},336:{},337:{},338:{},339:{},340:{},351:{},352:{f:dD},353:{f:dC},401:{},402:{},403:{},404:{},405:{},406:{},407:{},408:{},425:{},426:{},427:{},428:{},429:{},430:{f:function(a,b,c){var d=a.l+b,e=a.read_shift(2),f=a.read_shift(2);if(c.sbcch=f,1025==f||14849==f)return[f,e];if(f<1||f>255)throw Error("Unexpected SupBook type: "+f);for(var g=dK(a,f),h=[];d>a.l;)h.push(dL(a));return[f,e,g,h]}},431:{f:dD},432:{},433:{},434:{},437:{},438:{f:function(a,b,c){var d=a.l,e="";try{a.l+=4;var f=(c.lastobj||{cmo:[0,0]}).cmo[1];-1==[0,5,7,11,12,14].indexOf(f)?a.l+=6:(a.read_shift(1),a.l++,a.read_shift(2),a.l+=2);var g=a.read_shift(2);a.read_shift(2),dF(a,2);var h=a.read_shift(2);a.l+=h;for(var i=1;i<a.lens.length-1;++i){if(a.l-d!=a.lens[i])throw Error("TxO: bad continue record");var j=a[a.l],k=dK(a,a.lens[i+1]-a.lens[i]-1);if((e+=k).length>=(j?g:2*g))break}if(e.length!==g&&e.length!==2*g)throw Error("cchText: "+g+" != "+e.length);return a.l=d+b,{t:e}}catch(c){return a.l=d+b,{t:e}}}},439:{f:dD},440:{f:function(a,b){var c=dV(a,8);return a.l+=16,[c,function(a,b){var c=a.l+b,d=a.read_shift(4);if(2!==d)throw Error("Unrecognized streamVersion: "+d);var e=a.read_shift(2);a.l+=2;var f,g,h,i,j,k,l="";16&e&&(f=dO(a,c-a.l)),128&e&&(g=dO(a,c-a.l)),(257&e)==257&&(h=dO(a,c-a.l)),(257&e)==1&&(i=function(a,b){var c,d,e,f,g=a.read_shift(16);switch(b-=16,g){case"e0c9ea79f9bace118c8200aa004ba90b":return c=a.read_shift(4),d=a.l,e=!1,c>24&&(a.l+=c-24,"795881f43b1d7f48af2c825dc4852763"===a.read_shift(16)&&(e=!0),a.l=d),f=a.read_shift((e?c-24:c)>>1,"utf16le").replace(K,""),e&&(a.l+=24),f;case"0303000000000000c000000000000046":for(var h=a.read_shift(2),i="";h-- >0;)i+="../";var j=a.read_shift(0,"lpstr-ansi");if(a.l+=2,57005!=a.read_shift(2))throw Error("Bad FileMoniker");if(0===a.read_shift(4))return i+j.replace(/\\/g,"/");var k=a.read_shift(4);if(3!=a.read_shift(2))throw Error("Bad FileMoniker");return i+a.read_shift(k>>1,"utf16le").replace(K,"");default:throw Error("Unsupported Moniker "+g)}}(a,c-a.l)),8&e&&(l=dO(a,c-a.l)),32&e&&(j=a.read_shift(16)),64&e&&(k=dn(a)),a.l=c;var m=g||h||i||"";m&&l&&(m+="#"+l),m||(m="#"+l),2&e&&"/"==m.charAt(0)&&"/"!=m.charAt(1)&&(m="file://"+m);var n={Target:m};return j&&(n.guid=j),k&&(n.time=k),f&&(n.Tooltip=f),n}(a,b-24)]}},441:{},442:{f:dL},443:{},444:{f:dF},445:{},446:{},448:{f:dC},449:{f:function(a){return a.read_shift(2),a.read_shift(4)},r:2},450:{f:dC},512:{f:d2},513:{f:dS},515:{f:function(a,b,c){c.biffguess&&2==c.biff&&(c.biff=5);var d=dS(a,6);return d.val=cQ(a,8),d}},516:{f:function(a,b,c){c.biffguess&&2==c.biff&&(c.biff=5);var d=a.l+b,e=dS(a,6);return 2==c.biff&&a.l++,e.val=dL(a,d-a.l,c),e}},517:{f:d4},519:{f:dL},520:{f:function(a){var b={};b.r=a.read_shift(2),b.c=a.read_shift(2),b.cnt=a.read_shift(2)-b.c;var c=a.read_shift(2);a.l+=4;var d=a.read_shift(1);return a.l+=3,7&d&&(b.level=7&d),32&d&&(b.hidden=!0),64&d&&(b.hpt=c/20),b}},523:{},545:{f:ea},549:{f:d1},566:{},574:{f:function(a,b,c){return c&&c.biff>=2&&c.biff<5?{}:{RTL:64&a.read_shift(2)}}},638:{f:function(a){var b=a.read_shift(2),c=a.read_shift(2),d=dU(a);return{r:b,c:c,ixfe:d[0],rknum:d[1]}}},659:{},1048:{},1054:{f:function(a,b,c){return[a.read_shift(2),dM(a,0,c)]}},1084:{},1212:{f:function(a,b,c){var d=dX(a,6);a.l++;var e=a.read_shift(1);return[function(a,b,c){var d,e,f=a.l+b,g=a.read_shift(2),h=fy(a,g,c);return 65535==g?[[],(d=b-2,void(a.l+=d))]:(b!==g+2&&(e=fx(a,f-g-2,h,c)),[h,e])}(a,b-=8,c),e,d]}},2048:{f:function(a,b){a.read_shift(2);var c=dV(a,8),d=a.read_shift((b-10)/2,"dbcs-cont");return[c,d=d.replace(K,"")]}},2049:{},2050:{},2051:{},2052:{},2053:{},2054:{},2055:{},2056:{},2057:{f:d_},2058:{},2059:{},2060:{},2061:{},2062:{},2063:{},2064:{},2066:{},2067:{},2128:{},2129:{},2130:{},2131:{},2132:{},2133:{},2134:{},2135:{},2136:{},2137:{},2138:{},2146:{},2147:{r:12},2148:{},2149:{},2150:{},2151:{f:dC},2152:{},2154:{},2155:{},2156:{},2161:{},2162:{},2164:{},2165:{},2166:{},2167:{},2168:{},2169:{},2170:{},2171:{},2172:{f:function(a){a.l+=2;var b={cxfs:0,crc:0};return b.cxfs=a.read_shift(2),b.crc=a.read_shift(4),b},r:12},2173:{f:function(a,b){var c=a.l+b;a.l+=2;var d=a.read_shift(2);a.l+=2;for(var e=a.read_shift(2),f=[];e-- >0;)f.push(function(a){var b=a.read_shift(2),c=a.read_shift(2)-4,d=[b];switch(b){case 4:case 5:case 7:case 8:case 9:case 10:case 11:case 13:d[1]=function(a){var b={};switch(b.xclrType=a.read_shift(2),b.nTintShade=a.read_shift(2),b.xclrType){case 0:case 4:a.l+=4;break;case 1:b.xclrValue=function(a,b){a.l+=b}(a,4);break;case 2:b.xclrValue=dQ(a,4);break;case 3:b.xclrValue=a.read_shift(4)}return a.l+=8,b}(a,c);break;case 6:d[1]=void(a.l+=c);break;case 14:case 15:d[1]=a.read_shift(1===c?1:2);break;default:throw Error("Unrecognized ExtProp type: "+b+" "+c)}return d}(a,c-a.l));return{ixfe:d,ext:f}},r:12},2174:{},2175:{},2180:{},2181:{},2182:{},2183:{},2184:{},2185:{},2186:{},2187:{},2188:{f:dD,r:12},2189:{},2190:{r:12},2191:{},2192:{},2194:{},2195:{},2196:{f:function(a,b,c){if(c.biff<8){a.l+=b;return}var d=a.read_shift(2),e=a.read_shift(2);return[dK(a,d,c),dK(a,e,c)]},r:12},2197:{},2198:{f:function(a,b,c){var d,e=a.l+b;if(124226!==a.read_shift(4)){if(!c.cellStyles){a.l=e;return}var f=a.slice(a.l);a.l=e;try{d=a4(f,{type:"array"})}catch(a){return}var g=a0(d,"theme/theme/theme1.xml",!0);if(g)return e5(g,c)}},r:12},2199:{},2200:{},2201:{},2202:{f:function(a){return[0!==a.read_shift(4),0!==a.read_shift(4),a.read_shift(4)]},r:12},2203:{f:dC},2204:{},2205:{},2206:{},2207:{},2211:{f:function(a){var b,c,d=(b=a.read_shift(2),c=a.read_shift(2),a.l+=8,{type:b,flags:c});if(2211!=d.type)throw Error("Invalid Future Record "+d.type);return 0!==a.read_shift(4)}},2212:{},2213:{},2214:{},2215:{},4097:{},4098:{},4099:{},4102:{},4103:{},4105:{},4106:{},4107:{},4108:{},4109:{},4116:{},4117:{},4118:{},4119:{},4120:{},4121:{},4122:{},4123:{},4124:{},4125:{},4126:{},4127:{},4128:{},4129:{},4130:{},4132:{},4133:{},4134:{f:dF},4135:{},4146:{},4147:{},4148:{},4149:{},4154:{},4156:{},4157:{},4158:{},4159:{},4160:{},4161:{},4163:{},4164:{f:function(a,b,c){var d={area:!1};if(5!=c.biff)return a.l+=b,d;var e=a.read_shift(1);return a.l+=3,16&e&&(d.area=!0),d}},4165:{},4166:{},4168:{},4170:{},4171:{},4174:{},4175:{},4176:{},4177:{},4187:{},4188:{f:function(a){for(var b=a.read_shift(2),c=[];b-- >0;)c.push(dR(a,8));return c}},4189:{},4191:{},4192:{},4193:{},4194:{},4195:{},4196:{},4197:{},4198:{},4199:{},4200:{},0:{f:d2},1:{},2:{f:function(a){var b=dS(a,6);++a.l;var c=a.read_shift(2);return b.t="n",b.val=c,b}},3:{f:function(a){var b=dS(a,6);++a.l;var c=cQ(a,8);return b.t="n",b.val=c,b}},4:{f:function(a,b,c){c.biffguess&&5==c.biff&&(c.biff=2);var d=dS(a,6);++a.l;var e=dM(a,b-7,c);return d.t="str",d.val=e,d}},5:{f:d4},7:{f:function(a){var b=a.read_shift(1);return 0===b?(a.l++,""):a.read_shift(b,"sbcs-cont")}},8:{},9:{f:d_},11:{},22:{f:dF},30:{f:dM},31:{},32:{},33:{f:ea},36:{},37:{f:d1},50:{f:function(a,b){a.l+=6,a.l+=2,a.l+=1,a.l+=3,a.l+=1,a.l+=b-13}},62:{},52:{},67:{},68:{f:dF},69:{},86:{},126:{},127:{f:function(a){var b=a.read_shift(2),c=a.read_shift(2),d=a.read_shift(4),e={fmt:b,env:c,len:d,data:a.slice(a.l,a.l+d)};return a.l+=d,e}},135:{},136:{},137:{},145:{},148:{},149:{},150:{},169:{},171:{},188:{},191:{},192:{},194:{},195:{},214:{f:function(a,b,c){var d=a.l+b,e=dS(a,6),f=a.read_shift(2),g=dK(a,f,c);return a.l=d,e.t="str",e.val=g,e}},223:{},234:{},354:{},421:{},518:{f:fD},521:{f:d_},536:{f:d8},547:{f:d6},561:{},579:{},1030:{f:fD},1033:{f:d_},1091:{},2157:{},2163:{},2177:{},2240:{},2241:{},2242:{},2243:{},2244:{},2245:{},2246:{},2247:{},2248:{},2249:{},2250:{},2251:{},2262:{r:12},29282:{}};function gx(a,b,c,d){if(!isNaN(b)){var e=d||(c||[]).length||0,f=a.next(4);f.write_shift(2,b),f.write_shift(2,e),e>0&&b3(c)&&a.push(c)}}function gy(a,b,c){return a||(a=ch(7)),a.write_shift(2,b),a.write_shift(2,c),a.write_shift(2,0),a.write_shift(1,0),a}function gz(a,b){for(var c=0;c<=a.SheetNames.length;++c){var d=a.Sheets[a.SheetNames[c]];d&&d["!ref"]&&cv(d["!ref"]).e.c>255&&"undefined"!=typeof console&&console.error&&console.error("Worksheet '"+a.SheetNames[c]+"' extends beyond column IV (255).  Data may be lost.")}var e=b||{};switch(e.biff||2){case 8:case 5:var f=b||{},g=[];a&&!a.SSF&&(a.SSF=aR(U)),a&&a.SSF&&(aw(),av(a.SSF),f.revssf=aF(a.SSF),f.revssf[a.SSF[65535]]=0,f.ssf=a.SSF),f.Strings=[],f.Strings.Count=0,f.Strings.Unique=0,g_(f),f.cellXfs=[],fQ(f.cellXfs,{},{revssf:{General:0}}),a.Props||(a.Props={});for(var h=0;h<a.SheetNames.length;++h)g[g.length]=function(a,b,c){var d,e,f,g,h,i,j,k,l,m=cj(),n=c.SheetNames[a],o=c.Sheets[n]||{},p=(c||{}).Workbook||{},q=(p.Sheets||[])[a]||{},r=Array.isArray(o),s=8==b.biff,t="",u=[],v=cx(o["!ref"]||"A1"),w=s?65536:16384;if(v.e.c>255||v.e.r>=w){if(b.WTF)throw Error("Range "+(o["!ref"]||"A1")+" exceeds format limit A1:IV16384");v.e.c=Math.min(v.e.c,255),v.e.r=Math.min(v.e.c,w-1)}gx(m,2057,d0(c,16,b)),gx(m,13,dG(1)),gx(m,12,dG(100)),gx(m,15,dE(!0)),gx(m,17,dE(!1)),gx(m,16,cR(.001)),gx(m,95,dE(!0)),gx(m,42,dE(!1)),gx(m,43,dE(!1)),gx(m,130,dG(1)),gx(m,128,(d=[0,0],(e=ch(8)).write_shift(4,0),e.write_shift(2,d[0]?d[0]+1:0),e.write_shift(2,d[1]?d[1]+1:0),e)),gx(m,131,dE(!1)),gx(m,132,dE(!1)),s&&function(a,b){if(b){var c=0;b.forEach(function(b,d){var e,f,g;++c<=256&&b&&gx(a,125,(e=fO(d,b),(f=ch(12)).write_shift(2,d),f.write_shift(2,d),f.write_shift(2,256*e.width),f.write_shift(2,0),g=0,e.hidden&&(g|=1),f.write_shift(1,g),g=e.level||0,f.write_shift(1,g),f.write_shift(2,0),f))})}}(m,o["!cols"]),gx(m,512,((g=ch(2*(f=8!=b.biff&&b.biff?2:4)+6)).write_shift(f,v.s.r),g.write_shift(f,v.e.r+1),g.write_shift(2,v.s.c),g.write_shift(2,v.e.c+1),g.write_shift(2,0),g)),s&&(o["!links"]=[]);for(var x=v.s.r;x<=v.e.r;++x){t=cq(x);for(var y=v.s.c;y<=v.e.c;++y){x===v.s.r&&(u[y]=cs(y)),l=u[y]+t;var z=r?(o[x]||[])[y]:o[l];z&&(!function(a,b,c,d,e){var f=16+fQ(e.cellXfs,b,e);if(null==b.v&&!b.bf)return gx(a,513,dT(c,d,f));if(b.bf)gx(a,6,function(a,b,c,d,e){var f=dT(b,c,e),g=function(a){if(null==a){var b=ch(8);return b.write_shift(1,3),b.write_shift(1,0),b.write_shift(2,0),b.write_shift(2,0),b.write_shift(2,65535),b}return"number"==typeof a?cR(a):cR(0)}(a.v),h=ch(6);h.write_shift(2,33),h.write_shift(4,0);for(var i=ch(a.bf.length),j=0;j<a.bf.length;++j)i[j]=a.bf[j];return J([f,g,h,i])}(b,c,d,0,f));else switch(b.t){case"d":case"n":var g,h="d"==b.t?aH(aP(b.v)):b.v;gx(a,515,(dT(c,d,f,g=ch(14)),cR(h,g),g));break;case"b":case"e":gx(a,517,(i=b.v,j=b.t,dT(c,d,f,k=ch(8)),dI(i,j,k),k));break;case"s":case"str":if(e.bookSST){var i,j,k,l,m,n,o,p=fN(e.Strings,b.v,e.revStrings);gx(a,253,(dT(c,d,f,o=ch(10)),o.write_shift(4,p),o))}else gx(a,516,(l=(b.v||"").slice(0,255),dT(c,d,f,n=ch(8+ +(m=!e||8==e.biff)+(1+m)*l.length)),n.write_shift(2,l.length),m&&n.write_shift(1,1),n.write_shift((1+m)*l.length,l,m?"utf16le":"sbcs"),n));break;default:gx(a,513,dT(c,d,f))}}(m,z,x,y,b),s&&z.l&&o["!links"].push([l,z.l]))}}var A=q.CodeName||q.name||n;return s&&gx(m,574,(h=(p.Views||[])[0],i=ch(18),j=1718,h&&h.RTL&&(j|=64),i.write_shift(2,j),i.write_shift(4,0),i.write_shift(4,64),i.write_shift(4,0),i.write_shift(4,0),i)),s&&(o["!merges"]||[]).length&&gx(m,229,function(a){var b=ch(2+8*a.length);b.write_shift(2,a.length);for(var c=0;c<a.length;++c)dW(a[c],b);return b}(o["!merges"])),s&&function(a,b){for(var c=0;c<b["!links"].length;++c){var d=b["!links"][c];gx(a,440,function(a){var b=ch(24),c=ct(a[0]);b.write_shift(2,c.r),b.write_shift(2,c.r),b.write_shift(2,c.c),b.write_shift(2,c.c);for(var d="d0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),e=0;e<16;++e)b.write_shift(1,parseInt(d[e],16));return J([b,function(a){var b=ch(512),c=0,d=a.Target;"file://"==d.slice(0,7)&&(d=d.slice(7));var e=d.indexOf("#"),f=e>-1?31:23;switch(d.charAt(0)){case"#":f=28;break;case".":f&=-3}b.write_shift(4,2),b.write_shift(4,f);var g=[8,6815827,6619237,4849780,83];for(c=0;c<g.length;++c)b.write_shift(4,g[c]);if(28==f)dP(d=d.slice(1),b);else if(2&f){for(c=0,g="e0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" ");c<g.length;++c)b.write_shift(1,parseInt(g[c],16));var h=e>-1?d.slice(0,e):d;for(b.write_shift(4,2*(h.length+1)),c=0;c<h.length;++c)b.write_shift(2,h.charCodeAt(c));b.write_shift(2,0),8&f&&dP(e>-1?d.slice(e+1):"",b)}else{for(c=0,g="03 03 00 00 00 00 00 00 c0 00 00 00 00 00 00 46".split(" ");c<g.length;++c)b.write_shift(1,parseInt(g[c],16));for(var i=0;"../"==d.slice(3*i,3*i+3)||"..\\"==d.slice(3*i,3*i+3);)++i;for(b.write_shift(2,i),b.write_shift(4,d.length-3*i+1),c=0;c<d.length-3*i;++c)b.write_shift(1,255&d.charCodeAt(c+3*i));for(b.write_shift(1,0),b.write_shift(2,65535),b.write_shift(2,57005),c=0;c<6;++c)b.write_shift(4,0)}return b.slice(0,b.l)}(a[1])])}(d)),d[1].Tooltip&&gx(a,2048,function(a){var b=a[1].Tooltip,c=ch(10+2*(b.length+1));c.write_shift(2,2048);var d=ct(a[0]);c.write_shift(2,d.r),c.write_shift(2,d.r),c.write_shift(2,d.c),c.write_shift(2,d.c);for(var e=0;e<b.length;++e)c.write_shift(2,b.charCodeAt(e));return c.write_shift(2,0),c}(d))}delete b["!links"]}(m,o),gx(m,442,dN(A,b)),s&&((k=ch(19)).write_shift(4,2151),k.write_shift(4,0),k.write_shift(4,0),k.write_shift(2,3),k.write_shift(1,1),k.write_shift(4,0),gx(m,2151,k),(k=ch(39)).write_shift(4,2152),k.write_shift(4,0),k.write_shift(4,0),k.write_shift(2,3),k.write_shift(1,0),k.write_shift(4,0),k.write_shift(2,1),k.write_shift(4,4),k.write_shift(2,0),dW(cx(o["!ref"]||"A1"),k),k.write_shift(4,4),gx(m,2152,k)),gx(m,10),m.end()}(h,f,a);return g.unshift(function(a,b,c){var d,e,f,g,h,i,j,k=cj(),l=(a||{}).Workbook||{},m=l.Sheets||[],n=l.WBProps||{},o=8==c.biff,p=5==c.biff;gx(k,2057,d0(a,5,c)),"xla"==c.bookType&&gx(k,135),gx(k,225,o?dG(1200):null),gx(k,193,function(a,b){b||(b=ch(2));for(var c=0;c<2;++c)b.write_shift(1,0);return b}(2)),p&&gx(k,191),p&&gx(k,192),gx(k,226),gx(k,92,function(a,b){var c=!b||8==b.biff,d=ch(c?112:54);for(d.write_shift(8==b.biff?2:1,7),c&&d.write_shift(1,0),d.write_shift(4,0x33336853),d.write_shift(4,5458548|0x20000000*!c);d.l<d.length;)d.write_shift(1,32*!c);return d}(0,c)),gx(k,66,dG(o?1200:1252)),o&&gx(k,353,dG(0)),o&&gx(k,448),gx(k,317,function(a){for(var b=ch(2*a),c=0;c<a;++c)b.write_shift(2,c+1);return b}(a.SheetNames.length)),o&&a.vbaraw&&gx(k,211),o&&a.vbaraw&&gx(k,442,dN(n.CodeName||"ThisWorkbook",c)),gx(k,156,dG(17)),gx(k,25,dE(!1)),gx(k,18,dE(!1)),gx(k,19,dG(0)),o&&gx(k,431,dE(!1)),o&&gx(k,444,dG(0)),gx(k,61,((d=ch(18)).write_shift(2,0),d.write_shift(2,0),d.write_shift(2,29280),d.write_shift(2,17600),d.write_shift(2,56),d.write_shift(2,0),d.write_shift(2,0),d.write_shift(2,1),d.write_shift(2,500),d)),gx(k,64,dE(!1)),gx(k,141,dG(0)),gx(k,34,dE("true"==(a.Workbook&&a.Workbook.WBProps&&bo(a.Workbook.WBProps.date1904)?"true":"false"))),gx(k,14,dE(!0)),o&&gx(k,439,dE(!1)),gx(k,218,dG(0)),gx(k,49,(f=(e={sz:12,color:{theme:1},name:"Arial",family:2,scheme:"minor"}).name||"Arial",(h=ch((g=c&&5==c.biff)?15+f.length:16+2*f.length)).write_shift(2,20*(e.sz||12)),h.write_shift(4,0),h.write_shift(2,400),h.write_shift(4,0),h.write_shift(2,0),h.write_shift(1,f.length),g||h.write_shift(1,1),h.write_shift((g?1:2)*f.length,f,g?"sbcs":"utf16le"),h)),i=a.SSF,i&&[[5,8],[23,26],[41,44],[50,392]].forEach(function(a){for(var b=a[0];b<=a[1];++b)null!=i[b]&&gx(k,1054,function(a,b,c,d){var e=c&&5==c.biff;d||(d=ch(e?3+b.length:5+2*b.length)),d.write_shift(2,a),d.write_shift(e?1:2,b.length),e||d.write_shift(1,1),d.write_shift((e?1:2)*b.length,b,e?"sbcs":"utf16le");var f=d.length>d.l?d.slice(0,d.l):d;return null==f.l&&(f.l=f.length),f}(b,i[b],c))});for(var q=0;q<16;++q)gx(k,224,d3({numFmtId:0,style:!0},0,c));c.cellXfs.forEach(function(a){gx(k,224,d3(a,0,c))}),o&&gx(k,352,dE(!1));var r=k.end(),s=cj();o&&gx(s,140,(j||(j=ch(4)),j.write_shift(2,1),j.write_shift(2,1),j)),o&&c.Strings&&function(a,b,c,d){var e=(c||[]).length||0;if(e<=8224)return gx(a,252,c,e);if(!isNaN(252)){for(var f=c.parts||[],g=0,h=0,i=0;i+(f[g]||8224)<=8224;)i+=f[g]||8224,g++;var j=a.next(4);for(j.write_shift(2,b),j.write_shift(2,i),a.push(c.slice(h,h+i)),h+=i;h<e;){for((j=a.next(4)).write_shift(2,60),i=0;i+(f[g]||8224)<=8224;)i+=f[g]||8224,g++;j.write_shift(2,i),a.push(c.slice(h,h+i)),h+=i}}}(s,252,function(a,b){var c=ch(8);c.write_shift(4,a.Count),c.write_shift(4,a.Unique);for(var d=[],e=0;e<a.length;++e)d[e]=function(a){var b=a.t||"",c=ch(3);c.write_shift(2,b.length),c.write_shift(1,1);var d=ch(2*b.length);return d.write_shift(2*b.length,b,"utf16le"),J([c,d])}(a[e],b);var f=J([c].concat(d));return f.parts=[c.length].concat(d.map(function(a){return a.length})),f}(c.Strings,c)),gx(s,10);var t=s.end(),u=cj(),v=0,w=0;for(w=0;w<a.SheetNames.length;++w)v+=(o?12:11)+(o?2:1)*a.SheetNames[w].length;var x=r.length+v+t.length;for(w=0;w<a.SheetNames.length;++w)gx(u,133,function(a,b){var c=!b||b.biff>=8?2:1,d=ch(8+c*a.name.length);d.write_shift(4,a.pos),d.write_shift(1,a.hs||0),d.write_shift(1,a.dt),d.write_shift(1,a.name.length),b.biff>=8&&d.write_shift(1,1),d.write_shift(c*a.name.length,a.name,b.biff<8?"sbcs":"utf16le");var e=d.slice(0,d.l);return e.l=d.l,e}({pos:x,hs:(m[w]||{}).Hidden||0,dt:0,name:a.SheetNames[w]},c)),x+=b[w].length;var y=u.end();if(v!=y.length)throw Error("BS8 "+v+" != "+y.length);var z=[];return r.length&&z.push(r),y.length&&z.push(y),t.length&&z.push(t),J(z)}(a,g,f)),J(g);case 4:case 3:case 2:for(var i=b||{},j=cj(),k=0,l=0;l<a.SheetNames.length;++l)a.SheetNames[l]==i.sheet&&(k=l);if(0==k&&i.sheet&&a.SheetNames[0]!=i.sheet)throw Error("Sheet not found: "+i.sheet);return gx(j,4==i.biff?1033:3==i.biff?521:9,d0(a,16,i)),!function(a,b,c,d){var e,f=Array.isArray(b),g=cx(b["!ref"]||"A1"),h="",i=[];if(g.e.c>255||g.e.r>16383){if(d.WTF)throw Error("Range "+(b["!ref"]||"A1")+" exceeds format limit A1:IV16384");g.e.c=Math.min(g.e.c,255),g.e.r=Math.min(g.e.c,16383),e=cw(g)}for(var j=g.s.r;j<=g.e.r;++j){h=cq(j);for(var k=g.s.c;k<=g.e.c;++k){j===g.s.r&&(i[k]=cs(k)),e=i[k]+h;var l=f?(b[j]||[])[k]:b[e];l&&function(a,b,c,d){if(null!=b.v)switch(b.t){case"d":case"n":var e,f,g,h,i,j,k,l="d"==b.t?aH(aP(b.v)):b.v;l==(0|l)&&l>=0&&l<65536?gx(a,2,(gy(j=ch(9),c,d),j.write_shift(2,l),j)):gx(a,3,(gy(k=ch(15),c,d),k.write_shift(8,l,"f"),k));return;case"b":case"e":gx(a,5,(e=b.v,f=b.t,gy(g=ch(9),c,d),dI(e,f||"b",g),g));return;case"s":case"str":gx(a,4,(gy(i=ch(8+2*(h=(b.v||"").slice(0,255)).length),c,d),i.write_shift(1,h.length),i.write_shift(h.length,h,"sbcs"),i.l<i.length?i.slice(0,i.l):i));return}gx(a,1,gy(null,c,d))}(a,l,j,k,d)}}}(j,a.Sheets[a.SheetNames[k]],0,i,a),gx(j,10),j.end()}throw Error("invalid type "+e.bookType+" for BIFF")}function gA(a,b){var c=b||{},d=c.dense?[]:{},e=(a=a.replace(/<!--.*?-->/g,"")).match(/<table/i);if(!e)throw Error("Invalid HTML: could not find <table>");var f=a.match(/<\/table/i),g=e.index,h=f&&f.index||a.length,i=aW(a.slice(g,h),/(:?<tr[^>]*>)/i,"<tr>"),j=-1,k=0,l=0,m=0,n={s:{r:1e7,c:1e7},e:{r:0,c:0}},o=[];for(g=0;g<i.length;++g){var p=i[g].trim(),q=p.slice(0,3).toLowerCase();if("<tr"==q){if(++j,c.sheetRows&&c.sheetRows<=j){--j;break}k=0;continue}if("<td"==q||"<th"==q){var r=p.split(/<\/t[dh]>/i);for(h=0;h<r.length;++h){var s=r[h].trim();if(s.match(/<t[dh]/i)){for(var t=s,u=0;"<"==t.charAt(0)&&(u=t.indexOf(">"))>-1;)t=t.slice(u+1);for(var v=0;v<o.length;++v){var w=o[v];w.s.c==k&&w.s.r<j&&j<=w.e.r&&(k=w.e.c+1,v=-1)}var x=bc(s.slice(0,s.indexOf(">")));m=x.colspan?+x.colspan:1,((l=+x.rowspan)>1||m>1)&&o.push({s:{r:j,c:k},e:{r:j+(l||1)-1,c:k+m-1}});var y=x.t||x["data-t"]||"";if(!t.length||(t=bw(t),n.s.r>j&&(n.s.r=j),n.e.r<j&&(n.e.r=j),n.s.c>k&&(n.s.c=k),n.e.c<k&&(n.e.c=k),!t.length)){k+=m;continue}var z={t:"s",v:t};c.raw||!t.trim().length||"s"==y||("TRUE"===t?z={t:"b",v:!0}:"FALSE"===t?z={t:"b",v:!1}:isNaN(aT(t))?isNaN(aV(t).getDate())||(z={t:"d",v:aP(t)},c.cellDates||(z={t:"n",v:aH(z.v)}),z.z=c.dateNF||U[14]):z={t:"n",v:aT(t)}),c.dense?(d[j]||(d[j]=[]),d[j][k]=z):d[cu({r:j,c:k})]=z,k+=m}}}}return d["!ref"]=cw(n),o.length&&(d["!merges"]=o),d}var gB={day:["d","dd"],month:["m","mm"],year:["y","yy"],hours:["h","hh"],minutes:["m","mm"],seconds:["s","ss"],"am-pm":["A/P","AM/PM"],"day-of-week":["ddd","dddd"],era:["e","ee"],quarter:["\\Qm",'m\\"th quarter"']};function gC(a,b){var c=b||{},d,e,f,g,h,i,j=bG(a),k=[],l={name:""},m="",n=0,o={},p=[],q=c.dense?[]:{},r={value:""},s="",t=0,u=[],v=-1,w=-1,x={s:{r:1e6,c:1e7},e:{r:0,c:0}},y=0,z={},A=[],B={},C=0,D=[],E=1,F=1,G=[],H={Names:[]},I={},J=["",""],K=[],L={},M="",N=0,O=!1,P=!1,Q=0;for(bH.lastIndex=0,j=j.replace(/<!--([\s\S]*?)-->/mg,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"");h=bH.exec(j);)switch(h[3]=h[3].replace(/_.*$/,"")){case"table":case"工作表":"/"===h[1]?(x.e.c>=x.s.c&&x.e.r>=x.s.r?q["!ref"]=cw(x):q["!ref"]="A1:A1",c.sheetRows>0&&c.sheetRows<=x.e.r&&(q["!fullref"]=q["!ref"],x.e.r=c.sheetRows-1,q["!ref"]=cw(x)),A.length&&(q["!merges"]=A),D.length&&(q["!rows"]=D),f.name=f["名称"]||f.name,"undefined"!=typeof JSON&&JSON.stringify(f),p.push(f.name),o[f.name]=q,P=!1):"/"!==h[0].charAt(h[0].length-2)&&(f=bc(h[0],!1),v=w=-1,x.s.r=x.s.c=1e7,x.e.r=x.e.c=0,q=c.dense?[]:{},A=[],D=[],P=!0);break;case"table-row-group":"/"===h[1]?--y:++y;break;case"table-row":case"行":if("/"===h[1]){v+=E,E=1;break}if((g=bc(h[0],!1))["行号"]?v=g["行号"]-1:-1==v&&(v=0),(E=+g["number-rows-repeated"]||1)<10)for(Q=0;Q<E;++Q)y>0&&(D[v+Q]={level:y});w=-1;break;case"covered-table-cell":"/"!==h[1]&&++w,c.sheetStubs&&(c.dense?(q[v]||(q[v]=[]),q[v][w]={t:"z"}):q[cu({r:v,c:w})]={t:"z"}),s="",u=[];break;case"table-cell":case"数据":if("/"===h[0].charAt(h[0].length-2))++w,F=parseInt((r=bc(h[0],!1))["number-columns-repeated"]||"1",10),i={t:"z",v:null},r.formula&&!1!=c.cellFormula&&(i.f=fI(bg(r.formula))),"string"==(r["数据类型"]||r["value-type"])&&(i.t="s",i.v=bg(r["string-value"]||""),c.dense?(q[v]||(q[v]=[]),q[v][w]=i):q[cu({r:v,c:w})]=i),w+=F-1;else if("/"!==h[1]){s="",t=0,u=[],F=1;var R=E?v+E-1:v;if(++w>x.e.c&&(x.e.c=w),w<x.s.c&&(x.s.c=w),v<x.s.r&&(x.s.r=v),R>x.e.r&&(x.e.r=R),r=bc(h[0],!1),K=[],L={},i={t:r["数据类型"]||r["value-type"],v:null},c.cellFormula)if(r.formula&&(r.formula=bg(r.formula)),r["number-matrix-columns-spanned"]&&r["number-matrix-rows-spanned"]&&(B={s:{r:v,c:w},e:{r:v+(C=parseInt(r["number-matrix-rows-spanned"],10)||0)-1,c:w+(parseInt(r["number-matrix-columns-spanned"],10)||0)-1}},i.F=cw(B),G.push([B,i.F])),r.formula)i.f=fI(r.formula);else for(Q=0;Q<G.length;++Q)v>=G[Q][0].s.r&&v<=G[Q][0].e.r&&w>=G[Q][0].s.c&&w<=G[Q][0].e.c&&(i.F=G[Q][1]);switch((r["number-columns-spanned"]||r["number-rows-spanned"])&&(B={s:{r:v,c:w},e:{r:v+(C=parseInt(r["number-rows-spanned"],10)||0)-1,c:w+(parseInt(r["number-columns-spanned"],10)||0)-1}},A.push(B)),r["number-columns-repeated"]&&(F=parseInt(r["number-columns-repeated"],10)),i.t){case"boolean":i.t="b",i.v=bo(r["boolean-value"]);break;case"float":case"percentage":case"currency":i.t="n",i.v=parseFloat(r.value);break;case"date":i.t="d",i.v=aP(r["date-value"]),c.cellDates||(i.t="n",i.v=aH(i.v)),i.z="m/d/yy";break;case"time":i.t="n",i.v=function(a){var b=0,c=0,d=!1,e=a.match(/P([0-9\.]+Y)?([0-9\.]+M)?([0-9\.]+D)?T([0-9\.]+H)?([0-9\.]+M)?([0-9\.]+S)?/);if(!e)throw Error("|"+a+"| is not an ISO8601 Duration");for(var f=1;f!=e.length;++f)if(e[f]){switch(c=1,f>3&&(d=!0),e[f].slice(e[f].length-1)){case"Y":throw Error("Unsupported ISO Duration Field: "+e[f].slice(e[f].length-1));case"D":c*=24;case"H":c*=60;case"M":if(d)c*=60;else throw Error("Unsupported ISO Duration Field: M")}b+=c*parseInt(e[f],10)}return b}(r["time-value"])/86400,c.cellDates&&(i.t="d",i.v=aL(i.v)),i.z="HH:MM:SS";break;case"number":i.t="n",i.v=parseFloat(r["数据数值"]);break;default:if("string"!==i.t&&"text"!==i.t&&i.t)throw Error("Unsupported value type "+i.t);i.t="s",null!=r["string-value"]&&(s=bg(r["string-value"]),u=[])}}else{if(O=!1,"s"===i.t&&(i.v=s||"",u.length&&(i.R=u),O=0==t),I.Target&&(i.l=I),K.length>0&&(i.c=K,K=[]),s&&!1!==c.cellText&&(i.w=s),O&&(i.t="z",delete i.v),(!O||c.sheetStubs)&&!(c.sheetRows&&c.sheetRows<=v))for(var S=0;S<E;++S){if(F=parseInt(r["number-columns-repeated"]||"1",10),c.dense)for(q[v+S]||(q[v+S]=[]),q[v+S][w]=0==S?i:aR(i);--F>0;)q[v+S][w+F]=aR(i);else for(q[cu({r:v+S,c:w})]=i;--F>0;)q[cu({r:v+S,c:w+F})]=aR(i);x.e.c<=w&&(x.e.c=w)}w+=(F=parseInt(r["number-columns-repeated"]||"1",10))-1,F=0,i={},s="",u=[]}I={};break;case"document":case"document-content":case"电子表格文档":case"spreadsheet":case"主体":case"scripts":case"styles":case"font-face-decls":case"master-styles":if("/"===h[1]){if((d=k.pop())[0]!==h[3])throw"Bad state: "+d}else"/"!==h[0].charAt(h[0].length-2)&&k.push([h[3],!0]);break;case"annotation":if("/"===h[1]){if((d=k.pop())[0]!==h[3])throw"Bad state: "+d;L.t=s,u.length&&(L.R=u),L.a=M,K.push(L)}else"/"!==h[0].charAt(h[0].length-2)&&k.push([h[3],!1]);M="",N=0,s="",t=0,u=[];break;case"creator":"/"===h[1]?M=j.slice(N,h.index):N=h.index+h[0].length;break;case"meta":case"元数据":case"settings":case"config-item-set":case"config-item-map-indexed":case"config-item-map-entry":case"config-item-map-named":case"shapes":case"frame":case"text-box":case"image":case"data-pilot-tables":case"list-style":case"form":case"dde-links":case"event-listeners":case"chart":if("/"===h[1]){if((d=k.pop())[0]!==h[3])throw"Bad state: "+d}else"/"!==h[0].charAt(h[0].length-2)&&k.push([h[3],!1]);s="",t=0,u=[];break;case"scientific-number":case"currency-symbol":case"currency-style":case"script":case"libraries":case"automatic-styles":case"default-style":case"page-layout":case"style":case"map":case"font-face":case"paragraph-properties":case"table-properties":case"table-column-properties":case"table-row-properties":case"table-cell-properties":case"fraction":case"boolean-style":case"boolean":case"text-style":case"text-content":case"text-properties":case"embedded-text":case"body":case"电子表格":case"forms":case"table-column":case"table-header-rows":case"table-rows":case"table-column-group":case"table-header-columns":case"table-columns":case"null-date":case"graphic-properties":case"calculation-settings":case"named-expressions":case"label-range":case"label-ranges":case"named-expression":case"sort":case"sort-by":case"sort-groups":case"tab":case"line-break":case"span":case"s":case"date":case"object":case"title":case"标题":case"desc":case"binary-data":case"table-source":case"scenario":case"iteration":case"content-validations":case"content-validation":case"help-message":case"error-message":case"database-ranges":case"filter":case"filter-and":case"filter-or":case"filter-condition":case"list-level-style-bullet":case"list-level-style-number":case"list-level-properties":case"sender-firstname":case"sender-lastname":case"sender-initials":case"sender-title":case"sender-position":case"sender-email":case"sender-phone-private":case"sender-fax":case"sender-company":case"sender-phone-work":case"sender-street":case"sender-city":case"sender-postal-code":case"sender-country":case"sender-state-or-province":case"author-name":case"author-initials":case"chapter":case"file-name":case"template-name":case"sheet-name":case"event-listener":case"initial-creator":case"creation-date":case"print-date":case"generator":case"document-statistic":case"user-defined":case"editing-duration":case"editing-cycles":case"config-item":case"page-number":case"page-count":case"time":case"cell-range-source":case"detective":case"operation":case"highlighted-range":case"data-pilot-table":case"source-cell-range":case"source-service":case"data-pilot-field":case"data-pilot-level":case"data-pilot-subtotals":case"data-pilot-subtotal":case"data-pilot-members":case"data-pilot-member":case"data-pilot-display-info":case"data-pilot-sort-info":case"data-pilot-layout-info":case"data-pilot-field-reference":case"data-pilot-groups":case"data-pilot-group":case"data-pilot-group-member":case"rect":case"dde-connection-decls":case"dde-connection-decl":case"dde-link":case"dde-source":case"properties":case"property":case"table-protection":case"data-pilot-grand-total":case"office-document-common-attrs":break;case"number-style":case"percentage-style":case"date-style":case"time-style":if("/"===h[1]){if(z[l.name]=m,(d=k.pop())[0]!==h[3])throw"Bad state: "+d}else"/"!==h[0].charAt(h[0].length-2)&&(m="",l=bc(h[0],!1),k.push([h[3],!0]));break;case"number":case"day":case"month":case"year":case"era":case"day-of-week":case"week-of-year":case"quarter":case"hours":case"minutes":case"seconds":case"am-pm":switch(k[k.length-1][0]){case"time-style":case"date-style":e=bc(h[0],!1),m+=gB[h[3]][+("long"===e.style)]}break;case"text":if("/>"===h[0].slice(-2));else if("/"===h[1])switch(k[k.length-1][0]){case"number-style":case"date-style":case"time-style":m+=j.slice(n,h.index)}else n=h.index+h[0].length;break;case"named-range":J=fJ((e=bc(h[0],!1))["cell-range-address"]);var T={Name:e.name,Ref:J[0]+"!"+J[1]};P&&(T.Sheet=p.length),H.Names.push(T);break;case"p":case"文本串":if(["master-styles"].indexOf(k[k.length-1][0])>-1)break;if("/"!==h[1]||r&&r["string-value"])bc(h[0],!1),t=h.index+h[0].length;else{var U=[bg(j.slice(t,h.index).replace(/[\t\r\n]/g," ").trim().replace(/ +/g," ").replace(/<text:s\/>/g," ").replace(/<text:s text:c="(\d+)"\/>/g,function(a,b){return Array(parseInt(b,10)+1).join(" ")}).replace(/<text:tab[^>]*\/>/g,"	").replace(/<text:line-break\/>/g,"\n").replace(/<[^>]*>/g,""))];s=(s.length>0?s+"\n":"")+U[0]}break;case"database-range":if("/"===h[1])break;try{o[(J=fJ(bc(h[0])["target-range-address"]))[0]]["!autofilter"]={ref:J[1]}}catch(a){}break;case"a":if("/"!==h[1]){if(!(I=bc(h[0],!1)).href)break;I.Target=bg(I.href),delete I.href,"#"==I.Target.charAt(0)&&I.Target.indexOf(".")>-1?(J=fJ(I.Target.slice(1)),I.Target="#"+J[0]+"!"+J[1]):I.Target.match(/^\.\.[\\\/]/)&&(I.Target=I.Target.slice(3))}break;default:switch(h[2]){case"dc:":case"calcext:":case"loext:":case"ooo:":case"chartooo:":case"draw:":case"style:":case"chart:":case"form:":case"uof:":case"表:":case"字:":break;default:if(c.WTF)throw Error(h)}}var V={Sheets:o,SheetNames:p,Workbook:H};return c.bookSheets&&delete V.Sheets,V}var gD=function(){var a="<office:document-styles "+bD({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","office:version":"1.2"})+'><office:master-styles><style:master-page style:name="mp1" style:page-layout-name="mp1"><style:header/><style:header-left style:display="false"/><style:footer/><style:footer-left style:display="false"/></style:master-page></office:master-styles></office:document-styles>';return function(){return a6+a}}(),gE=function(){var a="          <table:table-cell />\n",b=function(b,c,d){var e=[];e.push('      <table:table table:name="'+bj(c.SheetNames[d])+'" table:style-name="ta1">\n');var f=0,g=0,h=cv(b["!ref"]||"A1"),i=b["!merges"]||[],j=0,k=Array.isArray(b);if(b["!cols"])for(g=0;g<=h.e.c;++g)e.push("        <table:table-column"+(b["!cols"][g]?' table:style-name="co'+b["!cols"][g].ods+'"':"")+"></table:table-column>\n");var l="",m=b["!rows"]||[];for(f=0;f<h.s.r;++f)l=m[f]?' table:style-name="ro'+m[f].ods+'"':"",e.push("        <table:table-row"+l+"></table:table-row>\n");for(;f<=h.e.r;++f){for(l=m[f]?' table:style-name="ro'+m[f].ods+'"':"",e.push("        <table:table-row"+l+">\n"),g=0;g<h.s.c;++g)e.push(a);for(;g<=h.e.c;++g){var n=!1,o={},p="";for(j=0;j!=i.length;++j)if(!(i[j].s.c>g)&&!(i[j].s.r>f)&&!(i[j].e.c<g)&&!(i[j].e.r<f)){(i[j].s.c!=g||i[j].s.r!=f)&&(n=!0),o["table:number-columns-spanned"]=i[j].e.c-i[j].s.c+1,o["table:number-rows-spanned"]=i[j].e.r-i[j].s.r+1;break}if(n){e.push("          <table:covered-table-cell/>\n");continue}var q=cu({r:f,c:g}),r=k?(b[f]||[])[g]:b[q];if(r&&r.f&&(o["table:formula"]=bj(("of:="+r.f.replace(fe,"$1[.$2$3$4$5]").replace(/\]:\[/g,":")).replace(/;/g,"|").replace(/,/g,";")),r.F&&r.F.slice(0,q.length)==q)){var s=cv(r.F);o["table:number-matrix-columns-spanned"]=s.e.c-s.s.c+1,o["table:number-matrix-rows-spanned"]=s.e.r-s.s.r+1}if(!r){e.push(a);continue}switch(r.t){case"b":p=r.v?"TRUE":"FALSE",o["office:value-type"]="boolean",o["office:boolean-value"]=r.v?"true":"false";break;case"n":p=r.w||String(r.v||0),o["office:value-type"]="float",o["office:value"]=r.v||0;break;case"s":case"str":p=null==r.v?"":r.v,o["office:value-type"]="string";break;case"d":p=r.w||aP(r.v).toISOString(),o["office:value-type"]="date",o["office:date-value"]=aP(r.v).toISOString(),o["table:style-name"]="ce1";break;default:e.push(a);continue}var t=bj(p).replace(/  +/g,function(a){return'<text:s text:c="'+a.length+'"/>'}).replace(/\t/g,"<text:tab/>").replace(/\n/g,"</text:p><text:p>").replace(/^ /,"<text:s/>").replace(/ $/,"<text:s/>");if(r.l&&r.l.Target){var u=r.l.Target;"#"==(u="#"==u.charAt(0)?"#"+u.slice(1).replace(/\./,"!"):u).charAt(0)||u.match(/^\w+:/)||(u="../"+u),t=bE("text:a",t,{"xlink:href":u.replace(/&/g,"&amp;")})}e.push("          "+bE("table:table-cell",bE("text:p",t,{}),o)+"\n")}e.push("        </table:table-row>\n")}return e.push("      </table:table>\n"),e.join("")},c=function(a,b){a.push(" <office:automatic-styles>\n"),a.push('  <number:date-style style:name="N37" number:automatic-order="true">\n'),a.push('   <number:month number:style="long"/>\n'),a.push("   <number:text>/</number:text>\n"),a.push('   <number:day number:style="long"/>\n'),a.push("   <number:text>/</number:text>\n"),a.push("   <number:year/>\n"),a.push("  </number:date-style>\n");var c=0;b.SheetNames.map(function(a){return b.Sheets[a]}).forEach(function(b){if(b&&b["!cols"]){for(var d=0;d<b["!cols"].length;++d)if(b["!cols"][d]){var e=b["!cols"][d];if(null==e.width&&null==e.wpx&&null==e.wch)continue;eN(e),e.ods=c;var f=b["!cols"][d].wpx+"px";a.push('  <style:style style:name="co'+c+'" style:family="table-column">\n'),a.push('   <style:table-column-properties fo:break-before="auto" style:column-width="'+f+'"/>\n'),a.push("  </style:style>\n"),++c}}});var d=0;b.SheetNames.map(function(a){return b.Sheets[a]}).forEach(function(b){if(b&&b["!rows"]){for(var c=0;c<b["!rows"].length;++c)if(b["!rows"][c]){b["!rows"][c].ods=d;var e=b["!rows"][c].hpx+"px";a.push('  <style:style style:name="ro'+d+'" style:family="table-row">\n'),a.push('   <style:table-row-properties fo:break-before="auto" style:row-height="'+e+'"/>\n'),a.push("  </style:style>\n"),++d}}}),a.push('  <style:style style:name="ta1" style:family="table" style:master-page-name="mp1">\n'),a.push('   <style:table-properties table:display="true" style:writing-mode="lr-tb"/>\n'),a.push("  </style:style>\n"),a.push('  <style:style style:name="ce1" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="N37"/>\n'),a.push(" </office:automatic-styles>\n")};return function(a,d){var e=[a6],f=bD({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:meta":"urn:oasis:names:tc:opendocument:xmlns:meta:1.0","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:presentation":"urn:oasis:names:tc:opendocument:xmlns:presentation:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:chart":"urn:oasis:names:tc:opendocument:xmlns:chart:1.0","xmlns:dr3d":"urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0","xmlns:math":"http://www.w3.org/1998/Math/MathML","xmlns:form":"urn:oasis:names:tc:opendocument:xmlns:form:1.0","xmlns:script":"urn:oasis:names:tc:opendocument:xmlns:script:1.0","xmlns:ooo":"http://openoffice.org/2004/office","xmlns:ooow":"http://openoffice.org/2004/writer","xmlns:oooc":"http://openoffice.org/2004/calc","xmlns:dom":"http://www.w3.org/2001/xml-events","xmlns:xforms":"http://www.w3.org/2002/xforms","xmlns:xsd":"http://www.w3.org/2001/XMLSchema","xmlns:xsi":"http://www.w3.org/2001/XMLSchema-instance","xmlns:sheet":"urn:oasis:names:tc:opendocument:sh33tjs:1.0","xmlns:rpt":"http://openoffice.org/2005/report","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","xmlns:xhtml":"http://www.w3.org/1999/xhtml","xmlns:grddl":"http://www.w3.org/2003/g/data-view#","xmlns:tableooo":"http://openoffice.org/2009/table","xmlns:drawooo":"http://openoffice.org/2010/draw","xmlns:calcext":"urn:org:documentfoundation:names:experimental:calc:xmlns:calcext:1.0","xmlns:loext":"urn:org:documentfoundation:names:experimental:office:xmlns:loext:1.0","xmlns:field":"urn:openoffice:names:experimental:ooo-ms-interop:xmlns:field:1.0","xmlns:formx":"urn:openoffice:names:experimental:ooxml-odf-interop:xmlns:form:1.0","xmlns:css3t":"http://www.w3.org/TR/css3-text/","office:version":"1.2"}),g=bD({"xmlns:config":"urn:oasis:names:tc:opendocument:xmlns:config:1.0","office:mimetype":"application/vnd.oasis.opendocument.spreadsheet"});"fods"==d.bookType?(e.push("<office:document"+f+g+">\n"),e.push(da().replace(/office:document-meta/g,"office:meta"))):e.push("<office:document-content"+f+">\n"),c(e,a),e.push("  <office:body>\n"),e.push("    <office:spreadsheet>\n");for(var h=0;h!=a.SheetNames.length;++h)e.push(b(a.Sheets[a.SheetNames[h]],a,h,d));return e.push("    </office:spreadsheet>\n"),e.push("  </office:body>\n"),"fods"==d.bookType?e.push("</office:document>"):e.push("</office:document-content>"),e.join("")}}();function gF(a,b){if("fods"==b.bookType)return gE(a,b);var c=a3(),d="",e=[],f=[];return a2(c,d="mimetype","application/vnd.oasis.opendocument.spreadsheet"),a2(c,d="content.xml",gE(a,b)),e.push([d,"text/xml"]),f.push([d,"ContentFile"]),a2(c,d="styles.xml",gD(a,b)),e.push([d,"text/xml"]),f.push([d,"StylesFile"]),a2(c,d="meta.xml",a6+da()),e.push([d,"text/xml"]),f.push([d,"MetadataFile"]),a2(c,d="manifest.rdf",function(a){var b=[a6];b.push('<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">\n');for(var c=0;c!=a.length;++c)b.push(c9(a[c][0],a[c][1])),b.push('  <rdf:Description rdf:about="">\n    <ns0:hasPart xmlns:ns0="http://docs.oasis-open.org/ns/office/1.2/meta/pkg#" rdf:resource="'+a[c][0]+'"/>\n  </rdf:Description>\n');return b.push(c9("","Document","pkg")),b.push("</rdf:RDF>"),b.join("")}(f)),e.push([d,"application/rdf+xml"]),a2(c,d="META-INF/manifest.xml",function(a){var b=[a6];b.push('<manifest:manifest xmlns:manifest="urn:oasis:names:tc:opendocument:xmlns:manifest:1.0" manifest:version="1.2">\n'),b.push('  <manifest:file-entry manifest:full-path="/" manifest:version="1.2" manifest:media-type="application/vnd.oasis.opendocument.spreadsheet"/>\n');for(var c=0;c<a.length;++c)b.push('  <manifest:file-entry manifest:full-path="'+a[c][0]+'" manifest:media-type="'+a[c][1]+'"/>\n');return b.push("</manifest:manifest>"),b.join("")}(e)),c}function gG(a){return new DataView(a.buffer,a.byteOffset,a.byteLength)}function gH(a){return"undefined"!=typeof TextDecoder?new TextDecoder().decode(a):bt(H(a))}function gI(a){var b=new Uint8Array(a.reduce(function(a,b){return a+b.length},0)),c=0;return a.forEach(function(a){b.set(a,c),c+=a.length}),b}function gJ(a){return a-=a>>1&0x55555555,((a=(0x33333333&a)+(a>>2&0x33333333))+(a>>4)&0xf0f0f0f)*0x1010101>>>24}function gK(a,b){var c=b?b[0]:0,d=127&a[c];c:if(a[c++]>=128&&(d|=(127&a[c])<<7,a[c++]<128||(d|=(127&a[c])<<14,a[c++]<128)||(d|=(127&a[c])<<21,a[c++]<128)||(d+=(127&a[c])*0x10000000,++c,a[c++]<128)||(d+=(127&a[c])*0x800000000,++c,a[c++]<128)||(d+=(127&a[c])*0x40000000000,++c,a[c++]<128)))break c;return b&&(b[0]=c),d}function gL(a){var b=new Uint8Array(7);b[0]=127&a;var c=1;d:if(a>127){if(b[c-1]|=128,b[c]=a>>7&127,++c,a<=16383||(b[c-1]|=128,b[c]=a>>14&127,++c,a<=2097151)||(b[c-1]|=128,b[c]=a>>21&127,++c,a<=0xfffffff)||(b[c-1]|=128,b[c]=a/256>>>21&127,++c,a<=0x7ffffffff)||(b[c-1]|=128,b[c]=a/65536>>>21&127,++c,a<=0x3ffffffffff))break d;b[c-1]|=128,b[c]=a/0x1000000>>>21&127,++c}return b.slice(0,c)}function gM(a){var b=0,c=127&a[0];c:if(a[b++]>=128){if(c|=(127&a[b])<<7,a[b++]<128||(c|=(127&a[b])<<14,a[b++]<128)||(c|=(127&a[b])<<21,a[b++]<128))break c;c|=(127&a[b])<<28}return c}function gN(a){for(var b=[],c=[0];c[0]<a.length;){var d,e=c[0],f=gK(a,c),g=7&f,h=0;if(0==(f=Math.floor(f/8)))break;switch(g){case 0:for(var i=c[0];a[c[0]++]>=128;);d=a.slice(i,c[0]);break;case 5:h=4,d=a.slice(c[0],c[0]+h),c[0]+=h;break;case 1:h=8,d=a.slice(c[0],c[0]+h),c[0]+=h;break;case 2:h=gK(a,c),d=a.slice(c[0],c[0]+h),c[0]+=h;break;default:throw Error("PB Type ".concat(g," for Field ").concat(f," at offset ").concat(e))}var j={data:d,type:g};null==b[f]?b[f]=[j]:b[f].push(j)}return b}function gO(a){var b=[];return a.forEach(function(a,c){a.forEach(function(a){a.data&&(b.push(gL(8*c+a.type)),2==a.type&&b.push(gL(a.data.length)),b.push(a.data))})}),gI(b)}function gP(a,b){return(null==a?void 0:a.map(function(a){return b(a.data)}))||[]}function gQ(a){for(var b,c=[],d=[0];d[0]<a.length;){var e=gK(a,d),f=gN(a.slice(d[0],d[0]+e));d[0]+=e;var g={id:gM(f[1][0].data),messages:[]};f[2].forEach(function(b){var c=gN(b.data),e=gM(c[3][0].data);g.messages.push({meta:c,data:a.slice(d[0],d[0]+e)}),d[0]+=e}),(null==(b=f[3])?void 0:b[0])&&(g.merge=gM(f[3][0].data)>>>0>0),c.push(g)}return c}function gR(a){var b=[];return a.forEach(function(a){var c=[];c[1]=[{data:gL(a.id),type:0}],c[2]=[],null!=a.merge&&(c[3]=[{data:gL(+!!a.merge),type:0}]);var d=[];a.messages.forEach(function(a){d.push(a.data),a.meta[3]=[{type:0,data:gL(a.data.length)}],c[2].push({data:gO(a.meta),type:2})});var e=gO(c);b.push(gL(e.length)),b.push(e),d.forEach(function(a){return b.push(a)})}),gI(b)}function gS(a){for(var b=[],c=0;c<a.length;){var d=a[c++],e=a[c]|a[c+1]<<8|a[c+2]<<16;c+=3,b.push(function(a,b){if(0!=a)throw Error("Unexpected Snappy chunk type ".concat(a));for(var c=[0],d=gK(b,c),e=[];c[0]<b.length;){var f=3&b[c[0]];if(0==f){var g=b[c[0]++]>>2;if(g<60)++g;else{var h=g-59;g=b[c[0]],h>1&&(g|=b[c[0]+1]<<8),h>2&&(g|=b[c[0]+2]<<16),h>3&&(g|=b[c[0]+3]<<24),g>>>=0,g++,c[0]+=h}e.push(b.slice(c[0],c[0]+g)),c[0]+=g;continue}var i=0,j=0;if(1==f?(j=(b[c[0]]>>2&7)+4,i=(224&b[c[0]++])<<3|b[c[0]++]):(j=(b[c[0]++]>>2)+1,2==f?(i=b[c[0]]|b[c[0]+1]<<8,c[0]+=2):(i=(b[c[0]]|b[c[0]+1]<<8|b[c[0]+2]<<16|b[c[0]+3]<<24)>>>0,c[0]+=4)),e=[gI(e)],0==i)throw Error("Invalid offset 0");if(i>e[0].length)throw Error("Invalid offset beyond length");if(j>=i)for(e.push(e[0].slice(-i)),j-=i;j>=e[e.length-1].length;)e.push(e[e.length-1]),j-=e[e.length-1].length;e.push(e[0].slice(-i,-i+j))}var k=gI(e);if(k.length!=d)throw Error("Unexpected length: ".concat(k.length," != ").concat(d));return k}(d,a.slice(c,c+e))),c+=e}if(c!==a.length)throw Error("data is not a valid framed stream!");return gI(b)}function gT(a){for(var b=[],c=0;c<a.length;){var d=Math.min(a.length-c,0xfffffff),e=new Uint8Array(4);b.push(e);var f=gL(d),g=f.length;b.push(f),d<=60?(g++,b.push(new Uint8Array([d-1<<2]))):d<=256?(g+=2,b.push(new Uint8Array([240,d-1&255]))):d<=65536?(g+=3,b.push(new Uint8Array([244,d-1&255,d-1>>8&255]))):d<=0x1000000?(g+=4,b.push(new Uint8Array([248,d-1&255,d-1>>8&255,d-1>>16&255]))):d<=0x100000000&&(g+=5,b.push(new Uint8Array([252,d-1&255,d-1>>8&255,d-1>>16&255,d-1>>>24&255]))),b.push(a.slice(c,c+d)),g+=d,e[0]=0,e[1]=255&g,e[2]=g>>8&255,e[3]=g>>16&255,c+=d}return gI(b)}function gU(a,b){var c=new Uint8Array(32),d=gG(c),e=12,f=0;switch(c[0]=5,a.t){case"n":c[1]=2,function(a,b,c){var d=Math.floor(0==c?0:Math.LOG10E*Math.log(Math.abs(c)))+6176-20,e=c/Math.pow(10,d-6176);a[b+15]|=d>>7,a[b+14]|=(127&d)<<1;for(var f=0;e>=1;++f,e/=256)a[b+f]=255&e;a[b+15]|=c>=0?0:128}(c,e,a.v),f|=1,e+=16;break;case"b":c[1]=6,d.setFloat64(e,+!!a.v,!0),f|=2,e+=8;break;case"s":if(-1==b.indexOf(a.v))throw Error("Value ".concat(a.v," missing from SST!"));c[1]=3,d.setUint32(e,b.indexOf(a.v),!0),f|=8,e+=4;break;default:throw"unsupported cell type "+a.t}return d.setUint32(8,f,!0),c.slice(0,e)}function gV(a,b){var c=new Uint8Array(32),d=gG(c),e=12,f=0;switch(c[0]=3,a.t){case"n":c[2]=2,d.setFloat64(e,a.v,!0),f|=32,e+=8;break;case"b":c[2]=6,d.setFloat64(e,+!!a.v,!0),f|=32,e+=8;break;case"s":if(-1==b.indexOf(a.v))throw Error("Value ".concat(a.v," missing from SST!"));c[2]=3,d.setUint32(e,b.indexOf(a.v),!0),f|=16,e+=4;break;default:throw"unsupported cell type "+a.t}return d.setUint32(4,f,!0),c.slice(0,e)}function gW(a){return gK(gN(a)[1][0].data)}function gX(a,b){var c=gN(b.data),d=gM(c[1][0].data),e=c[3],f=[];return(e||[]).forEach(function(b){var c=gN(b.data),e=gM(c[1][0].data)>>>0;switch(d){case 1:f[e]=gH(c[3][0].data);break;case 8:var g=gN(a[gW(c[9][0].data)][0].data),h=a[gW(g[1][0].data)][0],i=gM(h.meta[1][0].data);if(2001!=i)throw Error("2000 unexpected reference to ".concat(i));var j=gN(h.data);f[e]=j[3].map(function(a){return gH(a.data)}).join("")}}),f}function gY(a){var b,c,d,e,f={},g=[];if(a.FullPaths.forEach(function(a){if(a.match(/\.iwpv2/))throw Error("Unsupported password protection")}),a.FileIndex.forEach(function(a){var b,c;if(a.name.match(/\.iwa$/)){try{b=gS(a.content)}catch(b){return console.log("?? "+a.content.length+" "+(b.message||b))}try{c=gQ(b)}catch(a){return console.log("## "+(a.message||a))}c.forEach(function(a){f[a.id]=a.messages,g.push(a.id)})}}),!g.length)throw Error("File has no messages");var h=(null==(e=null==(d=null==(c=null==(b=null==f?void 0:f[1])?void 0:b[0])?void 0:c.meta)?void 0:d[1])?void 0:e[0].data)&&1==gM(f[1][0].meta[1][0].data)&&f[1][0];if(h||g.forEach(function(a){f[a].forEach(function(a){if(1==gM(a.meta[1][0].data)>>>0)if(h)throw Error("Document has multiple roots");else h=a})}),!h)throw Error("Cannot find Document root");var i=h,j=ha();if(gP(gN(i.data)[1],gW).forEach(function(a){f[a].forEach(function(a){if(2==gM(a.meta[1][0].data)){var b,c,d,e=(d={name:(null==(b=(c=gN(a.data))[1])?void 0:b[0])?gH(c[1][0].data):"",sheets:[]},gP(c[2],gW).forEach(function(a){f[a].forEach(function(a){6e3==gM(a.meta[1][0].data)&&d.sheets.push(function(a,b){var c=gN(b.data),d={"!ref":"A1"},e=a[gW(c[2][0].data)],f=gM(e[0].meta[1][0].data);if(6001!=f)throw Error("6000 unexpected reference to ".concat(f));return!function(a,b,c){var d,e=gN(b.data),f={s:{r:0,c:0},e:{r:0,c:0}};if(f.e.r=(gM(e[6][0].data)>>>0)-1,f.e.r<0)throw Error("Invalid row varint ".concat(e[6][0].data));if(f.e.c=(gM(e[7][0].data)>>>0)-1,f.e.c<0)throw Error("Invalid col varint ".concat(e[7][0].data));c["!ref"]=cw(f);var g=gN(e[4][0].data),h=gX(a,a[gW(g[4][0].data)][0]),i=(null==(d=g[17])?void 0:d[0])?gX(a,a[gW(g[17][0].data)][0]):[],j=gN(g[3][0].data),k=0;j[1].forEach(function(b){var d,e,f,g,j=a[gW(gN(b.data)[2][0].data)][0],l=gM(j.meta[1][0].data);if(6002!=l)throw Error("6001 unexpected reference to ".concat(l));var m=(f=(null==(d=null==(e=gN(j.data))?void 0:e[7])?void 0:d[0])?+(gM(e[7][0].data)>>>0>0):-1,g=gP(e[5],function(a){return function(a,b){var c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s=gN(a),t=gM(s[1][0].data)>>>0,u=gM(s[2][0].data)>>>0,v=(null==(d=null==(c=s[8])?void 0:c[0])?void 0:d.data)&&gM(s[8][0].data)>0||!1;if((null==(f=null==(e=s[7])?void 0:e[0])?void 0:f.data)&&0!=b)q=null==(h=null==(g=s[7])?void 0:g[0])?void 0:h.data,r=null==(j=null==(i=s[6])?void 0:i[0])?void 0:j.data;else if((null==(l=null==(k=s[4])?void 0:k[0])?void 0:l.data)&&1!=b)q=null==(n=null==(m=s[4])?void 0:m[0])?void 0:n.data,r=null==(p=null==(o=s[3])?void 0:o[0])?void 0:p.data;else throw"NUMBERS Tile missing ".concat(b," cell storage");for(var w=v?4:1,x=gG(q),y=[],z=0;z<q.length/2;++z){var A=x.getUint16(2*z,!0);A<65535&&y.push([z,A])}if(y.length!=u)throw"Expected ".concat(u," cells, found ").concat(y.length);var B=[];for(z=0;z<y.length-1;++z)B[y[z][0]]=r.subarray(y[z][1]*w,y[z+1][1]*w);return y.length>=1&&(B[y[y.length-1][0]]=r.subarray(y[y.length-1][1]*w)),{R:t,cells:B}}(a,f)}),{nrows:gM(e[4][0].data)>>>0,data:g.reduce(function(a,b){return a[b.R]||(a[b.R]=[]),b.cells.forEach(function(c,d){if(a[b.R][d])throw Error("Duplicate cell r=".concat(b.R," c=").concat(d));a[b.R][d]=c}),a},[])});m.data.forEach(function(a,b){a.forEach(function(a,d){var e=cu({r:k+b,c:d}),f=function(a,b,c){switch(a[0]){case 0:case 1:case 2:case 3:return function(a,b,c,d){var e,f=gG(a),g=f.getUint32(4,!0),h=(d>1?12:8)+4*gJ(g&(d>1?3470:398)),i=-1,j=-1,k=NaN,l=new Date(2001,0,1);switch(512&g&&(i=f.getUint32(h,!0),h+=4),h+=4*gJ(g&(d>1?12288:4096)),16&g&&(j=f.getUint32(h,!0),h+=4),32&g&&(k=f.getFloat64(h,!0),h+=8),64&g&&(l.setTime(l.getTime()+1e3*f.getFloat64(h,!0)),h+=8),a[2]){case 0:break;case 2:e={t:"n",v:k};break;case 3:e={t:"s",v:b[j]};break;case 5:e={t:"d",v:l};break;case 6:e={t:"b",v:k>0};break;case 7:e={t:"n",v:k/86400};break;case 8:e={t:"e",v:0};break;case 9:if(i>-1)e={t:"s",v:c[i]};else if(j>-1)e={t:"s",v:b[j]};else if(isNaN(k))throw Error("Unsupported cell type ".concat(a.slice(0,4)));else e={t:"n",v:k};break;default:throw Error("Unsupported cell type ".concat(a.slice(0,4)))}return e}(a,b,c,a[0]);case 5:return function(a,b,c){var d,e=gG(a),f=e.getUint32(8,!0),g=12,h=-1,i=-1,j=NaN,k=NaN,l=new Date(2001,0,1);switch(1&f&&(j=function(a,b){for(var c=(127&a[b+15])<<7|a[b+14]>>1,d=1&a[b+14],e=b+13;e>=b;--e)d=256*d+a[e];return(128&a[b+15]?-d:d)*Math.pow(10,c-6176)}(a,g),g+=16),2&f&&(k=e.getFloat64(g,!0),g+=8),4&f&&(l.setTime(l.getTime()+1e3*e.getFloat64(g,!0)),g+=8),8&f&&(i=e.getUint32(g,!0),g+=4),16&f&&(h=e.getUint32(g,!0),g+=4),a[1]){case 0:break;case 2:case 10:d={t:"n",v:j};break;case 3:d={t:"s",v:b[i]};break;case 5:d={t:"d",v:l};break;case 6:d={t:"b",v:k>0};break;case 7:d={t:"n",v:k/86400};break;case 8:d={t:"e",v:0};break;case 9:if(h>-1)d={t:"s",v:c[h]};else throw Error("Unsupported cell type ".concat(a[1]," : ").concat(31&f," : ").concat(a.slice(0,4)));break;default:throw Error("Unsupported cell type ".concat(a[1]," : ").concat(31&f," : ").concat(a.slice(0,4)))}return d}(a,b,c);default:throw Error("Unsupported payload version ".concat(a[0]))}}(a,h,i);f&&(c[e]=f)})}),k+=m.nrows})}(a,e[0],d),d}(f,a))})}),d);e.sheets.forEach(function(a,b){hb(j,a,0==b?e.name:e.name+"_"+b,!0)})}})}),0==j.SheetNames.length)throw Error("Empty NUMBERS file");return j}function gZ(a){return function(b){for(var c=0;c!=a.length;++c){var d=a[c];void 0===b[d[0]]&&(b[d[0]]=d[1]),"n"===d[2]&&(b[d[0]]=Number(b[d[0]]))}}}function g$(a){gZ([["cellNF",!1],["cellHTML",!0],["cellFormula",!0],["cellStyles",!1],["cellText",!0],["cellDates",!1],["sheetStubs",!1],["sheetRows",0,"n"],["bookDeps",!1],["bookSheets",!1],["bookProps",!1],["bookFiles",!1],["bookVBA",!1],["password",""],["WTF",!1]])(a)}function g_(a){gZ([["cellDates",!1],["bookSST",!1],["bookType","xlsx"],["compression",!1],["WTF",!1]])(a)}function g0(a){return"/"==a.charAt(0)?a.slice(1):a}function g1(a,b){var c="";switch((b||{}).type||"base64"){case"buffer":case"array":return[a[0],a[1],a[2],a[3],a[4],a[5],a[6],a[7]];case"base64":c=A(a.slice(0,12));break;case"binary":c=a;break;default:throw Error("Unrecognized type "+(b&&b.type||"undefined"))}return[c.charCodeAt(0),c.charCodeAt(1),c.charCodeAt(2),c.charCodeAt(3),c.charCodeAt(4),c.charCodeAt(5),c.charCodeAt(6),c.charCodeAt(7)]}function g2(a,b){var c=0;e:for(;c<a.length;)switch(a.charCodeAt(c)){case 10:case 13:case 32:++c;break;case 60:return go(a.slice(c),b);default:break e}return ei.to_workbook(a,b)}function g3(a,b,c,d){return d?(c.type="string",ei.to_workbook(a,c)):ei.to_workbook(b,c)}function g4(a,b){switch(b.type){case"base64":case"binary":break;case"buffer":case"array":b.type="";break;case"file":return aB(b.file,aA.write(a,{type:B?"buffer":""}));case"string":throw Error("'string' output type invalid for '"+b.bookType+"' files");default:throw Error("Unrecognized type "+b.type)}return aA.write(a,b)}function g5(a,b,c){c||(c="");var d=c+a;switch(b.type){case"base64":return z(bu(d));case"binary":return bu(d);case"string":return a;case"file":return aB(b.file,d,"utf8");case"buffer":if(B)return C(d,"utf8");if("undefined"!=typeof TextEncoder)return new TextEncoder().encode(d);return g5(d,{type:"binary"}).split("").map(function(a){return a.charCodeAt(0)})}throw Error("Unrecognized type "+b.type)}function g6(a,b){switch(b.type){case"string":case"base64":case"binary":for(var c="",d=0;d<a.length;++d)c+=String.fromCharCode(a[d]);return"base64"==b.type?z(c):"string"==b.type?bt(c):c;case"file":return aB(b.file,a);case"buffer":return a;default:throw Error("Unrecognized type "+b.type)}}function g7(a,b){if(null==a||null==a["!ref"])return[];var c={t:"n",v:0},d=0,e=1,f=[],g=0,h="",i={s:{r:0,c:0},e:{r:0,c:0}},j=b||{},k=null!=j.range?j.range:a["!ref"];switch(1===j.header?d=1:"A"===j.header?d=2:Array.isArray(j.header)?d=3:null==j.header&&(d=0),typeof k){case"string":i=cx(k);break;case"number":(i=cx(a["!ref"])).s.r=k;break;default:i=k}d>0&&(e=0);var l=cq(i.s.r),m=[],n=[],o=0,p=0,q=Array.isArray(a),r=i.s.r,s=0,t={};q&&!a[r]&&(a[r]=[]);var u=j.skipHidden&&a["!cols"]||[],v=j.skipHidden&&a["!rows"]||[];for(s=i.s.c;s<=i.e.c;++s)if(!(u[s]||{}).hidden)switch(m[s]=cs(s),c=q?a[r][s]:a[m[s]+l],d){case 1:f[s]=s-i.s.c;break;case 2:f[s]=m[s];break;case 3:f[s]=j.header[s-i.s.c];break;default:if(null==c&&(c={w:"__EMPTY",t:"s"}),h=g=cz(c,null,j),p=t[g]||0){do h=g+"_"+p++;while(t[h]);t[g]=p,t[h]=1}else t[g]=1;f[s]=h}for(r=i.s.r+e;r<=i.e.r;++r)if(!(v[r]||{}).hidden){var w=function(a,b,c,d,e,f,g,h){var i=cq(c),j=h.defval,k=h.raw||!Object.prototype.hasOwnProperty.call(h,"raw"),l=!0,m=1===e?[]:{};if(1!==e)if(Object.defineProperty)try{Object.defineProperty(m,"__rowNum__",{value:c,enumerable:!1})}catch(a){m.__rowNum__=c}else m.__rowNum__=c;if(!g||a[c])for(var n=b.s.c;n<=b.e.c;++n){var o=g?a[c][n]:a[d[n]+i];if(void 0===o||void 0===o.t){if(void 0===j)continue;null!=f[n]&&(m[f[n]]=j);continue}var p=o.v;switch(o.t){case"z":if(null==p)break;continue;case"e":p=0==p?null:void 0;break;case"s":case"d":case"b":case"n":break;default:throw Error("unrecognized type "+o.t)}if(null!=f[n]){if(null==p)if("e"==o.t&&null===p)m[f[n]]=null;else if(void 0!==j)m[f[n]]=j;else{if(!k||null!==p)continue;m[f[n]]=null}else m[f[n]]=k&&("n"!==o.t||"n"===o.t&&!1!==h.rawNumbers)?p:cz(o,p,h);null!=p&&(l=!1)}}return{row:m,isempty:l}}(a,i,r,m,d,f,q,j);(!1===w.isempty||(1===d?!1!==j.blankrows:j.blankrows))&&(n[o++]=w.row)}return n.length=o,n}var g8=/"/g;function g9(a,b){var c=[],d=null==b?{}:b;if(null==a||null==a["!ref"])return"";var e=cx(a["!ref"]),f=void 0!==d.FS?d.FS:",",g=f.charCodeAt(0),h=void 0!==d.RS?d.RS:"\n",i=h.charCodeAt(0),j=RegExp(("|"==f?"\\|":f)+"+$"),k="",l=[];d.dense=Array.isArray(a);for(var m=d.skipHidden&&a["!cols"]||[],n=d.skipHidden&&a["!rows"]||[],o=e.s.c;o<=e.e.c;++o)(m[o]||{}).hidden||(l[o]=cs(o));for(var p=0,q=e.s.r;q<=e.e.r;++q)!(n[q]||{}).hidden&&null!=(k=function(a,b,c,d,e,f,g,h){for(var i=!0,j=[],k="",l=cq(c),m=b.s.c;m<=b.e.c;++m)if(d[m]){var n=h.dense?(a[c]||[])[m]:a[d[m]+l];if(null==n)k="";else if(null!=n.v){i=!1,k=""+(h.rawNumbers&&"n"==n.t?n.v:cz(n,null,h));for(var o=0,p=0;o!==k.length;++o)if((p=k.charCodeAt(o))===e||p===f||34===p||h.forceQuotes){k='"'+k.replace(g8,'""')+'"';break}"ID"==k&&(k='"ID"')}else null==n.f||n.F?k="":(i=!1,(k="="+n.f).indexOf(",")>=0&&(k='"'+k.replace(g8,'""')+'"'));j.push(k)}return!1===h.blankrows&&i?null:j.join(g)}(a,e,q,l,g,i,f,d))&&(d.strip&&(k=k.replace(j,"")),(k||!1!==d.blankrows)&&c.push((p++?h:"")+k));return delete d.dense,c.join("")}function ha(){return{SheetNames:[],Sheets:{}}}function hb(a,b,c,d){var e=1;if(!c)for(;e<=65535&&-1!=a.SheetNames.indexOf(c="Sheet"+e);++e,c=void 0);if(!c||a.SheetNames.length>=65535)throw Error("Too many worksheets");if(d&&a.SheetNames.indexOf(c)>=0){var f=c.match(/(^.*?)(\d+)$/);e=f&&+f[2]||0;var g=f&&f[1]||c;for(++e;e<=65535&&-1!=a.SheetNames.indexOf(c=g+e);++e);}if(gg(c),a.SheetNames.indexOf(c)>=0)throw Error("Worksheet with name |"+c+"| already exists!");return a.SheetNames.push(c),a.Sheets[c]=b,c}var hc={aoa_to_sheet:cB,sheet_to_json:g7,book_new:ha,book_append_sheet:hb};function hd(){let a=(0,k.useRouter)(),[b,c]=(0,j.useState)(null),[f,g]=(0,j.useState)([]),[h,m]=(0,j.useState)([]),[n,o]=(0,j.useState)(!1),[p,q]=(0,j.useState)(""),r=a=>{let b=[];a.forEach((a,c)=>{let d=c+2;a.name.trim()||b.push(`الصف ${d}: الاسم مطلوب`),a.rankKey.trim()||b.push(`الصف ${d}: الرتبة مطلوبة`),a.nationalId.trim()?/^\d{12}$/.test(a.nationalId)||b.push(`الصف ${d}: الرقم الوطني يجب أن يكون 12 رقم`):b.push(`الصف ${d}: الرقم الوطني مطلوب`),a.bankAccount&&!/^\d{15}$/.test(a.bankAccount)&&b.push(`الصف ${d}: رقم الحساب المصرفي يجب أن يكون 15 رقم`)}),m(b)},t=async()=>{if(h.length>0)return void alert("يرجى إصلاح الأخطاء قبل الاستيراد");o(!0);try{let b=localStorage.getItem("token");for(let a of f){let c=await fetch("http://127.0.0.1:4001/employees",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${b}`},body:JSON.stringify({name:a.name,rankKey:a.rankKey,militaryNumber:a.militaryNumber||null,nationalId:a.nationalId,unitId:a.unitId?parseInt(a.unitId):void 0,birthDate:a.birthDate||null,birthPlace:a.birthPlace||null,motherName:a.motherName||null,maritalStatus:a.maritalStatus||null,appointmentDate:a.appointmentDate||null,lastPromotionDate:a.lastPromotionDate||null,bankName:a.bankName||null,bankBranch:a.bankBranch||null,bankAccount:a.bankAccount||null,leaveBalance:a.leaveBalance?parseInt(a.leaveBalance):0,leaveType:a.leaveType||null,employmentStatus:a.employmentStatus||null,qualification:a.qualification||null,qualificationDate:a.qualificationDate||null})});if(!c.ok){let b=await c.json();throw Error(`خطأ في إضافة الموظف ${a.name}: ${b.error}`)}}q(`تم استيراد ${f.length} موظف بنجاح`),setTimeout(()=>{a.push("/employees")},2e3)}catch(a){m([a instanceof Error?a.message:"خطأ في الاستيراد"])}finally{o(!1)}};return(0,i.jsxs)("div",{className:"min-h-screen bg-gray-50",dir:"rtl",children:[(0,i.jsx)("nav",{className:"bg-white shadow-sm border-b",children:(0,i.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,i.jsxs)("div",{className:"flex justify-between h-16",children:[(0,i.jsx)("div",{className:"flex items-center",children:(0,i.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"استيراد بيانات الموظفين"})}),(0,i.jsxs)("div",{className:"flex items-center space-x-4 space-x-reverse",children:[(0,i.jsx)("button",{onClick:()=>a.push("/employees"),className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium",children:"قائمة الموظفين"}),(0,i.jsx)("button",{onClick:()=>a.push("/dashboard"),className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium",children:"لوحة التحكم"}),(0,i.jsx)("button",{onClick:()=>{localStorage.removeItem("token"),a.push("/login")},className:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:"تسجيل الخروج"})]})]})})}),(0,i.jsx)("main",{className:"max-w-4xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,i.jsx)("div",{className:"px-4 py-6 sm:px-0",children:(0,i.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,i.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,i.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"استيراد الموظفين من ملف Excel"}),(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("button",{onClick:()=>{let a=hc.aoa_to_sheet([["الاسم","الرتبة","الرقم العسكري","الرقم الوطني","الوحدة","تاريخ الميلاد","مكان الميلاد","اسم الأم","الحالة الاجتماعية","تاريخ التعيين","تاريخ آخر ترقية","اسم المصرف","فرع المصرف","رقم الحساب","رصيد الإجازات","نوع الإجازة","حالة الموظف","المؤهل العلمي","تاريخ الحصول على المؤهل"],["أحمد محمد علي","ملازم","12345","123456789012","1","1990-01-01","بغداد","فاطمة أحمد","متزوج","2015-01-01","2020-01-01","البنك الأهلي","فرع المنصور","123456789012345","30","سنوية","مستمر","بكالوريوس","2014-06-01"]]),b=hc.book_new();hc.book_append_sheet(b,a,"قالب الموظفين"),function(a,b,c){var d={};d.type="file",d.file=b;if(!d.bookType){var f=d.file.slice(d.file.lastIndexOf(".")).toLowerCase();f.match(/^\.[a-z]+$/)&&(d.bookType=f.slice(1)),d.bookType=({xls:"biff8",htm:"html",slk:"sylk",socialcalc:"eth",Sh33tJS:"WTF"})[d.bookType]||d.bookType}!function a(b,c){s(),function(a){if(!a||!a.SheetNames||!a.Sheets)throw Error("Invalid Workbook");if(!a.SheetNames.length)throw Error("Workbook is empty");var b,c,d=a.Workbook&&a.Workbook.Sheets||[];b=a.SheetNames,c=!!a.vbaraw,b.forEach(function(a,e){gg(a);for(var f=0;f<e;++f)if(a==b[f])throw Error("Duplicate Sheet Name: "+a);if(c){var g=d&&d[e]&&d[e].CodeName||a;if(95==g.charCodeAt(0)&&g.length>22)throw Error("Bad Code Name: Worksheet"+g)}});for(var e=0;e<a.SheetNames.length;++e)!function(a,b,c){if(a&&a["!ref"]){var d=cx(a["!ref"]);if(d.e.c<d.s.c||d.e.r<d.s.r)throw Error("Bad range ("+c+"): "+a["!ref"])}}(a.Sheets[a.SheetNames[e]],a.SheetNames[e],e)}(b);var d,f,g=aR(c||{});if(g.cellStyles&&(g.cellNF=!0,g.sheetStubs=!0),"array"==g.type){g.type="binary";var h=a(b,g);return g.type="array",G(h)}var i=0;if(g.sheet&&(i="number"==typeof g.sheet?g.sheet:b.SheetNames.indexOf(g.sheet),!b.SheetNames[i]))throw Error("Sheet not found: "+g.sheet+" : "+typeof g.sheet);switch(g.bookType||"xlsb"){case"xml":case"xlml":return g5(function(a,b){b||(b={}),a.SSF||(a.SSF=aR(U)),a.SSF&&(aw(),av(a.SSF),b.revssf=aF(a.SSF),b.revssf[a.SSF[65535]]=0,b.ssf=a.SSF,b.cellXfs=[],fQ(b.cellXfs,{},{revssf:{General:0}}));var c,d,e,f,g,h,i,j,k,l,m=[];m.push((c=b,d=[],a.Props&&d.push((e=a.Props,f=[],aC(dm).map(function(a){for(var b=0;b<db.length;++b)if(db[b][1]==a)return db[b];for(b=0;b<dg.length;++b)if(dg[b][1]==a)return dg[b];throw a}).forEach(function(a){if(null!=e[a[1]]){var b=c&&c.Props&&null!=c.Props[a[1]]?c.Props[a[1]]:e[a[1]];"date"===a[2]&&(b=new Date(b).toISOString().replace(/\.\d*Z/,"Z")),"number"==typeof b?b=String(b):!0===b||!1===b?b=b?"1":"0":b instanceof Date&&(b=new Date(b).toISOString().replace(/\.\d*Z/,"")),f.push(bC(dm[a[1]]||a[1],b))}}),bE("DocumentProperties",f.join(""),{xmlns:bK.o}))),a.Custprops&&d.push((g=a.Props,h=a.Custprops,i=["Worksheets","SheetNames"],j="CustomDocumentProperties",k=[],g&&aC(g).forEach(function(a){if(Object.prototype.hasOwnProperty.call(g,a)){for(var b=0;b<db.length;++b)if(a==db[b][1])return;for(b=0;b<dg.length;++b)if(a==dg[b][1])return;for(b=0;b<i.length;++b)if(a==i[b])return;var c=g[a],d="string";"number"==typeof c?(d="float",c=String(c)):!0===c||!1===c?(d="boolean",c=c?"1":"0"):c=String(c),k.push(bE(bk(a),c,{"dt:dt":d}))}}),h&&aC(h).forEach(function(a){if(Object.prototype.hasOwnProperty.call(h,a)&&!(g&&Object.prototype.hasOwnProperty.call(g,a))){var b=h[a],c="string";"number"==typeof b?(c="float",b=String(b)):!0===b||!1===b?(c="boolean",b=b?"1":"0"):b instanceof Date?(c="dateTime.tz",b=b.toISOString()):b=String(b),k.push(bE(bk(a),b,{"dt:dt":c}))}}),"<"+j+' xmlns="'+bK.o+'">'+k.join("")+"</"+j+">")),d.join(""))),m.push(""),m.push(""),m.push("");for(var n=0;n<a.SheetNames.length;++n)m.push(bE("Worksheet",function(a,b,c){var d=[],e=c.SheetNames[a],f=c.Sheets[e],g=f?function(a,b,c,d){if(!a||!((d||{}).Workbook||{}).Names)return"";for(var e=d.Workbook.Names,f=[],g=0;g<e.length;++g){var h=e[g];h.Sheet==c&&(h.Name.match(/^_xlfn\./)||f.push(gp(h)))}return f.join("")}(f,0,a,c):"";return g.length>0&&d.push("<Names>"+g+"</Names>"),(g=f?function(a,b,c,d){if(!a["!ref"])return"";var e=cx(a["!ref"]),f=a["!merges"]||[],g=0,h=[];a["!cols"]&&a["!cols"].forEach(function(a,b){eN(a);var c=!!a.width,d=fO(b,a),e={"ss:Index":b+1};c&&(e["ss:Width"]=eI(d.width)),a.hidden&&(e["ss:Hidden"]="1"),h.push(bE("Column",null,e))});for(var i=Array.isArray(a),j=e.s.r;j<=e.e.r;++j){for(var k=[function(a,b){var c='<Row ss:Index="'+(a+1)+'"';return b&&(b.hpt&&!b.hpx&&(b.hpx=eP(b.hpt)),b.hpx&&(c+=' ss:AutoFitHeight="0" ss:Height="'+b.hpx+'"'),b.hidden&&(c+=' ss:Hidden="1"')),c+">"}(j,(a["!rows"]||[])[j])],l=e.s.c;l<=e.e.c;++l){var m=!1;for(g=0;g!=f.length;++g)if(!(f[g].s.c>l)&&!(f[g].s.r>j)&&!(f[g].e.c<l)&&!(f[g].e.r<j)){(f[g].s.c!=l||f[g].s.r!=j)&&(m=!0);break}if(!m){var n={r:j,c:l},o=cu(n),p=i?(a[j]||[])[l]:a[o];k.push(function(a,b,c,d,e,f,g){if(!a||void 0==a.v&&void 0==a.f)return"";var h={};if(a.f&&(h["ss:Formula"]="="+bj(ff(a.f,g))),a.F&&a.F.slice(0,b.length)==b){var i=ct(a.F.slice(b.length+1));h["ss:ArrayRange"]="RC:R"+(i.r==g.r?"":"["+(i.r-g.r)+"]")+"C"+(i.c==g.c?"":"["+(i.c-g.c)+"]")}if(a.l&&a.l.Target&&(h["ss:HRef"]=bj(a.l.Target),a.l.Tooltip&&(h["x:HRefScreenTip"]=bj(a.l.Tooltip))),c["!merges"])for(var j=c["!merges"],k=0;k!=j.length;++k)j[k].s.c==g.c&&j[k].s.r==g.r&&(j[k].e.c>j[k].s.c&&(h["ss:MergeAcross"]=j[k].e.c-j[k].s.c),j[k].e.r>j[k].s.r&&(h["ss:MergeDown"]=j[k].e.r-j[k].s.r));var l="",m="";switch(a.t){case"z":if(!d.sheetStubs)return"";break;case"n":l="Number",m=String(a.v);break;case"b":l="Boolean",m=a.v?"1":"0";break;case"e":l="Error",m=c$[a.v];break;case"d":l="DateTime",m=new Date(a.v).toISOString(),null==a.z&&(a.z=a.z||U[14]);break;case"s":l="String",m=((a.v||"")+"").replace(bh,function(a){return bf[a]}).replace(bl,function(a){return"&#x"+a.charCodeAt(0).toString(16).toUpperCase()+";"})}var n=fQ(d.cellXfs,a,d);h["ss:StyleID"]="s"+(21+n),h["ss:Index"]=g.c+1;var o=null!=a.v?m:"",p="z"==a.t?"":'<Data ss:Type="'+l+'">'+o+"</Data>";return(a.c||[]).length>0&&(p+=a.c.map(function(a){var b=bE("ss:Data",(a.t||"").replace(/(\r\n|[\r\n])/g,"&#10;"),{xmlns:"http://www.w3.org/TR/REC-html40"});return bE("Comment",b,{"ss:Author":a.a})}).join("")),bE("Cell",p,h)}(p,o,a,b,0,0,n))}}k.push("</Row>"),k.length>2&&h.push(k.join(""))}return h.join("")}(f,b,0,0):"").length>0&&d.push("<Table>"+g+"</Table>"),d.push(function(a,b,c,d){if(!a)return"";var e=[];if(a["!margins"]&&(e.push("<PageSetup>"),a["!margins"].header&&e.push(bE("Header",null,{"x:Margin":a["!margins"].header})),a["!margins"].footer&&e.push(bE("Footer",null,{"x:Margin":a["!margins"].footer})),e.push(bE("PageMargins",null,{"x:Bottom":a["!margins"].bottom||"0.75","x:Left":a["!margins"].left||"0.7","x:Right":a["!margins"].right||"0.7","x:Top":a["!margins"].top||"0.75"})),e.push("</PageSetup>")),d&&d.Workbook&&d.Workbook.Sheets&&d.Workbook.Sheets[c])if(d.Workbook.Sheets[c].Hidden)e.push(bE("Visible",1==d.Workbook.Sheets[c].Hidden?"SheetHidden":"SheetVeryHidden",{}));else{for(var f=0;f<c&&(!d.Workbook.Sheets[f]||d.Workbook.Sheets[f].Hidden);++f);f==c&&e.push("<Selected/>")}return(((((d||{}).Workbook||{}).Views||[])[0]||{}).RTL&&e.push("<DisplayRightToLeft/>"),a["!protect"]&&(e.push(bC("ProtectContents","True")),a["!protect"].objects&&e.push(bC("ProtectObjects","True")),a["!protect"].scenarios&&e.push(bC("ProtectScenarios","True")),null==a["!protect"].selectLockedCells||a["!protect"].selectLockedCells?null==a["!protect"].selectUnlockedCells||a["!protect"].selectUnlockedCells||e.push(bC("EnableSelection","UnlockedCells")):e.push(bC("EnableSelection","NoSelection")),[["formatCells","AllowFormatCells"],["formatColumns","AllowSizeCols"],["formatRows","AllowSizeRows"],["insertColumns","AllowInsertCols"],["insertRows","AllowInsertRows"],["insertHyperlinks","AllowInsertHyperlinks"],["deleteColumns","AllowDeleteCols"],["deleteRows","AllowDeleteRows"],["sort","AllowSort"],["autoFilter","AllowFilter"],["pivotTables","AllowUsePivotTables"]].forEach(function(b){a["!protect"][b[0]]&&e.push("<"+b[1]+"/>")})),0==e.length)?"":bE("WorksheetOptions",e.join(""),{xmlns:bK.x})}(f,0,a,c)),d.join("")}(n,b,a),{"ss:Name":bj(a.SheetNames[n])}));return m[2]=(l=['<Style ss:ID="Default" ss:Name="Normal"><NumberFormat/></Style>'],b.cellXfs.forEach(function(a,b){var c=[];c.push(bE("NumberFormat",null,{"ss:Format":bj(U[a.numFmtId])})),l.push(bE("Style",c.join(""),{"ss:ID":"s"+(21+b)}))}),bE("Styles",l.join(""))),m[3]=function(a){if(!((a||{}).Workbook||{}).Names)return"";for(var b=a.Workbook.Names,c=[],d=0;d<b.length;++d){var e=b[d];null==e.Sheet&&(e.Name.match(/^_xlfn\./)||c.push(gp(e)))}return bE("Names",c.join(""))}(a,b),a6+bE("Workbook",m.join(""),{xmlns:bK.ss,"xmlns:o":bK.o,"xmlns:x":bK.x,"xmlns:ss":bK.ss,"xmlns:dt":bK.dt,"xmlns:html":bK.html})}(b,g),g);case"slk":case"sylk":return g5(ef.from_sheet(b.Sheets[b.SheetNames[i]],g),g);case"htm":case"html":return g5(function(a,b){var c=b||{},d=null!=c.header?c.header:'<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>',e=null!=c.footer?c.footer:"</body></html>",f=[d],g=cv(a["!ref"]);c.dense=Array.isArray(a),f.push("<table"+(c&&c.id?' id="'+c.id+'"':"")+">");for(var h=g.s.r;h<=g.e.r;++h)f.push(function(a,b,c,d){for(var e=a["!merges"]||[],f=[],g=b.s.c;g<=b.e.c;++g){for(var h=0,i=0,j=0;j<e.length;++j)if(!(e[j].s.r>c)&&!(e[j].s.c>g)&&!(e[j].e.r<c)&&!(e[j].e.c<g)){if(e[j].s.r<c||e[j].s.c<g){h=-1;break}h=e[j].e.r-e[j].s.r+1,i=e[j].e.c-e[j].s.c+1;break}if(!(h<0)){var k=cu({r:c,c:g}),l=d.dense?(a[c]||[])[g]:a[k],m=l&&null!=l.v&&(l.h||bm(l.w||(cz(l),l.w)||""))||"",n={};h>1&&(n.rowspan=h),i>1&&(n.colspan=i),d.editable?m='<span contenteditable="true">'+m+"</span>":l&&(n["data-t"]=l&&l.t||"z",null!=l.v&&(n["data-v"]=l.v),null!=l.z&&(n["data-z"]=l.z),l.l&&"#"!=(l.l.Target||"#").charAt(0)&&(m='<a href="'+l.l.Target+'">'+m+"</a>")),n.id=(d.id||"sjs")+"-"+k,f.push(bE("td",m,n))}}return"<tr>"+f.join("")+"</tr>"}(a,g,h,c));return f.push("</table>"+e),f.join("")}(b.Sheets[b.SheetNames[i]],g),g);case"txt":var j=function(a,b){b||(b={}),b.FS="	",b.RS="\n";var c=g9(a,b);if(void 0===e||"string"==b.type)return c;var d=e.utils.encode(1200,c,"str");return String.fromCharCode(255)+String.fromCharCode(254)+d}(b.Sheets[b.SheetNames[i]],g);switch(g.type){case"base64":return z(j);case"binary":case"string":return j;case"file":return aB(g.file,j,"binary");case"buffer":if(B)return C(j,"binary");return j.split("").map(function(a){return a.charCodeAt(0)})}throw Error("Unrecognized type "+g.type);case"csv":return g5(g9(b.Sheets[b.SheetNames[i]],g),g,"\uFEFF");case"dif":return g5(eg.from_sheet(b.Sheets[b.SheetNames[i]],g),g);case"dbf":return g6(ee.from_sheet(b.Sheets[b.SheetNames[i]],g),g);case"prn":return g5(ei.from_sheet(b.Sheets[b.SheetNames[i]],g),g);case"rtf":return g5(eE.from_sheet(b.Sheets[b.SheetNames[i]],g),g);case"eth":return g5(eh.from_sheet(b.Sheets[b.SheetNames[i]],g),g);case"fods":return g5(gF(b,g),g);case"wk1":return g6(ej.sheet_to_wk1(b.Sheets[b.SheetNames[i]],g),g);case"wk3":return g6(ej.book_to_wk3(b,g),g);case"biff2":g.biff||(g.biff=2);case"biff3":g.biff||(g.biff=3);case"biff4":return g.biff||(g.biff=4),g6(gz(b,g),g);case"biff5":g.biff||(g.biff=5);case"biff8":case"xla":case"xls":return g.biff||(g.biff=8),g4(function(a,b){var c,d=b||{},e=aA.utils.cfb_new({root:"R"}),f="/Workbook";switch(d.bookType||"xls"){case"xls":d.bookType="biff8";case"xla":d.bookType||(d.bookType="xla");case"biff8":f="/Workbook",d.biff=8;break;case"biff5":f="/Book",d.biff=5;break;default:throw Error("invalid type "+d.bookType+" for XLS CFB")}return aA.utils.cfb_add(e,f,gz(a,d)),8==d.biff&&(a.Props||a.Custprops)&&function(a,b){var c,d=[],e=[],f=[],g=0,h=aD(cV,"n"),i=aD(cW,"n");if(a.Props)for(g=0,c=aC(a.Props);g<c.length;++g)(Object.prototype.hasOwnProperty.call(h,c[g])?d:Object.prototype.hasOwnProperty.call(i,c[g])?e:f).push([c[g],a.Props[c[g]]]);if(a.Custprops)for(g=0,c=aC(a.Custprops);g<c.length;++g)Object.prototype.hasOwnProperty.call(a.Props||{},c[g])||(Object.prototype.hasOwnProperty.call(h,c[g])?d:Object.prototype.hasOwnProperty.call(i,c[g])?e:f).push([c[g],a.Custprops[c[g]]]);var j=[];for(g=0;g<f.length;++g)dy.indexOf(f[g][0])>-1||dh.indexOf(f[g][0])>-1||null!=f[g][1]&&j.push(f[g]);e.length&&aA.utils.cfb_add(b,"/\x05SummaryInformation",dB(e,gt.SI,i,cW)),(d.length||j.length)&&aA.utils.cfb_add(b,"/\x05DocumentSummaryInformation",dB(d,gt.DSI,h,cV,j.length?j:null,gt.UDI))}(a,e),8==d.biff&&a.vbaraw&&(c=aA.read(a.vbaraw,{type:"string"==typeof a.vbaraw?"binary":"buffer"})).FullPaths.forEach(function(a,b){if(0!=b){var d=a.replace(/[^\/]*[\/]/,"/_VBA_PROJECT_CUR/");"/"!==d.slice(-1)&&aA.utils.cfb_add(e,d,c.FileIndex[b].content)}}),e}(b,d=g||{}),d);case"xlsx":case"xlsm":case"xlam":case"xlsb":case"numbers":case"ods":return function(a,b){var c={},d=B?"nodebuffer":"undefined"!=typeof Uint8Array?"array":"string";if(b.compression&&(c.compression="DEFLATE"),b.password)c.type=d;else switch(b.type){case"base64":c.type="base64";break;case"binary":c.type="string";break;case"string":throw Error("'string' output type invalid for '"+b.bookType+"' files");case"buffer":case"file":c.type=d;break;default:throw Error("Unrecognized type "+b.type)}var e=a.FullPaths?aA.write(a,{fileType:"zip",type:{nodebuffer:"buffer",string:"binary"}[c.type]||c.type,compression:!!b.compression}):a.generate(c);if("undefined"!=typeof Deno&&"string"==typeof e){if("binary"==b.type||"base64"==b.type)return e;e=new Uint8Array(G(e))}return b.password&&"undefined"!=typeof encrypt_agile?g4(encrypt_agile(e,b.password),b):"file"===b.type?aB(b.file,e):"string"==b.type?bt(e):e}("ods"==(f=aR(g||{})).bookType?gF(b,f):"numbers"==f.bookType?function(a,b){if(!b||!b.numbers)throw Error("Must pass a `numbers` option -- check the README");var c,d=a.Sheets[a.SheetNames[0]];a.SheetNames.length>1&&console.error("The Numbers writer currently writes only the first table");var e=cv(d["!ref"]);e.s.r=e.s.c=0;var f=!1;e.e.c>9&&(f=!0,e.e.c=9),e.e.r>49&&(f=!0,e.e.r=49),f&&console.error("The Numbers writer is currently limited to ".concat(cw(e)));var g=g7(d,{range:e,header:1}),h=["~Sh33tJ5~"];g.forEach(function(a){return a.forEach(function(a){"string"==typeof a&&h.push(a)})});var i={},j=[],k=aA.read(b.numbers,{type:"base64"});k.FileIndex.map(function(a,b){return[a,k.FullPaths[b]]}).forEach(function(a){var b=a[0],c=a[1];2==b.type&&b.name.match(/\.iwa/)&&gQ(gS(b.content)).forEach(function(a){j.push(a.id),i[a.id]={deps:[],location:c,type:gM(a.messages[0].meta[1][0].data)}})}),j.sort(function(a,b){return a-b});var l=j.filter(function(a){return a>1}).map(function(a){return[a,gL(a)]});k.FileIndex.map(function(a,b){return[a,k.FullPaths[b]]}).forEach(function(a){var b=a[0];a[1],b.name.match(/\.iwa/)&&gQ(gS(b.content)).forEach(function(a){a.messages.forEach(function(b){l.forEach(function(b){a.messages.some(function(a){return 11006!=gM(a.meta[1][0].data)&&function(a,b){a:for(var c=0;c<=a.length-b.length;++c){for(var d=0;d<b.length;++d)if(a[c+d]!=b[d])continue a;return!0}return!1}(a.data,b[1])})&&i[b[0]].deps.push(a.id)})})})});for(var m=aA.find(k,i[1].location),n=gQ(gS(m.content)),o=0;o<n.length;++o){var p=n[o];1==p.id&&(c=p)}var q=gW(gN(c.messages[0].data)[1][0].data);for(o=0,n=gQ(gS((m=aA.find(k,i[q].location)).content));o<n.length;++o)(p=n[o]).id==q&&(c=p);for(o=0,q=gW(gN(c.messages[0].data)[2][0].data),n=gQ(gS((m=aA.find(k,i[q].location)).content));o<n.length;++o)(p=n[o]).id==q&&(c=p);for(o=0,q=gW(gN(c.messages[0].data)[2][0].data),n=gQ(gS((m=aA.find(k,i[q].location)).content));o<n.length;++o)(p=n[o]).id==q&&(c=p);var r=gN(c.messages[0].data);r[6][0].data=gL(e.e.r+1),r[7][0].data=gL(e.e.c+1);for(var s=gW(r[46][0].data),t=aA.find(k,i[s].location),u=gQ(gS(t.content)),v=0;v<u.length&&u[v].id!=s;++v);if(u[v].id!=s)throw"Bad ColumnRowUIDMapArchive";var w=gN(u[v].messages[0].data);w[1]=[],w[2]=[],w[3]=[];for(var x=0;x<=e.e.c;++x){var y=[];y[1]=y[2]=[{type:0,data:gL(x+420690)}],w[1].push({type:2,data:gO(y)}),w[2].push({type:0,data:gL(x)}),w[3].push({type:0,data:gL(x)})}w[4]=[],w[5]=[],w[6]=[];for(var z=0;z<=e.e.r;++z)(y=[])[1]=y[2]=[{type:0,data:gL(z+726270)}],w[4].push({type:2,data:gO(y)}),w[5].push({type:0,data:gL(z)}),w[6].push({type:0,data:gL(z)});u[v].messages[0].data=gO(w),t.content=gT(gR(u)),t.size=t.content.length,delete r[46];var A=gN(r[4][0].data);A[7][0].data=gL(e.e.r+1);var B=gW(gN(A[1][0].data)[2][0].data);if((u=gQ(gS((t=aA.find(k,i[B].location)).content)))[0].id!=B)throw"Bad HeaderStorageBucket";var C=gN(u[0].messages[0].data);for(z=0;z<g.length;++z){var D=gN(C[2][0].data);D[1][0].data=gL(z),D[4][0].data=gL(g[z].length),C[2][z]={type:C[2][0].type,data:gO(D)}}u[0].messages[0].data=gO(C),t.content=gT(gR(u)),t.size=t.content.length;var E=gW(A[2][0].data);if((u=gQ(gS((t=aA.find(k,i[E].location)).content)))[0].id!=E)throw"Bad HeaderStorageBucket";for(x=0,C=gN(u[0].messages[0].data);x<=e.e.c;++x)(D=gN(C[2][0].data))[1][0].data=gL(x),D[4][0].data=gL(e.e.r+1),C[2][x]={type:C[2][0].type,data:gO(D)};u[0].messages[0].data=gO(C),t.content=gT(gR(u)),t.size=t.content.length;var G=gW(A[4][0].data);!function(){for(var a,b=aA.find(k,i[G].location),c=gQ(gS(b.content)),d=0;d<c.length;++d){var e=c[d];e.id==G&&(a=e)}var f=gN(a.messages[0].data);f[3]=[];var g=[];h.forEach(function(a,b){g[1]=[{type:0,data:gL(b)}],g[2]=[{type:0,data:gL(1)}],g[3]=[{type:2,data:"undefined"!=typeof TextEncoder?new TextEncoder().encode(a):F(bu(a))}],f[3].push({type:2,data:gO(g)})}),a.messages[0].data=gO(f),b.content=gT(gR(c)),b.size=b.content.length}();var H=gN(A[3][0].data),I=H[1][0];delete H[2];var J=gN(I.data),K=gW(J[2][0].data);!function(){for(var a,b=aA.find(k,i[K].location),c=gQ(gS(b.content)),d=0;d<c.length;++d){var f=c[d];f.id==K&&(a=f)}var j=gN(a.messages[0].data);delete j[6],delete H[7];var l=new Uint8Array(j[5][0].data);j[5]=[];for(var m=0,n=0;n<=e.e.r;++n){var o=gN(l);m+=function(a,b,c){if(!(null==(d=a[6])?void 0:d[0])||!(null==(e=a[7])?void 0:e[0]))throw"Mutation only works on post-BNC storages!";if((null==(g=null==(f=a[8])?void 0:f[0])?void 0:g.data)&&gM(a[8][0].data)>0)throw"Math only works with normal offsets";for(var d,e,f,g,h,i,j=0,k=gG(a[7][0].data),l=0,m=[],n=gG(a[4][0].data),o=0,p=[],q=0;q<b.length;++q){if(null==b[q]){k.setUint16(2*q,65535,!0),n.setUint16(2*q,65535);continue}switch(k.setUint16(2*q,l,!0),n.setUint16(2*q,o,!0),typeof b[q]){case"string":h=gU({t:"s",v:b[q]},c),i=gV({t:"s",v:b[q]},c);break;case"number":h=gU({t:"n",v:b[q]},c),i=gV({t:"n",v:b[q]},c);break;case"boolean":h=gU({t:"b",v:b[q]},c),i=gV({t:"b",v:b[q]},c);break;default:throw Error("Unsupported value "+b[q])}m.push(h),l+=h.length,p.push(i),o+=i.length,++j}for(a[2][0].data=gL(j);q<a[7][0].data.length/2;++q)k.setUint16(2*q,65535,!0),n.setUint16(2*q,65535,!0);return a[6][0].data=gI(m),a[3][0].data=gI(p),j}(o,g[n],h),o[1][0].data=gL(n),j[5].push({data:gO(o),type:2})}j[1]=[{type:0,data:gL(e.e.c+1)}],j[2]=[{type:0,data:gL(e.e.r+1)}],j[3]=[{type:0,data:gL(m)}],j[4]=[{type:0,data:gL(e.e.r+1)}],a.messages[0].data=gO(j),b.content=gT(gR(c)),b.size=b.content.length}(),I.data=gO(J),A[3][0].data=gO(H),r[4][0].data=gO(A),c.messages[0].data=gO(r);var L=gT(gR(n));return m.content=L,m.size=m.content.length,k}(b,f):"xlsb"==f.bookType?function(a,b){e8=1024,a&&!a.SSF&&(a.SSF=aR(U)),a&&a.SSF&&(aw(),av(a.SSF),b.revssf=aF(a.SSF),b.revssf[a.SSF[65535]]=0,b.ssf=a.SSF),b.rels={},b.wbrels={},b.Strings=[],b.Strings.Count=0,b.Strings.Unique=0,fM?b.revStrings=new Map:(b.revStrings={},b.revStrings.foo=[],delete b.revStrings.foo);var c,d,e,f,g,h,i,j="xlsb"==b.bookType?"bin":"xml",k=fc.indexOf(b.bookType)>-1,m=c2();g_(b=b||{});var n=a3(),o="",p=0;if(b.cellXfs=[],fQ(b.cellXfs,{},{revssf:{General:0}}),a.Props||(a.Props={}),a2(n,o="docProps/core.xml",df(a.Props,b)),m.coreprops.push(o),c8(b.rels,2,o,c4.CORE_PROPS),o="docProps/app.xml",a.Props&&a.Props.SheetNames);else if(a.Workbook&&a.Workbook.Sheets){for(var q=[],r=0;r<a.SheetNames.length;++r)2!=(a.Workbook.Sheets[r]||{}).Hidden&&q.push(a.SheetNames[r]);a.Props.SheetNames=q}else a.Props.SheetNames=a.SheetNames;for(a.Props.Worksheets=a.Props.SheetNames.length,a2(n,o,dj(a.Props,b)),m.extprops.push(o),c8(b.rels,3,o,c4.EXT_PROPS),a.Custprops!==a.Props&&aC(a.Custprops||{}).length>0&&(a2(n,o="docProps/custom.xml",dl(a.Custprops,b)),m.custprops.push(o),c8(b.rels,4,o,c4.CUST_PROPS)),p=1;p<=a.SheetNames.length;++p){var s={"!id":{}},t=a.Sheets[a.SheetNames[p-1]];if((t||{})["!type"],a2(n,o="xl/worksheets/sheet"+p+"."+j,(u=p-1,v=o,w=b,(".bin"===v.slice(-4)?function(a,b,c,d){var e,f,g,h,i,j,k,l,m,n=cj(),o=c.SheetNames[a],p=c.Sheets[o]||{},q=o;try{c&&c.Workbook&&(q=c.Workbook.Sheets[a].CodeName||q)}catch(a){}var r=cx(p["!ref"]||"A1");if(r.e.c>16383||r.e.r>1048575){if(b.WTF)throw Error("Range "+(p["!ref"]||"A1")+" exceeds format limit A1:XFD1048576");r.e.c=Math.min(r.e.c,16383),r.e.r=Math.min(r.e.c,1048575)}p["!links"]=[],p["!comments"]=[],ck(n,129),(c.vbaraw||p["!outline"])&&ck(n,147,function(a,b,c){null==c&&(c=ch(84+4*a.length));var d=192;b&&(b.above&&(d&=-65),b.left&&(d&=-129)),c.write_shift(1,d);for(var e=1;e<3;++e)c.write_shift(1,0);return cS({auto:1},c),c.write_shift(-4,-1),c.write_shift(-4,-1),cE(a,c),c.slice(0,c.l)}(q,p["!outline"])),ck(n,148,cP(r)),e=c.Workbook,ck(n,133),ck(n,137,(null==f&&(f=ch(30)),g=924,(((e||{}).Views||[])[0]||{}).RTL&&(g|=32),f.write_shift(2,g),f.write_shift(4,0),f.write_shift(4,0),f.write_shift(4,0),f.write_shift(1,0),f.write_shift(1,0),f.write_shift(2,0),f.write_shift(2,100),f.write_shift(2,0),f.write_shift(2,0),f.write_shift(2,0),f.write_shift(4,0),f)),ck(n,138),ck(n,134),p&&p["!cols"]&&(ck(n,390),p["!cols"].forEach(function(a,b){var c,d,e;a&&ck(n,60,(null==c&&(c=ch(18)),d=fO(b,a),c.write_shift(-4,b),c.write_shift(-4,b),c.write_shift(4,256*(d.width||10)),c.write_shift(4,0),e=0,a.hidden&&(e|=1),"number"==typeof d.width&&(e|=2),a.level&&(e|=a.level<<8),c.write_shift(2,e),c))}),ck(n,391)),function(a,b,c,d){var e,f=cx(b["!ref"]||"A1"),g="",h=[];ck(a,145);var i=Array.isArray(b),j=f.e.r;b["!rows"]&&(j=Math.max(f.e.r,b["!rows"].length-1));for(var k=f.s.r;k<=j;++k){g=cq(k),function(a,b,c,d){var e=function(a,b,c){var d=ch(145),e=(c["!rows"]||[])[a]||{};d.write_shift(4,a),d.write_shift(4,0);var f=320;e.hpx?f=20*eO(e.hpx):e.hpt&&(f=20*e.hpt),d.write_shift(2,f),d.write_shift(1,0);var g=0;e.level&&(g|=e.level),e.hidden&&(g|=16),(e.hpx||e.hpt)&&(g|=32),d.write_shift(1,g),d.write_shift(1,0);var h=0,i=d.l;d.l+=4;for(var j={r:a,c:0},k=0;k<16;++k)if(!(b.s.c>k+1<<10)&&!(b.e.c<k<<10)){for(var l=-1,m=-1,n=k<<10;n<k+1<<10;++n)j.c=n,(Array.isArray(c)?(c[j.r]||[])[j.c]:c[cu(j)])&&(l<0&&(l=n),m=n);l<0||(++h,d.write_shift(4,l),d.write_shift(4,m))}var o=d.l;return d.l=i,d.write_shift(4,h),d.l=o,d.length>d.l?d.slice(0,d.l):d}(d,c,b);(e.length>17||(b["!rows"]||[])[d])&&ck(a,0,e)}(a,b,f,k);var l=!1;if(k<=f.e.r)for(var m=f.s.c;m<=f.e.c;++m){k===f.s.r&&(h[m]=cs(m)),e=h[m]+g;var n=i?(b[k]||[])[m]:b[e];if(!n){l=!1;continue}l=function(a,b,c,d,e,f,g){if(void 0===b.v)return!1;var h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F="";switch(b.t){case"b":F=b.v?"1":"0";break;case"d":(b=aR(b)).z=b.z||U[14],b.v=aH(aP(b.v)),b.t="n";break;case"n":case"e":F=""+b.v;break;default:F=b.v}var G={r:c,c:d};switch(G.s=fQ(e.cellXfs,b,e),b.l&&f["!links"].push([cu(G),b.l]),b.c&&f["!comments"].push([cu(G),b.c]),b.t){case"s":case"str":return e.bookSST?(F=fN(e.Strings,b.v,e.revStrings),G.t="s",G.v=F,g)?ck(a,18,(null==h&&(h=ch(8)),cJ(G,h),h.write_shift(4,G.v),h)):ck(a,7,(null==i&&(i=ch(12)),cH(G,i),i.write_shift(4,G.v),i)):(G.t="str",g)?ck(a,17,(j=b,null==k&&(k=ch(8+4*j.v.length)),cJ(G,k),cE(j.v,k),k.length>k.l?k.slice(0,k.l):k)):ck(a,6,(l=b,null==m&&(m=ch(12+4*l.v.length)),cH(G,m),cE(l.v,m),m.length>m.l?m.slice(0,m.l):m)),!0;case"n":return b.v==(0|b.v)&&b.v>-1e3&&b.v<1e3?g?ck(a,13,(n=b,null==o&&(o=ch(8)),cJ(G,o),cN(n.v,o),o)):ck(a,2,(p=b,null==q&&(q=ch(12)),cH(G,q),cN(p.v,q),q)):g?ck(a,16,(r=b,null==s&&(s=ch(12)),cJ(G,s),cR(r.v,s),s)):ck(a,5,(t=b,null==u&&(u=ch(16)),cH(G,u),cR(t.v,u),u)),!0;case"b":return(G.t="b",g)?ck(a,15,(v=b,null==w&&(w=ch(5)),cJ(G,w),w.write_shift(1,+!!v.v),w)):ck(a,4,(x=b,null==y&&(y=ch(9)),cH(G,y),y.write_shift(1,+!!x.v),y)),!0;case"e":return(G.t="e",g)?ck(a,14,(z=b,null==A&&(A=ch(8)),cJ(G,A),A.write_shift(1,z.v),A.write_shift(2,0),A.write_shift(1,0),A)):ck(a,3,(B=b,null==C&&(C=ch(9)),cH(G,C),C.write_shift(1,B.v),C)),!0}return g?ck(a,12,(null==D&&(D=ch(4)),cJ(G,D))):ck(a,1,(null==E&&(E=ch(8)),cH(G,E))),!0}(a,n,k,m,d,b,l)}}ck(a,146)}(n,p,0,b,c);p["!protect"]&&ck(n,535,(h=p["!protect"],null==i&&(i=ch(66)),i.write_shift(2,h.password?eA(h.password):0),i.write_shift(4,1),[["objects",!1],["scenarios",!1],["formatCells",!0],["formatColumns",!0],["formatRows",!0],["insertColumns",!0],["insertRows",!0],["insertHyperlinks",!0],["deleteColumns",!0],["deleteRows",!0],["selectLockedCells",!1],["sort",!0],["autoFilter",!0],["pivotTables",!0],["selectUnlockedCells",!1]].forEach(function(a){a[1]?i.write_shift(4,+(null!=h[a[0]]&&!h[a[0]])):i.write_shift(4,null!=h[a[0]]&&h[a[0]]?0:1)}),i)),!function(a,b,c,d){if(b["!autofilter"]){var e=b["!autofilter"],f="string"==typeof e.ref?e.ref:cw(e.ref);c.Workbook||(c.Workbook={Sheets:[]}),c.Workbook.Names||(c.Workbook.Names=[]);var g=c.Workbook.Names,h=cv(f);h.s.r==h.e.r&&(h.e.r=cv(b["!ref"]).e.r,f=cw(h));for(var i=0;i<g.length;++i){var j=g[i];if("_xlnm._FilterDatabase"==j.Name&&j.Sheet==d){j.Ref="'"+c.SheetNames[d]+"'!"+f;break}}i==g.length&&g.push({Name:"_xlnm._FilterDatabase",Sheet:d,Ref:"'"+c.SheetNames[d]+"'!"+f}),ck(a,161,cP(cx(f))),ck(a,162)}}(n,p,c,a);p&&p["!merges"]&&(ck(n,177,(j=p["!merges"].length,null==k&&(k=ch(4)),k.write_shift(4,j),k)),p["!merges"].forEach(function(a){ck(n,176,cP(a))}),ck(n,178)),p["!links"].forEach(function(a){if(a[1].Target){var b,c,e=c8(d,-1,a[1].Target.replace(/#.*$/,""),c4.HLINK);ck(n,494,(b=ch(50+4*(a[1].Target.length+(a[1].Tooltip||"").length)),cP({s:ct(a[0]),e:ct(a[0])},b),cL("rId"+e,b),cE((-1==(c=a[1].Target.indexOf("#"))?"":a[1].Target.slice(c+1))||"",b),cE(a[1].Tooltip||"",b),cE("",b),b.slice(0,b.l)))}}),delete p["!links"],p["!margins"]&&ck(n,476,(l=p["!margins"],null==m&&(m=ch(48)),fP(l),f7.forEach(function(a){cR(l[a],m)}),m)),(!b||b.ignoreEC||void 0==b.ignoreEC)&&function(a,b){if(b&&b["!ref"]){var c,d;ck(a,648),ck(a,649,(c=cx(b["!ref"]),(d=ch(24)).write_shift(4,4),d.write_shift(4,1),cP(c,d),d)),ck(a,650)}}(n,p);if(p["!comments"].length>0){var s=c8(d,-1,"../drawings/vmlDrawing"+(a+1)+".vml",c4.VML);ck(n,551,cL("rId"+s)),p["!legacy"]=s}return ck(n,130),n.end()}:f5)(u,w,a,s))),m.sheets.push(o),c8(b.wbrels,-1,"worksheets/sheet"+p+"."+j,c4.WS[0]),t){var u,v,w,x,y,z=t["!comments"],A=!1,B="";z&&z.length>0&&(a2(n,B="xl/comments"+p+"."+j,(x=B,y=b,(".bin"===x.slice(-4)?function(a){var b=cj(),c=[];return ck(b,628),ck(b,630),a.forEach(function(a){a[1].forEach(function(a){c.indexOf(a.a)>-1||(c.push(a.a.slice(0,54)),ck(b,632,cE(a.a.slice(0,54))))})}),ck(b,631),ck(b,633),a.forEach(function(a){a[1].forEach(function(d){var e,f,g,h,i,j;d.iauthor=c.indexOf(d.a),ck(b,635,(e=[{s:ct(a[0]),e:ct(a[0])},d],null==f&&(f=ch(36)),f.write_shift(4,e[1].iauthor),cP(e[0],f),f.write_shift(4,0),f.write_shift(4,0),f.write_shift(4,0),f.write_shift(4,0),f)),d.t&&d.t.length>0&&ck(b,637,(h=!1,null==g&&(h=!0,g=ch(23+4*d.t.length)),g.write_shift(1,1),cE(d.t,g),g.write_shift(4,1),i={ich:0,ifnt:0},(j=g)||(j=ch(4)),j.write_shift(2,i.ich||0),j.write_shift(2,i.ifnt||0),h?g.slice(0,g.l):g)),ck(b,636),delete d.iauthor})}),ck(b,634),ck(b,629),b.end()}:fb)(z,y))),m.comments.push(B),c8(s,-1,"../comments"+p+"."+j,c4.CMNT),A=!0),t["!legacy"]&&A&&a2(n,"xl/drawings/vmlDrawing"+p+".vml",e9(p,t["!comments"])),delete t["!comments"],delete t["!legacy"]}s["!id"].rId1&&a2(n,c5(o),c7(s))}return null!=b.Strings&&b.Strings.length>0&&(a2(n,o="xl/sharedStrings."+j,(c=b.Strings,d=o,e=b,(".bin"===d.slice(-4)?function(a){var b,c=cj();ck(c,159,(b||(b=ch(8)),b.write_shift(4,a.Count),b.write_shift(4,a.Unique),b));for(var d=0;d<a.length;++d)ck(c,19,ev(a[d]));return ck(c,160),c.end()}:eu)(c,e))),m.strs.push(o),c8(b.wbrels,-1,"sharedStrings."+j,c4.SST)),a2(n,o="xl/workbook."+j,(f=o,g=b,(".bin"===f.slice(-4)?function(a,b){var c=cj();ck(c,131),ck(c,128,function(a,b){b||(b=ch(127));for(var c=0;4!=c;++c)b.write_shift(4,0);return cE("SheetJS",b),cE(l.version,b),cE(l.version,b),cE("7262",b),b.length>b.l?b.slice(0,b.l):b}()),ck(c,153,(e=a.Workbook&&a.Workbook.WBProps||null,f||(f=ch(72)),g=0,e&&e.filterPrivacy&&(g|=8),f.write_shift(4,g),f.write_shift(4,0),cE(e&&e.CodeName||"ThisWorkbook",f),f.slice(0,f.l))),function(a,b){if(b.Workbook&&b.Workbook.Sheets){for(var c,d,e=b.Workbook.Sheets,f=0,g=-1,h=-1;f<e.length;++f)e[f]&&(e[f].Hidden||-1!=g)?1==e[f].Hidden&&-1==h&&(h=f):g=f;h>g||(ck(a,135),ck(a,158,(c=g,d||(d=ch(29)),d.write_shift(-4,0),d.write_shift(-4,460),d.write_shift(4,28800),d.write_shift(4,17600),d.write_shift(4,500),d.write_shift(4,c),d.write_shift(4,c),d.write_shift(1,120),d.length>d.l?d.slice(0,d.l):d)),ck(a,136))}}(c,a,b);ck(c,143);for(var d=0;d!=a.SheetNames.length;++d){var e,f,g,h={Hidden:a.Workbook&&a.Workbook.Sheets&&a.Workbook.Sheets[d]&&a.Workbook.Sheets[d].Hidden||0,iTabID:d+1,strRelID:"rId"+(d+1),name:a.SheetNames[d]},i=void 0;ck(c,156,(i||(i=ch(127)),i.write_shift(4,h.Hidden),i.write_shift(4,h.iTabID),cL(h.strRelID,i),cE(h.name.slice(0,31),i),i.length>i.l?i.slice(0,i.l):i))}return ck(c,144),ck(c,132),c.end()}:gi)(a,g))),m.workbooks.push(o),c8(b.rels,1,o,c4.WB),a2(n,o="xl/theme/theme1.xml",e6(a.Themes,b)),m.themes.push(o),c8(b.wbrels,-1,"theme/theme1.xml",c4.THEME),a2(n,o="xl/styles."+j,(h=o,i=b,(".bin"===h.slice(-4)?function(a,b){var c,d,e,f,g,h=cj();return ck(h,278),function(a,b){if(b){var c=0;[[5,8],[23,26],[41,44],[50,392]].forEach(function(a){for(var d=a[0];d<=a[1];++d)null!=b[d]&&++c}),0!=c&&(ck(a,615,cC(c)),[[5,8],[23,26],[41,44],[50,392]].forEach(function(c){for(var d=c[0];d<=c[1];++d)null!=b[d]&&ck(a,44,function(a,b,c){c||(c=ch(6+4*b.length)),c.write_shift(2,a),cE(b,c);var d=c.length>c.l?c.slice(0,c.l):c;return null==c.l&&(c.l=c.length),d}(d,b[d]))}),ck(a,616))}}(h,a.SSF),function(a){var b,c,d,e,f,g;ck(a,611,cC(1)),ck(a,43,(b={sz:12,color:{theme:1},name:"Calibri",family:2,scheme:"minor"},c||(c=ch(153)),c.write_shift(2,20*b.sz),(d=c)||(d=ch(2)),e=2*!!b.italic|8*!!b.strike|16*!!b.outline|32*!!b.shadow|64*!!b.condense|128*!!b.extend,d.write_shift(1,e),d.write_shift(1,0),c.write_shift(2,b.bold?700:400),f=0,"superscript"==b.vertAlign?f=1:"subscript"==b.vertAlign&&(f=2),c.write_shift(2,f),c.write_shift(1,b.underline||0),c.write_shift(1,b.family||0),c.write_shift(1,b.charset||0),c.write_shift(1,0),cS(b.color,c),g=0,"major"==b.scheme&&(g=1),"minor"==b.scheme&&(g=2),c.write_shift(1,g),cE(b.name,c),c.length>c.l?c.slice(0,c.l):c)),ck(a,612)}(h,a),ck(h,603,cC(2)),ck(h,45,eW({patternType:"none"})),ck(h,45,eW({patternType:"gray125"})),ck(h,604),ck(h,613,cC(1)),ck(h,46,(c||(c=ch(51)),c.write_shift(1,0),eY(null,c),eY(null,c),eY(null,c),eY(null,c),eY(null,c),c.length>c.l?c.slice(0,c.l):c)),ck(h,614),ck(h,626,cC(1)),ck(h,47,eX({numFmtId:0,fontId:0,fillId:0,borderId:0},65535)),ck(h,627),ck(h,617,cC((d=b.cellXfs).length)),d.forEach(function(a){ck(h,47,eX(a,0))}),ck(h,618),ck(h,619,cC(1)),ck(h,48,(e={xfId:0,builtinId:0,name:"Normal"},f||(f=ch(52)),f.write_shift(4,e.xfId),f.write_shift(2,1),f.write_shift(1,+e.builtinId),f.write_shift(1,0),cL(e.name||"",f),f.length>f.l?f.slice(0,f.l):f)),ck(h,620),ck(h,505,cC(0)),ck(h,506),ck(h,508,((g=ch(2052)).write_shift(4,0),cL("TableStyleMedium9",g),cL("PivotStyleMedium4",g),g.length>g.l?g.slice(0,g.l):g)),ck(h,509),ck(h,279),h.end()}:eU)(a,i))),m.styles.push(o),c8(b.wbrels,-1,"styles."+j,c4.STY),a.vbaraw&&k&&(a2(n,o="xl/vbaProject.bin",a.vbaraw),m.vba.push(o),c8(b.wbrels,-1,"vbaProject.bin",c4.VBA)),a2(n,o="xl/metadata."+j,(".bin"===o.slice(-4)?function(){var a,b,c,d,e=cj();return ck(e,332),ck(e,334,cC(1)),ck(e,335,((b=ch(12+2*(a={name:"XLDAPR",version:12e4,flags:0xd06ac0b0}).name.length)).write_shift(4,a.flags),b.write_shift(4,a.version),cE(a.name,b),b.slice(0,b.l))),ck(e,336),ck(e,339,((c=ch(20)).write_shift(4,1),cE("XLDAPR",c),c.slice(0,c.l))),ck(e,52),ck(e,35,cC(514)),ck(e,4096,cC(0)),ck(e,4097,dG(1)),ck(e,36),ck(e,53),ck(e,340),ck(e,337,((d=ch(8)).write_shift(4,1),d.write_shift(4,1),d)),ck(e,51,function(a){var b=ch(4+8*a.length);b.write_shift(4,a.length);for(var c=0;c<a.length;++c)b.write_shift(4,a[c][0]),b.write_shift(4,a[c][1]);return b}([[1,0]])),ck(e,338),ck(e,333),e.end()}:e7)()),m.metadata.push(o),c8(b.wbrels,-1,"metadata."+j,c4.XLMETA),a2(n,"[Content_Types].xml",c3(m,b)),a2(n,"_rels/.rels",c7(b.rels)),a2(n,"xl/_rels/workbook."+j+".rels",c7(b.wbrels)),delete b.revssf,delete b.ssf,n}(b,f):function(a,b){e8=1024,a&&!a.SSF&&(a.SSF=aR(U)),a&&a.SSF&&(aw(),av(a.SSF),b.revssf=aF(a.SSF),b.revssf[a.SSF[65535]]=0,b.ssf=a.SSF),b.rels={},b.wbrels={},b.Strings=[],b.Strings.Count=0,b.Strings.Unique=0,fM?b.revStrings=new Map:(b.revStrings={},b.revStrings.foo=[],delete b.revStrings.foo);var c,d=fc.indexOf(b.bookType)>-1,e=c2();g_(b=b||{});var f=a3(),g="",h=0;if(b.cellXfs=[],fQ(b.cellXfs,{},{revssf:{General:0}}),a.Props||(a.Props={}),a2(f,g="docProps/core.xml",df(a.Props,b)),e.coreprops.push(g),c8(b.rels,2,g,c4.CORE_PROPS),g="docProps/app.xml",a.Props&&a.Props.SheetNames);else if(a.Workbook&&a.Workbook.Sheets){for(var i=[],j=0;j<a.SheetNames.length;++j)2!=(a.Workbook.Sheets[j]||{}).Hidden&&i.push(a.SheetNames[j]);a.Props.SheetNames=i}else a.Props.SheetNames=a.SheetNames;a.Props.Worksheets=a.Props.SheetNames.length,a2(f,g,dj(a.Props,b)),e.extprops.push(g),c8(b.rels,3,g,c4.EXT_PROPS),a.Custprops!==a.Props&&aC(a.Custprops||{}).length>0&&(a2(f,g="docProps/custom.xml",dl(a.Custprops,b)),e.custprops.push(g),c8(b.rels,4,g,c4.CUST_PROPS));var k=["SheetJ5"];for(h=1,b.tcid=0;h<=a.SheetNames.length;++h){var l={"!id":{}},m=a.Sheets[a.SheetNames[h-1]];if((m||{})["!type"],a2(f,g="xl/worksheets/sheet"+h+".xml",f5(h-1,b,a,l)),e.sheets.push(g),c8(b.wbrels,-1,"worksheets/sheet"+h+".xml",c4.WS[0]),m){var n=m["!comments"],o=!1,p="";if(n&&n.length>0){var q=!1;n.forEach(function(a){a[1].forEach(function(a){!0==a.T&&(q=!0)})}),q&&(a2(f,p="xl/threadedComments/threadedComment"+h+".xml",function(a,b,c){var d=[a6,bE("ThreadedComments",null,{xmlns:bI.TCMNT}).replace(/[\/]>/,">")];return a.forEach(function(a){var e="";(a[1]||[]).forEach(function(f,g){if(!f.T)return void delete f.ID;f.a&&-1==b.indexOf(f.a)&&b.push(f.a);var h={ref:a[0],id:"{54EE7951-7262-4200-6969-"+("000000000000"+c.tcid++).slice(-12)+"}"};0==g?e=h.id:h.parentId=e,f.ID=h.id,f.a&&(h.personId="{54EE7950-7262-4200-6969-"+("000000000000"+b.indexOf(f.a)).slice(-12)+"}"),d.push(bE("threadedComment",bC("text",f.t||""),h))})}),d.push("</ThreadedComments>"),d.join("")}(n,k,b)),e.threadedcomments.push(p),c8(l,-1,"../threadedComments/threadedComment"+h+".xml",c4.TCMNT)),a2(f,p="xl/comments"+h+".xml",fb(n,b)),e.comments.push(p),c8(l,-1,"../comments"+h+".xml",c4.CMNT),o=!0}m["!legacy"]&&o&&a2(f,"xl/drawings/vmlDrawing"+h+".vml",e9(h,m["!comments"])),delete m["!comments"],delete m["!legacy"]}l["!id"].rId1&&a2(f,c5(g),c7(l))}return null!=b.Strings&&b.Strings.length>0&&(a2(f,g="xl/sharedStrings.xml",eu(b.Strings,b)),e.strs.push(g),c8(b.wbrels,-1,"sharedStrings.xml",c4.SST)),a2(f,g="xl/workbook.xml",gi(a,b)),e.workbooks.push(g),c8(b.rels,1,g,c4.WB),a2(f,g="xl/theme/theme1.xml",e6(a.Themes,b)),e.themes.push(g),c8(b.wbrels,-1,"theme/theme1.xml",c4.THEME),a2(f,g="xl/styles.xml",eU(a,b)),e.styles.push(g),c8(b.wbrels,-1,"styles.xml",c4.STY),a.vbaraw&&d&&(a2(f,g="xl/vbaProject.bin",a.vbaraw),e.vba.push(g),c8(b.wbrels,-1,"vbaProject.bin",c4.VBA)),a2(f,g="xl/metadata.xml",e7()),e.metadata.push(g),c8(b.wbrels,-1,"metadata.xml",c4.XLMETA),k.length>1&&(a2(f,g="xl/persons/person.xml",(c=[a6,bE("personList",null,{xmlns:bI.TCMNT,"xmlns:x":bJ[0]}).replace(/[\/]>/,">")],k.forEach(function(a,b){c.push(bE("person",null,{displayName:a,id:"{54EE7950-7262-4200-6969-"+("000000000000"+b).slice(-12)+"}",userId:a,providerId:"None"}))}),c.push("</personList>"),c.join(""))),e.people.push(g),c8(b.wbrels,-1,"persons/person.xml",c4.PEOPLE)),a2(f,"[Content_Types].xml",c3(e,b)),a2(f,"_rels/.rels",c7(b.rels)),a2(f,"xl/_rels/workbook.xml.rels",c7(b.wbrels)),delete b.revssf,delete b.ssf,f}(b,f),f);default:throw Error("Unrecognized bookType |"+g.bookType+"|")}}(a,d)}(b,"قالب_استيراد_الموظفين.xlsx")},className:"mb-4 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:"تحميل قالب Excel"}),(0,i.jsx)("div",{className:"mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md",children:(0,i.jsxs)("div",{className:"space-y-1 text-center",children:[(0,i.jsx)("svg",{className:"mx-auto h-12 w-12 text-gray-400",stroke:"currentColor",fill:"none",viewBox:"0 0 48 48",children:(0,i.jsx)("path",{d:"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"})}),(0,i.jsxs)("div",{className:"flex text-sm text-gray-600",children:[(0,i.jsxs)("label",{htmlFor:"file-upload",className:"relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500",children:[(0,i.jsx)("span",{children:"اختر ملف Excel"}),(0,i.jsx)("input",{id:"file-upload",name:"file-upload",type:"file",accept:".xlsx,.xls",className:"sr-only",onChange:a=>{let b=a.target.files?.[0];b&&(c(b),(a=>{let b=new FileReader;b.onload=a=>{try{let b=new Uint8Array(a.target?.result),c=function a(b,c){s();var f,g,h,i,j,k,l,m=c||{};if("undefined"!=typeof ArrayBuffer&&b instanceof ArrayBuffer)return a(new Uint8Array(b),((m=aR(m)).type="array",m));"undefined"!=typeof Uint8Array&&b instanceof Uint8Array&&!m.type&&(m.type="undefined"!=typeof Deno?"buffer":"array");var n=b,o=[0,0,0,0],p=!1;if(m.cellStyles&&(m.cellNF=!0,m.sheetStubs=!0),fL={},m.dateNF&&(fL.dateNF=m.dateNF),m.type||(m.type=B&&Buffer.isBuffer(b)?"buffer":"base64"),"file"==m.type&&(m.type=B?"buffer":"binary",n=function(a){if(void 0!==d)return d.readFileSync(a);if("undefined"!=typeof Deno)return Deno.readFileSync(a);if("undefined"!=typeof $&&"undefined"!=typeof File&&"undefined"!=typeof Folder)try{var b=File(a);b.open("r"),b.encoding="binary";var c=b.read();return b.close(),c}catch(a){if(!a.message||!a.message.match(/onstruct/))throw a}throw Error("Cannot access file "+a)}(b),"undefined"==typeof Uint8Array||B||(m.type="array")),"string"==m.type&&(p=!0,m.type="binary",m.codepage=65001,n=b.match(/[^\x00-\x7F]/)?bu(b):b),"array"==m.type&&"undefined"!=typeof Uint8Array&&b instanceof Uint8Array&&"undefined"!=typeof ArrayBuffer){var q=new Uint8Array(new ArrayBuffer(3));if(q.foo="bar",!q.foo)return(m=aR(m)).type="array",a(I(n),m)}switch((o=g1(n,m))[0]){case 208:if(207===o[1]&&17===o[2]&&224===o[3]&&161===o[4]&&177===o[5]&&26===o[6]&&225===o[7])return h=aA.read(n,m),i=m,aA.find(h,"EncryptedPackage")?function(a,b){var c=b||{},d="Workbook",e=aA.find(a,d);try{if(d="/!DataSpaces/Version",!(e=aA.find(a,d))||!e.content||(f=e.content,(g={}).id=f.read_shift(0,"lpp4"),g.R=ex(f,4),g.U=ex(f,4),g.W=ex(f,4),d="/!DataSpaces/DataSpaceMap",!(e=aA.find(a,d))||!e.content))throw Error("ECMA-376 Encrypted file missing "+d);var f,g,h=function(a){var b=[];a.l+=4;for(var c=a.read_shift(4);c-- >0;)b.push(function(a){for(var b=a.read_shift(4),c=a.l+b-4,d={},e=a.read_shift(4),f=[];e-- >0;)f.push({t:a.read_shift(4),v:a.read_shift(0,"lpp4")});if(d.name=a.read_shift(0,"lpp4"),d.comps=f,a.l!=c)throw Error("Bad DataSpaceMapEntry: "+a.l+" != "+c);return d}(a));return b}(e.content);if(1!==h.length||1!==h[0].comps.length||0!==h[0].comps[0].t||"StrongEncryptionDataSpace"!==h[0].name||"EncryptedPackage"!==h[0].comps[0].v)throw Error("ECMA-376 Encrypted file bad "+d);if(d="/!DataSpaces/DataSpaceInfo/StrongEncryptionDataSpace",!(e=aA.find(a,d))||!e.content)throw Error("ECMA-376 Encrypted file missing "+d);var i=function(a){var b=[];a.l+=4;for(var c=a.read_shift(4);c-- >0;)b.push(a.read_shift(0,"lpp4"));return b}(e.content);if(1!=i.length||"StrongEncryptionTransform"!=i[0])throw Error("ECMA-376 Encrypted file bad "+d);if(d="/!DataSpaces/TransformInfo/StrongEncryptionTransform/!Primary",!(e=aA.find(a,d))||!e.content)throw Error("ECMA-376 Encrypted file missing "+d);!function(a){var b,c=(b={},a.read_shift(4),a.l+=4,b.id=a.read_shift(0,"lpp4"),b.name=a.read_shift(0,"lpp4"),b.R=ex(a,4),b.U=ex(a,4),b.W=ex(a,4),b);if(c.ename=a.read_shift(0,"8lpp4"),c.blksz=a.read_shift(4),c.cmode=a.read_shift(4),4!=a.read_shift(4))throw Error("Bad !Primary record")}(e.content)}catch(a){}if(d="/EncryptionInfo",!(e=aA.find(a,d))||!e.content)throw Error("ECMA-376 Encrypted file missing "+d);var j=function(a){var b,c,d,e,f=ex(a);switch(f.Minor){case 2:return[f.Minor,function(a){if((63&a.read_shift(4))!=36)throw Error("EncryptionInfo mismatch");var b=a.read_shift(4);return{t:"Std",h:ey(a,b),v:ez(a,a.length-a.l)}}(a,f)];case 3:return[f.Minor,function(){throw Error("File is password-protected: ECMA-376 Extensible")}(a,f)];case 4:return[f.Minor,(b=a,c=["saltSize","blockSize","keyBits","hashSize","cipherAlgorithm","cipherChaining","hashAlgorithm","saltValue"],b.l+=4,d=b.read_shift(b.length-b.l,"utf8"),e={},d.replace(a9,function(a){var b=bc(a);switch(bd(b[0])){case"<?xml":case"<encryption":case"</encryption>":case"</keyEncryptors>":case"</keyEncryptor>":break;case"<keyData":c.forEach(function(a){e[a]=b[a]});break;case"<dataIntegrity":e.encryptedHmacKey=b.encryptedHmacKey,e.encryptedHmacValue=b.encryptedHmacValue;break;case"<keyEncryptors>":case"<keyEncryptors":e.encs=[];break;case"<keyEncryptor":e.uri=b.uri;break;case"<encryptedKey":e.encs.push(b);break;default:throw b[0]}}),e)]}throw Error("ECMA-376 Encrypted file unrecognized Version: "+f.Minor)}(e.content);if(d="/EncryptedPackage",!(e=aA.find(a,d))||!e.content)throw Error("ECMA-376 Encrypted file missing "+d);if(4==j[0]&&"undefined"!=typeof decrypt_agile)return decrypt_agile(j[1],e.content,c.password||"",c);if(2==j[0]&&"undefined"!=typeof decrypt_std76)return decrypt_std76(j[1],e.content,c.password||"",c);throw Error("File is password-protected")}(h,i):gu(h,i);break;case 9:if(o[1]<=8)return gu(n,m);break;case 60:return go(n,m);case 73:if(73===o[1]&&42===o[2]&&0===o[3])throw Error("TIFF Image File is not a spreadsheet");if(68===o[1]){var r=n,t=m,u=t||{},v=!!u.WTF;u.WTF=!0;try{var w=ef.to_workbook(r,u);return u.WTF=v,w}catch(a){if(u.WTF=v,!a.message.match(/SYLK bad record ID/)&&v)throw a;return ei.to_workbook(r,t)}}break;case 84:if(65===o[1]&&66===o[2]&&76===o[3])return eg.to_workbook(n,m);break;case 80:return 75===o[1]&&o[2]<9&&o[3]<9?(f=n,(g=m||{}).type||(g.type=B&&Buffer.isBuffer(f)?"buffer":"base64"),function(a,b){if(aw(),g$(b=b||{}),aZ(a,"META-INF/manifest.xml")||aZ(a,"objectdata.xml")){var c=a,d=b;d=d||{},aZ(c,"META-INF/manifest.xml")&&function(a,b){for(var c,d,e=bG(a);c=bH.exec(e);)switch(c[3]){case"manifest":break;case"file-entry":if("/"==(d=bc(c[0],!1)).path&&"application/vnd.oasis.opendocument.spreadsheet"!==d.type)throw Error("This OpenDocument is not a spreadsheet");break;case"encryption-data":case"algorithm":case"start-key-generation":case"key-derivation":throw Error("Unsupported ODS Encryption");default:if(b&&b.WTF)throw c}}(a_(c,"META-INF/manifest.xml"),d);var e=a0(c,"content.xml");if(!e)throw Error("Missing content.xml in ODS / UOF file");var f=gC(bt(e),d);return aZ(c,"meta.xml")&&(f.Props=dd(a_(c,"meta.xml"))),f}if(aZ(a,"Index/Document.iwa")){if("undefined"==typeof Uint8Array)throw Error("NUMBERS file parsing requires Uint8Array support");if(a.FileIndex)return gY(a);var g,h,i,j,k,l,m,n,o,p,q,r,s,t=aA.utils.cfb_new();return a1(a).forEach(function(b){a2(t,b,function a(b,c,d){if(!d)return aY(a$(b,c));if(!c)return null;try{return a(b,c)}catch(a){return null}}(a,b))}),gY(t)}if(!aZ(a,"[Content_Types].xml")){if(aZ(a,"index.xml.gz"))throw Error("Unsupported NUMBERS 08 file");if(aZ(a,"index.xml"))throw Error("Unsupported NUMBERS 09 file");throw Error("Unsupported ZIP file")}var u=a1(a),v=function(a){var b=c2();if(!a||!a.match)return b;var c={};if((a.match(a9)||[]).forEach(function(a){var d=bc(a);switch(d[0].replace(ba,"<")){case"<?xml":break;case"<Types":b.xmlns=d["xmlns"+(d[0].match(/<(\w+):/)||["",""])[1]];break;case"<Default":c[d.Extension]=d.ContentType;break;case"<Override":void 0!==b[c0[d.ContentType]]&&b[c0[d.ContentType]].push(d.PartName)}}),b.xmlns!==bI.CT)throw Error("Unknown Namespace: "+b.xmlns);return b.calcchain=b.calcchains.length>0?b.calcchains[0]:"",b.sst=b.strs.length>0?b.strs[0]:"",b.style=b.styles.length>0?b.styles[0]:"",b.defaults=c,delete b.calcchains,b}(a0(a,"[Content_Types].xml")),w=!1;if(0===v.workbooks.length&&a_(a,q="xl/workbook.xml",!0)&&v.workbooks.push(q),0===v.workbooks.length){if(!a_(a,q="xl/workbook.bin",!0))throw Error("Could not find workbook");v.workbooks.push(q),w=!0}"bin"==v.workbooks[0].slice(-3)&&(w=!0);var x={},y={};if(!b.bookSheets&&!b.bookProps){if(fK=[],v.sst)try{fK=function(a,b,c){if(".bin"===b.slice(-4)){var d,e;return d=[],e=!1,ci(a,function(a,b,f){switch(f){case 159:d.Count=a[0],d.Unique=a[1];break;case 19:d.push(a);break;case 160:return!0;case 35:e=!0;break;case 36:e=!1;break;default:if(b.T,!e||c.WTF)throw Error("Unexpected record 0x"+f.toString(16))}}),d}return function(a,b){var c=[],d="";if(!a)return c;var e=a.match(eq);if(e){d=e[2].replace(er,"").split(es);for(var f=0;f!=d.length;++f){var g=ep(d[f].trim(),b);null!=g&&(c[c.length]=g)}c.Count=(e=bc(e[1])).count,c.Unique=e.uniqueCount}return c}(a,c)}(a_(a,g0(v.sst)),v.sst,b)}catch(a){if(b.WTF)throw a}b.cellStyles&&v.themes.length&&(g=a0(a,v.themes[0].replace(/^\//,""),!0)||"",v.themes[0],x=e5(g,b)),v.style&&(y=function(a,b,c,d){if(".bin"===b.slice(-4)){var e={};for(var f in e.NumberFmt=[],U)e.NumberFmt[f]=U[f];e.CellXf=[],e.Fonts=[];var g=[],h=!1;return ci(a,function(a,b,f){switch(f){case 44:e.NumberFmt[a[0]]=a[1],au(a[1],a[0]);break;case 43:e.Fonts.push(a),null!=a.color.theme&&c&&c.themeElements&&c.themeElements.clrScheme&&(a.color.rgb=eG(c.themeElements.clrScheme[a.color.theme].rgb,a.color.tint||0));break;case 1025:case 45:case 46:case 48:case 507:case 572:case 475:case 1171:case 2102:case 1130:case 512:case 2095:case 3072:break;case 47:617==g[g.length-1]&&e.CellXf.push(a);break;case 35:h=!0;break;case 36:h=!1;break;case 37:g.push(f),h=!0;break;case 38:g.pop(),h=!1;break;default:if(b.T>0)g.push(f);else if(b.T<0)g.pop();else if(!h||d.WTF&&37!=g[g.length-1])throw Error("Unexpected record 0x"+f.toString(16))}}),e}return eT(a,c,d)}(a_(a,g0(v.style)),v.style,x,b))}v.links.map(function(c){try{c6(a0(a,c5(g0(c))),c);var d=a_(a,g0(c)),e=c,f=b;if(".bin"===e.slice(-4)){if(!d)return d;var g=f||{},h=!1;return void ci(d,function(a,b,c){switch(c){case 359:case 363:case 364:case 366:case 367:case 368:case 369:case 370:case 371:case 472:case 577:case 578:case 579:case 580:case 581:case 582:case 583:case 584:case 585:case 586:case 587:break;case 35:h=!0;break;case 36:h=!1;break;default:if(b.T);else if(!h||g.WTF)throw Error("Unexpected record 0x"+c.toString(16))}},g)}return}catch(a){}});var z=function(a,b,c){if(".bin"===b.slice(-4)){var d,e,f,g,h,i;return e={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},xmlns:""},f=[],g=!1,(d=c)||(d={}),d.biff=12,h=[],(i=[[]]).SheetNames=[],i.XTI=[],gv[16]={n:"BrtFRTArchID$",f:gj},ci(a,function(a,b,c){switch(c){case 156:i.SheetNames.push(a.name),e.Sheets.push(a);break;case 153:e.WBProps=a;break;case 39:null!=a.Sheet&&(d.SID=a.Sheet),a.Ref=fC(a.Ptg,null,null,i,d),delete d.SID,delete a.Ptg,h.push(a);break;case 1036:case 361:case 2071:case 158:case 143:case 664:case 353:case 3072:case 3073:case 534:case 677:case 157:case 610:case 2050:case 155:case 548:case 676:case 128:case 665:case 2128:case 2125:case 549:case 2053:case 596:case 2076:case 2075:case 2082:case 397:case 154:case 1117:case 553:case 2091:case 16:break;case 357:case 358:case 355:case 667:i[0].length?i.push([c,a]):i[0]=[c,a],i[i.length-1].XTI=[];break;case 362:0===i.length&&(i[0]=[],i[0].XTI=[]),i[i.length-1].XTI=i[i.length-1].XTI.concat(a),i.XTI=i.XTI.concat(a);break;case 35:case 37:f.push(c),g=!0;break;case 36:case 38:f.pop(),g=!1;break;default:if(b.T);else if(!g||d.WTF&&37!=f[f.length-1]&&35!=f[f.length-1])throw Error("Unexpected record 0x"+c.toString(16))}},d),ge(e),e.Names=h,e.supbooks=i,e}return function(a,b){if(!a)throw Error("Could not find file");var c={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},Names:[],xmlns:""},d=!1,e="xmlns",f={},g=0;if(a.replace(a9,function(h,i){var j=bc(h);switch(bd(j[0])){case"<?xml":case"</workbook>":case"<fileVersion/>":case"</fileVersion>":case"<fileSharing":case"<fileSharing/>":case"</workbookPr>":case"<workbookProtection":case"<workbookProtection/>":case"<bookViews":case"<bookViews>":case"</bookViews>":case"</workbookView>":case"<sheets":case"<sheets>":case"</sheets>":case"</sheet>":case"<functionGroups":case"<functionGroups/>":case"<functionGroup":case"<externalReferences":case"</externalReferences>":case"<externalReferences>":case"<externalReference":case"<definedNames/>":case"<definedName/>":case"</calcPr>":case"<oleSize":case"<customWorkbookViews>":case"</customWorkbookViews>":case"<customWorkbookViews":case"<customWorkbookView":case"</customWorkbookView>":case"<pivotCaches>":case"</pivotCaches>":case"<pivotCaches":case"<pivotCache":case"<smartTagPr":case"<smartTagPr/>":case"<smartTagTypes":case"<smartTagTypes>":case"</smartTagTypes>":case"<smartTagType":case"<webPublishing":case"<webPublishing/>":case"<fileRecoveryPr":case"<fileRecoveryPr/>":case"<webPublishObjects>":case"<webPublishObjects":case"</webPublishObjects>":case"<webPublishObject":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":case"<ArchID":case"<revisionPtr":break;case"<workbook":h.match(gh)&&(e="xmlns"+h.match(/<(\w+):/)[1]),c.xmlns=j[e];break;case"<fileVersion":delete j[0],c.AppVersion=j;break;case"<workbookPr":case"<workbookPr/>":f8.forEach(function(a){if(null!=j[a[0]])switch(a[2]){case"bool":c.WBProps[a[0]]=bo(j[a[0]]);break;case"int":c.WBProps[a[0]]=parseInt(j[a[0]],10);break;default:c.WBProps[a[0]]=j[a[0]]}}),j.codeName&&(c.WBProps.CodeName=bt(j.codeName));break;case"<workbookView":case"<workbookView/>":delete j[0],c.WBView.push(j);break;case"<sheet":switch(j.state){case"hidden":j.Hidden=1;break;case"veryHidden":j.Hidden=2;break;default:j.Hidden=0}delete j.state,j.name=bg(bt(j.name)),delete j[0],c.Sheets.push(j);break;case"<definedNames>":case"<definedNames":case"<ext":case"<AlternateContent":case"<AlternateContent>":d=!0;break;case"</definedNames>":case"</ext>":case"</AlternateContent>":d=!1;break;case"<definedName":(f={}).Name=bt(j.name),j.comment&&(f.Comment=j.comment),j.localSheetId&&(f.Sheet=+j.localSheetId),bo(j.hidden||"0")&&(f.Hidden=!0),g=i+h.length;break;case"</definedName>":f.Ref=bg(bt(a.slice(g,i))),c.Names.push(f);break;case"<calcPr":case"<calcPr/>":delete j[0],c.CalcPr=j;break;default:if(!d&&b.WTF)throw Error("unrecognized "+j[0]+" in workbook")}return h}),-1===bJ.indexOf(c.xmlns))throw Error("Unknown Namespace: "+c.xmlns);return ge(c),c}(a,c)}(a_(a,g0(v.workbooks[0])),v.workbooks[0],b),A={},B="";v.coreprops.length&&((B=a_(a,g0(v.coreprops[0]),!0))&&(A=dd(B)),0!==v.extprops.length)&&(B=a_(a,g0(v.extprops[0]),!0))&&(h=B,i=A,j=b,k={},i||(i={}),h=bt(h),dg.forEach(function(a){var b=(h.match(bv(a[0]))||[])[1];switch(a[2]){case"string":b&&(i[a[1]]=bg(b));break;case"bool":i[a[1]]="true"===b;break;case"raw":var c=h.match(RegExp("<"+a[0]+"[^>]*>([\\s\\S]*?)</"+a[0]+">"));c&&c.length>0&&(k[a[1]]=c[1])}}),k.HeadingPairs&&k.TitlesOfParts&&di(k.HeadingPairs,k.TitlesOfParts,i,j));var C={};(!b.bookSheets||b.bookProps)&&0!==v.custprops.length&&(B=a0(a,g0(v.custprops[0]),!0))&&(C=function(a,b){var c={},d="",e=a.match(dk);if(e)for(var f=0;f!=e.length;++f){var g=e[f],h=bc(g);switch(h[0]){case"<?xml":case"<Properties":break;case"<property":d=bg(h.name);break;case"</property>":d=null;break;default:if(0===g.indexOf("<vt:")){var i=g.split(">"),j=i[0].slice(4),k=i[1];switch(j){case"lpstr":case"bstr":case"lpwstr":case"cy":case"error":c[d]=bg(k);break;case"bool":c[d]=bo(k);break;case"i1":case"i2":case"i4":case"i8":case"int":case"uint":c[d]=parseInt(k,10);break;case"r4":case"r8":case"decimal":c[d]=parseFloat(k);break;case"filetime":case"date":c[d]=aP(k);break;default:if("/"==j.slice(-1))break;b.WTF&&"undefined"!=typeof console&&console.warn("Unexpected",g,j,i)}}else if("</"===g.slice(0,2));else if(b.WTF)throw Error(g)}}return c}(B,b));var D={};if((b.bookSheets||b.bookProps)&&(z.Sheets?p=z.Sheets.map(function(a){return a.name}):A.Worksheets&&A.SheetNames.length>0&&(p=A.SheetNames),b.bookProps&&(D.Props=A,D.Custprops=C),b.bookSheets&&void 0!==p&&(D.SheetNames=p),b.bookSheets?D.SheetNames:b.bookProps))return D;p={};var E={};b.bookDeps&&v.calcchain&&(E=function(a,b,c){if(".bin"===b.slice(-4)){var d;return d=[],ci(a,function(a,b,c){if(63===c)d.push(a);else if(b.T);else if(1)throw Error("Unexpected record 0x"+c.toString(16))}),d}var e=[];if(!a)return e;var f=1;return(a.match(a9)||[]).forEach(function(a){var b=bc(a);switch(b[0]){case"<?xml":case"<calcChain":case"<calcChain>":case"</calcChain>":break;case"<c":delete b[0],b.i?f=b.i:b.i=f,e.push(b)}}),e}(a_(a,g0(v.calcchain)),v.calcchain,b));var F=0,G={},H=z.Sheets;A.Worksheets=H.length,A.SheetNames=[];for(var I=0;I!=H.length;++I)A.SheetNames[I]=H[I].name;var J=w?"bin":"xml",K=v.workbooks[0].lastIndexOf("/"),L=(v.workbooks[0].slice(0,K+1)+"_rels/"+v.workbooks[0].slice(K+1)+".rels").replace(/^\//,"");aZ(a,L)||(L="xl/_rels/workbook."+J+".rels");var M=c6(a0(a,L,!0),L.replace(/_rels.*/,"s5s"));(v.metadata||[]).length>=1&&(b.xlmeta=function(a,b,c){if(".bin"===b.slice(-4)){var d,e,f,g,h;return d={Types:[],Cell:[],Value:[]},e=c||{},f=[],g=!1,h=2,ci(a,function(a,b,c){switch(c){case 335:d.Types.push({name:a.name});break;case 51:a.forEach(function(a){1==h?d.Cell.push({type:d.Types[a[0]-1].name,index:a[1]}):0==h&&d.Value.push({type:d.Types[a[0]-1].name,index:a[1]})});break;case 337:h=+!!a;break;case 338:h=2;break;case 35:f.push(c),g=!0;break;case 36:f.pop(),g=!1;break;default:if(b.T);else if(!g||e.WTF&&35!=f[f.length-1])throw Error("Unexpected record 0x"+c.toString(16))}}),d}var i,j={Types:[],Cell:[],Value:[]};if(!a)return j;var k=!1,l=2;return a.replace(a9,function(a){var b=bc(a);switch(bd(b[0])){case"<?xml":case"<metadata":case"</metadata>":case"<metadataTypes":case"</metadataTypes>":case"</metadataType>":case"</futureMetadata>":case"<bk>":case"</bk>":case"</rc>":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<metadataType":j.Types.push({name:b.name});break;case"<futureMetadata":for(var d=0;d<j.Types.length;++d)j.Types[d].name==b.name&&(i=j.Types[d]);break;case"<rc":1==l?j.Cell.push({type:j.Types[b.t-1].name,index:+b.v}):0==l&&j.Value.push({type:j.Types[b.t-1].name,index:+b.v});break;case"<cellMetadata":l=1;break;case"</cellMetadata>":case"</valueMetadata>":l=2;break;case"<valueMetadata":l=0;break;case"<ext":k=!0;break;case"</ext>":k=!1;break;case"<rvb":if(!i)break;i.offsets||(i.offsets=[]),i.offsets.push(+b.i);break;default:if(!k&&c.WTF)throw Error("unrecognized "+b[0]+" in metadata")}return a}),j}(a_(a,g0(v.metadata[0])),v.metadata[0],b)),(v.people||[]).length>=1&&(b.people=(l=a_(a,g0(v.people[0])),m=b,n=[],o=!1,l.replace(a9,function(a){var b=bc(a);switch(bd(b[0])){case"<?xml":case"<personList":case"</personList>":case"</person>":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<person":n.push({name:b.displayname,id:b.id});break;case"<ext":o=!0;break;case"</ext>":o=!1;break;default:if(!o&&m.WTF)throw Error("unrecognized "+b[0]+" in threaded comments")}return a}),n)),M&&(M=function(a,b){if(!a)return 0;try{a=b.map(function(b){var c;return b.id||(b.id=b.strRelID),[b.name,a["!id"][b.id].Target,(c=a["!id"][b.id].Type,c4.WS.indexOf(c)>-1?"sheet":c4.CS&&c==c4.CS?"chart":c4.DS&&c==c4.DS?"dialog":c4.MS&&c==c4.MS?"macro":c&&c.length?c:"sheet")]})}catch(a){return null}return a&&0!==a.length?a:null}(M,z.Sheets));var N=+!!a_(a,"xl/worksheets/sheet.xml",!0);for(F=0;F!=A.Worksheets;++F){var O="sheet";if(M&&M[F]?(aZ(a,r="xl/"+M[F][1].replace(/[\/]?xl\//,""))||(r=M[F][1]),aZ(a,r)||(r=L.replace(/_rels\/.*$/,"")+M[F][1]),O=M[F][2]):r=(r="xl/worksheets/sheet"+(F+1-N)+"."+J).replace(/sheet0\./,"sheet."),s=r.replace(/^(.*)(\/)([^\/]*)$/,"$1/_rels/$3.rels"),b&&null!=b.sheets)switch(typeof b.sheets){case"number":if(F!=b.sheets)continue;break;case"string":if(A.SheetNames[F].toLowerCase()!=b.sheets.toLowerCase())continue;break;default:if(Array.isArray&&Array.isArray(b.sheets)){for(var P=!1,Q=0;Q!=b.sheets.length;++Q)"number"==typeof b.sheets[Q]&&b.sheets[Q]==F&&(P=1),"string"==typeof b.sheets[Q]&&b.sheets[Q].toLowerCase()==A.SheetNames[F].toLowerCase()&&(P=1);if(!P)continue}}!function(a,b,c,d,e,f,g,h,i,j,k,l){try{f[d]=c6(a0(a,c,!0),b);var m,n,o=a_(a,b);switch(h){case"sheet":m=f[d],n=".bin"===b.slice(-4)?function(a,b,c,d,e,f,g){if(!a)return a;var h,i,j,k,l,m,n,o,p,q,r,s,t=b||{};d||(d={"!id":{}});var u=t.dense?[]:{},v={s:{r:2e6,c:2e6},e:{r:0,c:0}},w=[],x=!1,y=!1,z=[];t.biff=12,t["!row"]=0;var A=0,B=!1,C=[],D={},E=t.supbooks||e.supbooks||[[]];if(E.sharedf=D,E.arrayf=C,E.SheetNames=e.SheetNames||e.Sheets.map(function(a){return a.name}),!t.supbooks&&(t.supbooks=E,e.Names))for(var F=0;F<e.Names.length;++F)E[0][F+1]=e.Names[F];var G=[],H=[],I=!1;if(gv[16]={n:"BrtShortReal",f:f6},ci(a,function(a,b,F){if(!y)switch(F){case 148:h=a;break;case 0:i=a,t.sheetRows&&t.sheetRows<=i.r&&(y=!0),p=cq(l=i.r),t["!row"]=i.r,(a.hidden||a.hpt||null!=a.level)&&(a.hpt&&(a.hpx=eP(a.hpt)),H[a.r]=a);break;case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:case 13:case 14:case 15:case 16:case 17:case 18:case 62:switch(j={t:a[2]},a[2]){case"n":j.v=a[1];break;case"s":o=fK[a[1]],j.v=o.t,j.r=o.r;break;case"b":j.v=!!a[1];break;case"e":j.v=a[1],!1!==t.cellText&&(j.w=c$[j.v]);break;case"str":j.t="s",j.v=a[1];break;case"is":j.t="s",j.v=a[1].t}if((k=g.CellXf[a[0].iStyleRef])&&fR(j,k.numFmtId,null,t,f,g),m=-1==a[0].c?m+1:a[0].c,t.dense?(u[l]||(u[l]=[]),u[l][m]=j):u[cs(m)+p]=j,t.cellFormula){for(A=0,B=!1;A<C.length;++A){var J=C[A];i.r>=J[0].s.r&&i.r<=J[0].e.r&&m>=J[0].s.c&&m<=J[0].e.c&&(j.F=cw(J[0]),B=!0)}!B&&a.length>3&&(j.f=a[3])}if(v.s.r>i.r&&(v.s.r=i.r),v.s.c>m&&(v.s.c=m),v.e.r<i.r&&(v.e.r=i.r),v.e.c<m&&(v.e.c=m),t.cellDates&&k&&"n"==j.t&&aq(U[k.numFmtId])){var K=Y(j.v);K&&(j.t="d",j.v=new Date(K.y,K.m-1,K.d,K.H,K.M,K.S,K.u))}r&&("XLDAPR"==r.type&&(j.D=!0),r=void 0),s&&(s=void 0);break;case 1:case 12:if(!t.sheetStubs||x)break;j={t:"z",v:void 0},m=-1==a[0].c?m+1:a[0].c,t.dense?(u[l]||(u[l]=[]),u[l][m]=j):u[cs(m)+p]=j,v.s.r>i.r&&(v.s.r=i.r),v.s.c>m&&(v.s.c=m),v.e.r<i.r&&(v.e.r=i.r),v.e.c<m&&(v.e.c=m),r&&("XLDAPR"==r.type&&(j.D=!0),r=void 0),s&&(s=void 0);break;case 176:z.push(a);break;case 49:r=((t.xlmeta||{}).Cell||[])[a-1];break;case 494:var L=d["!id"][a.relId];for(L?(a.Target=L.Target,a.loc&&(a.Target+="#"+a.loc),a.Rel=L):""==a.relId&&(a.Target="#"+a.loc),l=a.rfx.s.r;l<=a.rfx.e.r;++l)for(m=a.rfx.s.c;m<=a.rfx.e.c;++m)t.dense?(u[l]||(u[l]=[]),u[l][m]||(u[l][m]={t:"z",v:void 0}),u[l][m].l=a):(u[n=cu({c:m,r:l})]||(u[n]={t:"z",v:void 0}),u[n].l=a);break;case 426:if(!t.cellFormula)break;C.push(a),(q=t.dense?u[l][m]:u[cs(m)+p]).f=fC(a[1],v,{r:i.r,c:m},E,t),q.F=cw(a[0]);break;case 427:if(!t.cellFormula)break;D[cu(a[0].s)]=a[1],(q=t.dense?u[l][m]:u[cs(m)+p]).f=fC(a[1],v,{r:i.r,c:m},E,t);break;case 60:if(!t.cellStyles)break;for(;a.e>=a.s;)G[a.e--]={width:a.w/256,hidden:!!(1&a.flags),level:a.level},I||(I=!0,eM(a.w/256)),eN(G[a.e+1]);break;case 161:u["!autofilter"]={ref:cw(a)};break;case 476:u["!margins"]=a;break;case 147:e.Sheets[c]||(e.Sheets[c]={}),a.name&&(e.Sheets[c].CodeName=a.name),(a.above||a.left)&&(u["!outline"]={above:a.above,left:a.left});break;case 137:e.Views||(e.Views=[{}]),e.Views[0]||(e.Views[0]={}),a.RTL&&(e.Views[0].RTL=!0);break;case 485:case 64:case 1053:case 151:case 152:case 175:case 644:case 625:case 562:case 396:case 1112:case 1146:case 471:case 1050:case 649:case 1105:case 589:case 607:case 564:case 1055:case 168:case 174:case 1180:case 499:case 507:case 550:case 171:case 167:case 1177:case 169:case 1181:case 551:case 552:case 661:case 639:case 478:case 537:case 477:case 536:case 1103:case 680:case 1104:case 1024:case 663:case 535:case 678:case 504:case 1043:case 428:case 170:case 3072:case 50:case 2070:case 1045:break;case 35:x=!0;break;case 36:x=!1;break;case 37:w.push(F),x=!0;break;case 38:w.pop(),x=!1;break;default:if(b.T);else if(!x||t.WTF)throw Error("Unexpected record 0x"+F.toString(16))}},t),delete t.supbooks,delete t["!row"],!u["!ref"]&&(v.s.r<2e6||h&&(h.e.r>0||h.e.c>0||h.s.r>0||h.s.c>0))&&(u["!ref"]=cw(h||v)),t.sheetRows&&u["!ref"]){var J=cx(u["!ref"]);t.sheetRows<=+J.e.r&&(J.e.r=t.sheetRows-1,J.e.r>v.e.r&&(J.e.r=v.e.r),J.e.r<J.s.r&&(J.s.r=J.e.r),J.e.c>v.e.c&&(J.e.c=v.e.c),J.e.c<J.s.c&&(J.s.c=J.e.c),u["!fullref"]=u["!ref"],u["!ref"]=cw(J))}return z.length>0&&(u["!merges"]=z),G.length>0&&(u["!cols"]=G),H.length>0&&(u["!rows"]=H),u}(o,i,e,m,j,k,l):function(a,b,c,d,e,f,g){if(!a)return a;d||(d={"!id":{}});var h=b.dense?[]:{},i={s:{r:2e6,c:2e6},e:{r:0,c:0}},j="",k="",l=a.match(fT);l?(j=a.slice(0,l.index),k=a.slice(l.index+l[0].length)):j=k=a;var m=j.match(fZ);m?f0(m[0],h,e,c):(m=j.match(f$))&&(o=m[0],p=m[1],q=h,r=e,s=c,f0(o.slice(0,o.indexOf(">")),q,r,s));var n=(j.match(/<(?:\w*:)?dimension/)||{index:-1}).index;if(n>0){var o,p,q,r,s,t,u=j.slice(n,n+50).match(fV);u&&(t=cx(u[1])).s.r<=t.e.r&&t.s.c<=t.e.c&&t.s.r>=0&&t.s.c>=0&&(h["!ref"]=cw(t))}var v=j.match(f_);v&&v[1]&&function(a,b){b.Views||(b.Views=[{}]),(a.match(f3)||[]).forEach(function(a,c){var d=bc(a);b.Views[c]||(b.Views[c]={}),+d.zoomScale&&(b.Views[c].zoom=+d.zoomScale),bo(d.rightToLeft)&&(b.Views[c].RTL=!0)})}(v[1],e);var w=[];if(b.cellStyles){var x=j.match(fW);x&&function(a,b){for(var c=!1,d=0;d!=b.length;++d){var e=bc(b[d],!0);e.hidden&&(e.hidden=bo(e.hidden));var f=parseInt(e.min,10)-1,g=parseInt(e.max,10)-1;for(e.outlineLevel&&(e.level=+e.outlineLevel||0),delete e.min,delete e.max,e.width=+e.width,!c&&e.width&&(c=!0,eM(e.width)),eN(e);f<=g;)a[f++]=aR(e)}}(w,x)}l&&f4(l[1],h,b,i,f,g);var y=k.match(fX);y&&(h["!autofilter"]={ref:(y[0].match(/ref="([^"]*)"/)||[])[1]});var z=[],A=k.match(fS);if(A)for(n=0;n!=A.length;++n)z[n]=cx(A[n].slice(A[n].indexOf('"')+1));var B=k.match(fU);B&&function(a,b,c){for(var d=Array.isArray(a),e=0;e!=b.length;++e){var f=bc(bt(b[e]),!0);if(!f.ref)return;var g=((c||{})["!id"]||[])[f.id];g?(f.Target=g.Target,f.location&&(f.Target+="#"+bg(f.location))):(f.Target="#"+bg(f.location),g={Target:f.Target,TargetMode:"Internal"}),f.Rel=g,f.tooltip&&(f.Tooltip=f.tooltip,delete f.tooltip);for(var h=cx(f.ref),i=h.s.r;i<=h.e.r;++i)for(var j=h.s.c;j<=h.e.c;++j){var k=cu({c:j,r:i});d?(a[i]||(a[i]=[]),a[i][j]||(a[i][j]={t:"z",v:void 0}),a[i][j].l=f):(a[k]||(a[k]={t:"z",v:void 0}),a[k].l=f)}}}(h,B,d);var C=k.match(fY);if(C&&(h["!margins"]=function(a){var b={};return["left","right","top","bottom","header","footer"].forEach(function(c){a[c]&&(b[c]=parseFloat(a[c]))}),b}(bc(C[0]))),!h["!ref"]&&i.e.c>=i.s.c&&i.e.r>=i.s.r&&(h["!ref"]=cw(i)),b.sheetRows>0&&h["!ref"]){var D=cx(h["!ref"]);b.sheetRows<=+D.e.r&&(D.e.r=b.sheetRows-1,D.e.r>i.e.r&&(D.e.r=i.e.r),D.e.r<D.s.r&&(D.s.r=D.e.r),D.e.c>i.e.c&&(D.e.c=i.e.c),D.e.c<D.s.c&&(D.s.c=D.e.c),h["!fullref"]=h["!ref"],h["!ref"]=cw(D))}return w.length>0&&(h["!cols"]=w),z.length>0&&(h["!merges"]=z),h}(o,i,e,m,j,k,l);break;case"chart":if(!(n=function(a,b,c,d,e,f,g,h){if(".bin"===b.slice(-4)){var i=e;if(!a)return a;i||(i={"!id":{}});var j={"!type":"chart","!drawel":null,"!rel":""},k=[],l=!1;return ci(a,function(a,b,e){switch(e){case 550:j["!rel"]=a;break;case 651:f.Sheets[c]||(f.Sheets[c]={}),a.name&&(f.Sheets[c].CodeName=a.name);break;case 562:case 652:case 669:case 679:case 551:case 552:case 476:case 3072:break;case 35:l=!0;break;case 36:l=!1;break;case 37:k.push(e);break;case 38:k.pop();break;default:if(b.T>0)k.push(e);else if(b.T<0)k.pop();else if(!l||d.WTF)throw Error("Unexpected record 0x"+e.toString(16))}},d),i["!id"][j["!rel"]]&&(j["!drawel"]=i["!id"][j["!rel"]]),j}var m=e;if(!a)return a;m||(m={"!id":{}});var n,o={"!type":"chart","!drawel":null,"!rel":""},p=a.match(fZ);return p&&f0(p[0],o,f,c),(n=a.match(/drawing r:id="(.*?)"/))&&(o["!rel"]=n[1]),m["!id"][o["!rel"]]&&(o["!drawel"]=m["!id"][o["!rel"]]),o}(o,b,e,i,f[d],j,0,0))||!n["!drawel"])break;var p=a5(n["!drawel"].Target,b),q=c5(p),r=function(a,b){if(!a)return"??";var c=(a.match(/<c:chart [^>]*r:id="([^"]*)"/)||["",""])[1];return b["!id"][c].Target}(a0(a,p,!0),c6(a0(a,q,!0),p)),s=a5(r,p),t=c5(s);n=function(a,b,c,d,e,f){var g=f||{"!type":"chart"};if(!a)return f;var h=0,i=0,j="A",k={s:{r:2e6,c:2e6},e:{r:0,c:0}};return(a.match(/<c:numCache>[\s\S]*?<\/c:numCache>/gm)||[]).forEach(function(a){var b,c,d,e,f=(c=[],d=a.match(/^<c:numCache>/),(a.match(/<c:pt idx="(\d*)">(.*?)<\/c:pt>/mg)||[]).forEach(function(a){var b=a.match(/<c:pt idx="(\d*?)"><c:v>(.*)<\/c:v><\/c:pt>/);b&&(c[+b[1]]=d?+b[2]:b[2])}),e=bg((a.match(/<c:formatCode>([\s\S]*?)<\/c:formatCode>/)||["","General"])[1]),(a.match(/<c:f>(.*?)<\/c:f>/mg)||[]).forEach(function(a){b=a.replace(/<.*?>/g,"")}),[c,e,b]);k.s.r=k.s.c=0,k.e.c=h,j=cs(h),f[0].forEach(function(a,b){g[j+cq(b)]={t:"n",v:a,z:f[1]},i=b}),k.e.r<i&&(k.e.r=i),++h}),h>0&&(g["!ref"]=cw(k)),g}(a0(a,s,!0),0,0,c6(a0(a,t,!0),s),0,n);break;case"macro":f[d],b.slice(-4),n={"!type":"macro"};break;case"dialog":f[d],b.slice(-4),n={"!type":"dialog"};break;default:throw Error("Unrecognized sheet type "+h)}g[d]=n;var u=[];f&&f[d]&&aC(f[d]).forEach(function(c){var e,g,h,j,k,l="";if(f[d][c].Type==c4.CMNT){l=a5(f[d][c].Target,b);var m=function(a,b,c){if(".bin"===b.slice(-4)){var d,e,f,g;return d=[],e=[],f={},g=!1,ci(a,function(a,b,h){switch(h){case 632:e.push(a);break;case 635:f=a;break;case 637:f.t=a.t,f.h=a.h,f.r=a.r;break;case 636:if(f.author=e[f.iauthor],delete f.iauthor,c.sheetRows&&f.rfx&&c.sheetRows<=f.rfx.r)break;f.t||(f.t=""),delete f.rfx,d.push(f);break;case 3072:case 37:case 38:break;case 35:g=!0;break;case 36:g=!1;break;default:if(b.T);else if(!g||c.WTF)throw Error("Unexpected record 0x"+h.toString(16))}}),d}if(a.match(/<(?:\w+:)?comments *\/>/))return[];var h=[],i=[],j=a.match(/<(?:\w+:)?authors>([\s\S]*)<\/(?:\w+:)?authors>/);j&&j[1]&&j[1].split(/<\/\w*:?author>/).forEach(function(a){if(""!==a&&""!==a.trim()){var b=a.match(/<(?:\w+:)?author[^>]*>(.*)/);b&&h.push(b[1])}});var k=a.match(/<(?:\w+:)?commentList>([\s\S]*)<\/(?:\w+:)?commentList>/);return k&&k[1]&&k[1].split(/<\/\w*:?comment>/).forEach(function(a){if(""!==a&&""!==a.trim()){var b=a.match(/<(?:\w+:)?comment[^>]*>/);if(b){var d=bc(b[0]),e={author:d.authorId&&h[d.authorId]||"sheetjsghost",ref:d.ref,guid:d.guid},f=ct(d.ref);if(!c.sheetRows||!(c.sheetRows<=f.r)){var g=a.match(/<(?:\w+:)?text>([\s\S]*)<\/(?:\w+:)?text>/),j=!!g&&!!g[1]&&ep(g[1])||{r:"",t:"",h:""};e.r=j.r,"<t></t>"==j.r&&(j.t=j.h=""),e.t=(j.t||"").replace(/\r\n/g,"\n").replace(/\r/g,"\n"),c.cellHTML&&(e.h=j.h),i.push(e)}}}}),i}(a_(a,l,!0),l,i);if(!m||!m.length)return;fa(n,m,!1)}f[d][c].Type==c4.TCMNT&&(l=a5(f[d][c].Target,b),u=u.concat((e=a_(a,l,!0),g=[],h=!1,j={},k=0,e.replace(a9,function(a,b){var c=bc(a);switch(bd(c[0])){case"<?xml":case"<ThreadedComments":case"</ThreadedComments>":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<threadedComment":j={author:c.personId,guid:c.id,ref:c.ref,T:1};break;case"</threadedComment>":null!=j.t&&g.push(j);break;case"<text>":case"<text":k=b+a.length;break;case"</text>":j.t=e.slice(k,b).replace(/\r\n/g,"\n").replace(/\r/g,"\n");break;case"<mentions":case"<mentions>":case"<ext":h=!0;break;case"</mentions>":case"</ext>":h=!1;break;default:if(!h&&i.WTF)throw Error("unrecognized "+c[0]+" in threaded comments")}return a}),g)))}),u&&u.length&&fa(n,u,!0,i.people||[])}catch(a){if(i.WTF)throw a}}(a,r,s,A.SheetNames[F],F,G,p,O,b,z,x,y)}return D={Directory:v,Workbook:z,Props:A,Custprops:C,Deps:E,Sheets:p,SheetNames:A.SheetNames,Strings:fK,Styles:y,Themes:x,SSF:aR(U)},b&&b.bookFiles&&(a.files?(D.keys=u,D.files=a.files):(D.keys=[],D.files={},a.FullPaths.forEach(function(b,c){b=b.replace(/^Root Entry[\/]/,""),D.keys.push(b),D.files[b]=a.FileIndex[c]}))),b&&b.bookVBA&&(v.vba.length>0?D.vbaraw=a_(a,g0(v.vba[0]),!0):v.defaults&&"application/vnd.ms-office.vbaProject"===v.defaults.bin&&(D.vbaraw=a_(a,"xl/vbaProject.bin",!0))),D}(a4(f,g),g)):g3(b,n,m,p);case 239:return 60===o[3]?go(n,m):g3(b,n,m,p);case 255:if(254===o[1])return j=n,k=m,l=j,"base64"==k.type&&(l=A(l)),l=e.utils.decode(1200,l.slice(2),"str"),k.type="binary",g2(l,k);if(0===o[1]&&2===o[2]&&0===o[3])return ej.to_workbook(n,m);break;case 0:if(0===o[1]&&(o[2]>=2&&0===o[3]||0===o[2]&&(8===o[3]||9===o[3])))return ej.to_workbook(n,m);break;case 3:case 131:case 139:case 140:return ee.to_workbook(n,m);case 123:if(92===o[1]&&114===o[2]&&116===o[3])return eE.to_workbook(n,m);break;case 10:case 13:case 32:var x=n,y=m,z="",C=g1(x,y);switch(y.type){case"base64":z=A(x);break;case"binary":z=x;break;case"buffer":z=x.toString("binary");break;case"array":z=aQ(x);break;default:throw Error("Unrecognized type "+y.type)}return 239==C[0]&&187==C[1]&&191==C[2]&&(z=bt(z)),y.type="binary",g2(z,y);case 137:if(80===o[1]&&78===o[2]&&71===o[3])throw Error("PNG Image File is not a spreadsheet")}return ed.indexOf(o[0])>-1&&o[2]<=12&&o[3]<=31?ee.to_workbook(n,m):g3(b,n,m,p)}(b,{type:"array"}),f=c.SheetNames[0],h=c.Sheets[f],i=hc.sheet_to_json(h,{header:1}).slice(1).map((a,b)=>({name:a[0]||"",rankKey:a[1]||"",militaryNumber:a[2]||"",nationalId:a[3]||"",unitId:a[4]||"",birthDate:a[5]||"",birthPlace:a[6]||"",motherName:a[7]||"",maritalStatus:a[8]||"",appointmentDate:a[9]||"",lastPromotionDate:a[10]||"",bankName:a[11]||"",bankBranch:a[12]||"",bankAccount:a[13]||"",leaveBalance:a[14]||"",leaveType:a[15]||"",employmentStatus:a[16]||"",qualification:a[17]||"",qualificationDate:a[18]||""}));g(i),r(i)}catch(a){m(["خطأ في قراءة الملف. تأكد من أن الملف بصيغة Excel صحيحة."])}},b.readAsArrayBuffer(a)})(b))}})]}),(0,i.jsx)("p",{className:"pr-1",children:"أو اسحب الملف هنا"})]}),(0,i.jsx)("p",{className:"text-xs text-gray-500",children:"ملفات Excel فقط (.xlsx, .xls)"})]})})]}),b&&(0,i.jsxs)("div",{className:"bg-gray-50 p-4 rounded-md",children:[(0,i.jsxs)("p",{className:"text-sm text-gray-700",children:["الملف المختار: ",(0,i.jsx)("span",{className:"font-medium",children:b.name})]}),(0,i.jsxs)("p",{className:"text-sm text-gray-700",children:["عدد الصفوف: ",(0,i.jsx)("span",{className:"font-medium",children:f.length})]})]}),h.length>0&&(0,i.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:[(0,i.jsx)("h3",{className:"text-sm font-medium text-red-800 mb-2",children:"الأخطاء الموجودة:"}),(0,i.jsx)("ul",{className:"text-sm text-red-700 space-y-1",children:h.map((a,b)=>(0,i.jsxs)("li",{children:["• ",a]},b))})]}),p&&(0,i.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-md p-4",children:(0,i.jsx)("p",{className:"text-sm text-green-800",children:p})}),f.length>0&&(0,i.jsxs)("div",{className:"flex justify-end space-x-3 space-x-reverse",children:[(0,i.jsx)("button",{onClick:()=>{c(null),g([]),m([])},className:"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium",children:"إلغاء"}),(0,i.jsx)("button",{onClick:t,disabled:n||h.length>0,className:"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md text-sm font-medium",children:n?"جاري الاستيراد...":"استيراد البيانات"})]})]})]})})})})]})}l.version},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},35459:()=>{},37355:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,48365,23)),Promise.resolve().then(c.t.bind(c,64596,23)),Promise.resolve().then(c.t.bind(c,56186,23)),Promise.resolve().then(c.t.bind(c,67805,23)),Promise.resolve().then(c.t.bind(c,27561,23)),Promise.resolve().then(c.t.bind(c,47569,23)),Promise.resolve().then(c.t.bind(c,42747,23)),Promise.resolve().then(c.t.bind(c,56676,23)),Promise.resolve().then(c.bind(c,97225))},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},42931:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,58671,23)),Promise.resolve().then(c.t.bind(c,56542,23)),Promise.resolve().then(c.t.bind(c,88248,23)),Promise.resolve().then(c.t.bind(c,49743,23)),Promise.resolve().then(c.t.bind(c,96231,23)),Promise.resolve().then(c.t.bind(c,10959,23)),Promise.resolve().then(c.t.bind(c,72041,23)),Promise.resolve().then(c.t.bind(c,95094,23)),Promise.resolve().then(c.t.bind(c,67487,23))},47570:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>h,metadata:()=>g});var d=c(5939),e=c(50157),f=c.n(e);c(14276);let g={title:"نظام إدارة الموارد البشرية - مرجب",description:"نظام شامل لإدارة الموارد البشرية والقوة العمومية",keywords:["الموارد البشرية","إدارة الموظفين","القوة العمومية","مرجب"],authors:[{name:"Mergeb HR System"}],viewport:"width=device-width, initial-scale=1",robots:"noindex, nofollow"};function h({children:a}){return(0,d.jsx)("html",{lang:"ar",dir:"rtl",children:(0,d.jsx)("body",{className:`${f().variable} font-sans antialiased bg-gradient-to-br from-secondary-50 via-white to-primary-50 min-h-screen`,children:(0,d.jsx)("div",{className:"relative",children:a})})})}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67815:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(25459).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\import\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\mergeb HR\\apps\\personnel-frontend\\src\\app\\employees\\import\\page.tsx","default")},71159:(a,b,c)=>{"use strict";var d=c(30291);c.o(d,"useParams")&&c.d(b,{useParams:function(){return d.useParams}}),c.o(d,"useRouter")&&c.d(b,{useRouter:function(){return d.useRouter}})},77533:(a,b,c)=>{Promise.resolve().then(c.bind(c,67815))},78454:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>D.a,__next_app__:()=>J,handler:()=>L,pages:()=>I,routeModule:()=>K,tree:()=>H});var d=c(73653),e=c(97714),f=c(85250),g=c(37587),h=c(22369),i=c(1889),j=c(96232),k=c(22841),l=c(46537),m=c(46027),n=c(78559),o=c(75928),p=c(19374),q=c(65971),r=c(261),s=c(79898),t=c(32967),u=c(26713),v=c(40139),w=c(14248),x=c(59580),y=c(57749),z=c(53123),A=c(89745),B=c(86439),C=c(58671),D=c.n(C),E=c(18283),F=c(39818),G={};for(let a in E)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(G[a]=()=>E[a]);c.d(b,G);let H={children:["",{children:["employees",{children:["import",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,67815)),"C:\\Users\\<USER>\\Desktop\\mergeb HR\\apps\\personnel-frontend\\src\\app\\employees\\import\\page.tsx"]}]},{}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,9699))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,47570)),"C:\\Users\\<USER>\\Desktop\\mergeb HR\\apps\\personnel-frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,58671,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,17983,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,15034,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,54693,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,9699))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,I=["C:\\Users\\<USER>\\Desktop\\mergeb HR\\apps\\personnel-frontend\\src\\app\\employees\\import\\page.tsx"],J={require:c,loadChunk:()=>Promise.resolve()},K=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/employees/import/page",pathname:"/employees/import",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:H},distDir:".next",relativeProjectDir:""});async function L(a,b,d){var C;let G="/employees/import/page";"/index"===G&&(G="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await K.prepare(a,b,{srcPage:G,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:_,isDraftMode:aa,resolvedPathname:ab,revalidateOnlyGenerated:ac,routerServerContext:ad,nextConfig:ae,interceptionRoutePatterns:af}=O,ag=S.pathname||"/",ah=(0,r.normalizeAppPath)(G),{isOnDemandRevalidate:ai}=O,aj=K.match(ag,_),ak=!!_.routes[ab],al=!!(aj||ak||_.routes[ah]),am=a.headers["user-agent"]||"",an=(0,u.getBotType)(am),ao=(0,p.isHtmlBotRequest)(a),ap=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],aq=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],ar=(0,s.getIsPossibleServerAction)(a),as=(0,m.checkIsAppPPREnabled)(ae.experimental.ppr)&&(null==(C=_.routes[ah]??_.dynamicRoutes[ah])?void 0:C.renderingMode)==="PARTIALLY_STATIC",at=!1,au=!1,av=as?M:void 0,aw=as&&aq&&!ap,ax=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ay=!am||(0,p.shouldServeStreamingMetadata)(am,ae.htmlLimitedBots);ao&&as&&(al=!1,ay=!1);let az=!0===K.isDev||!al||"string"==typeof M||aw,aA=ao&&as,aB=null;aa||!al||az||ar||av||aw||(aB=ab);let aC=aB;!aC&&K.isDev&&(aC=ab),K.isDev||aa||!al||!aq||aw||(0,k.d)(a.headers);let aD={...E,tree:H,pages:I,GlobalError:D(),handler:L,routeModule:K,__next_app__:J};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:G,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aE=a.method||"GET",aF=(0,g.getTracer)(),aG=aF.getActiveScopeSpan();try{let f=K.getVaryHeader(ab,af);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return K.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aF.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aE} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aE} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ah,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aD,Component:(0,j.T)(aD),params:R,routeModule:K,page:G,postponed:f,shouldWaitOnAllReady:aA,serveStreamingMetadata:ay,supportsDynamicResponse:"string"==typeof f||az,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ad?void 0:ad.setIsrStatus,dir:c(33873).join(process.cwd(),K.relativeProjectDir),isDraftMode:aa,isRevalidate:al&&!f&&!aw,botType:an,isOnDemandRevalidate:ai,isPossibleServerAction:ar,assetPrefix:ae.assetPrefix,nextConfigOutput:ae.output,crossOrigin:ae.crossOrigin,trailingSlash:ae.trailingSlash,previewProps:_.preview,deploymentId:ae.deploymentId,enableTainting:ae.experimental.taint,htmlLimitedBots:ae.htmlLimitedBots,devtoolSegmentExplorer:ae.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ae.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ae.experimental.cacheLife,basePath:ae.basePath,serverActions:ae.experimental.serverActions,...at?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:at}:{},experimental:{isRoutePPREnabled:as,expireTime:ae.expireTime,staleTimes:ae.experimental.staleTimes,cacheComponents:!!ae.experimental.cacheComponents,clientSegmentCache:!!ae.experimental.clientSegmentCache,clientParamParsing:!!ae.experimental.clientParamParsing,dynamicOnHover:!!ae.experimental.dynamicOnHover,inlineCss:!!ae.experimental.inlineCss,authInterrupts:!!ae.experimental.authInterrupts,clientTraceMetadata:ae.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>K.onRequestError(a,b,d,ad),err:(0,h.getRequestMeta)(a,"invokeError"),dev:K.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,al&&(null==n?void 0:n.revalidate)===0&&!K.isDev&&!as){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${ab}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===K.isDev,l=c||b.writableEnded;if(ai&&ac&&!f&&!N)return(null==ad?void 0:ad.render404)?await ad.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(aj&&(j=(0,w.parseFallbackField)(aj.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(am)&&(!as||ao)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ai=!0),ai&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aC&&!l&&!aa&&T&&(k||!ak)){let b;if((k||aj)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(as&&!aq){let c="string"==typeof(null==aj?void 0:aj.fallback)?aj.fallback:k?ah:null;if(b=await K.handleResponse({cacheKey:c,req:a,nextConfig:ae,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:_,isRoutePPREnabled:as,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||au?(0,n.u)(ah):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ai||g||!av?void 0:av;if(at&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&as&&((0,h.getRequestMeta)(a,"renderFallbackShell")||au)?(0,n.u)(ag):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await K.handleResponse({cacheKey:aB,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ai,isRoutePPREnabled:as,req:a,nextConfig:ae,prerenderManifest:_,waitUntil:d.waitUntil});if(aa&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),K.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aB)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;al&&!aw&&(!p||ap)&&(N||b.setHeader("x-nextjs-cache",ai?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(av)l={revalidate:0,expire:void 0};else if(N&&aq&&!ap&&as)l={revalidate:0,expire:void 0};else if(!K.isDev)if(aa)l={revalidate:0,expire:void 0};else if(al){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ae.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof ax&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&al&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(ax);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ae.generateEtags,poweredByHeader:ae.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ae.generateEtags,poweredByHeader:ae.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&av)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&al||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&al&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||aq&&as||(b.statusCode=q.status),!N&&q.status&&F.RedirectStatusCode[q.status]&&aq&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),aq&&!aa){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ae.generateEtags,poweredByHeader:ae.poweredByHeader,result:q.html,cacheControl:aw?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ae.generateEtags,poweredByHeader:ae.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||aq)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ae.generateEtags,poweredByHeader:ae.poweredByHeader,result:u,cacheControl:n.cacheControl});if(at)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ae.generateEtags,poweredByHeader:ae.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ae.generateEtags,poweredByHeader:ae.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aG)return await aF.withPropagatedContext(a.headers,()=>aF.trace(i.BaseServerSpan.handleRequest,{spanName:`${aE} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aE,"http.target":a.url}},p));await p(aG)}catch(b){throw aG||b instanceof B.NoFallbackError||await K.onRequestError(a,b,{routerKind:"App Router",routePath:G,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:al,isOnDemandRevalidate:ai})},ad),b}}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[635,533],()=>b(b.s=78454));module.exports=c})();
'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

interface Rank {
  key: string;
  label: string;
  category: 'officer' | 'nco' | 'employee';
  order: number;
}

interface Unit {
  id: number;
  name: string;
}

export default function NewEmployeePage() {
  const [formData, setFormData] = useState({
    name: '',
    rankKey: '',
    militaryNumber: '',
    nationalId: '',
    unitId: '',
    // Personal Information
    birthDate: '',
    birthPlace: '',
    motherName: '',
    maritalStatus: '',
    // Work Information
    appointmentDate: '',
    lastPromotionDate: '',
    // Bank Information
    bankName: '',
    bankBranch: '',
    bankAccount: '',
    // Leave Information
    leaveBalance: '',
    leaveType: 'سنوية',
    // Employee Status
    employeeStatus: 'مستمر',
    // Education Information
    qualification: '',
    qualificationDate: '',
    // Photo
    photo: null as File | null
  });
  const [ranks, setRanks] = useState<Rank[]>([]);
  const [units, setUnits] = useState<Unit[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const router = useRouter();

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (!token) {
      router.push('/login');
      return;
    }

    fetchData(token);
  }, [router]);

  const fetchData = async (token: string) => {
    try {
      // Fetch ranks
      const ranksResponse = await fetch('http://127.0.0.1:4001/ranks', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (ranksResponse.ok) {
        const ranksData = await ranksResponse.json();
        setRanks(ranksData);
      }

      // Fetch units
      const unitsResponse = await fetch('http://127.0.0.1:4001/units', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (unitsResponse.ok) {
        const unitsData = await ranksResponse.json();
        // Mock units data since API returns array of strings
        setUnits([
          { id: 1, name: 'الخمس' },
          { id: 2, name: 'سوق الخميس' },
          { id: 3, name: 'كعام' },
          { id: 4, name: 'الدوريات' },
          { id: 5, name: 'غنيمة' },
        ]);
      }
    } catch (err) {
      setError('خطأ في تحميل البيانات');
    }
  };

  const getSelectedRank = () => {
    return ranks.find(r => r.key === formData.rankKey);
  };

  const getMilitaryNumberPlaceholder = () => {
    const rank = getSelectedRank();
    if (!rank) return 'اختر الرتبة أولاً';
    
    switch (rank.category) {
      case 'officer': return 'بلا';
      case 'nco': return 'أدخل الرقم العسكري';
      case 'employee': return 'موظف';
      default: return '';
    }
  };

  const validateMilitaryNumber = () => {
    const rank = getSelectedRank();
    if (!rank) return 'يجب اختيار الرتبة أولاً';

    switch (rank.category) {
      case 'officer':
        if (formData.militaryNumber !== 'بلا') {
          return 'بالنسبة للضباط يجب أن تكون قيمة الرقم العسكري "بلا"';
        }
        break;
      case 'nco':
        if (!formData.militaryNumber || formData.militaryNumber.trim() === '') {
          return 'الرقم العسكري إجباري لضباط الصف';
        }
        if (formData.militaryNumber === 'بلا' || formData.militaryNumber === 'موظف') {
          return 'أدخل رقماً عسكرياً صحيحاً لضباط الصف';
        }
        break;
      case 'employee':
        if (formData.militaryNumber !== 'موظف') {
          return 'بالنسبة للموظفين يجب أن تكون قيمة الرقم العسكري "موظف"';
        }
        break;
    }
    return '';
  };

  const handleRankChange = (rankKey: string) => {
    const rank = ranks.find(r => r.key === rankKey);
    let militaryNumber = '';
    
    if (rank) {
      switch (rank.category) {
        case 'officer':
          militaryNumber = 'بلا';
          break;
        case 'employee':
          militaryNumber = 'موظف';
          break;
        case 'nco':
          militaryNumber = '';
          break;
      }
    }

    setFormData(prev => ({
      ...prev,
      rankKey,
      militaryNumber
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    // Validate national ID
    if (formData.nationalId.length !== 12) {
      setError('الرقم الوطني يجب أن يكون 12 رقماً');
      setLoading(false);
      return;
    }

    // Validate bank account if provided
    if (formData.bankAccount && formData.bankAccount.length !== 15) {
      setError('رقم الحساب المصرفي يجب أن يكون 15 رقماً');
      setLoading(false);
      return;
    }

    // Validate military number
    const militaryNumberError = validateMilitaryNumber();
    if (militaryNumberError) {
      setError(militaryNumberError);
      setLoading(false);
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://127.0.0.1:4001/employees', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          name: formData.name,
          rankKey: formData.rankKey,
          militaryNumber: formData.militaryNumber,
          nationalId: formData.nationalId,
          unitId: formData.unitId ? parseInt(formData.unitId) : undefined,
          birthDate: formData.birthDate || null,
          birthPlace: formData.birthPlace || null,
          motherName: formData.motherName || null,
          maritalStatus: formData.maritalStatus || null,
          appointmentDate: formData.appointmentDate || null,
          lastPromotionDate: formData.lastPromotionDate || null,
          bankName: formData.bankName || null,
          bankBranch: formData.bankBranch || null,
          bankAccount: formData.bankAccount || null,
          leaveBalance: formData.leaveBalance ? parseInt(formData.leaveBalance) : 0,
          leaveType: formData.leaveType || null,
          employeeStatus: formData.employeeStatus || null,
          qualification: formData.qualification || null,
          qualificationDate: formData.qualificationDate || null,
        }),
      });

      if (response.ok) {
        setSuccess('تم إضافة الموظف بنجاح');
        setTimeout(() => {
          router.push('/employees');
        }, 2000);
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'خطأ في إضافة الموظف');
      }
    } catch (err) {
      setError('خطأ في الاتصال بالخادم');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('token');
    router.push('/login');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4 space-x-reverse">
              <h1 className="text-xl font-semibold text-gray-900">إضافة موظف جديد</h1>
              <Link
                href="/employees"
                className="text-blue-600 hover:text-blue-800 text-sm"
              >
                ← العودة لقائمة الموظفين
              </Link>
            </div>
            <button
              onClick={handleLogout}
              className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors text-sm"
            >
              تسجيل الخروج
            </button>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Employee Status Card - Right Side */}
          <div className="lg:col-span-1 order-first lg:order-last">
            <div className="bg-white rounded-lg shadow-lg p-6 sticky top-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">حالة الموظف</h3>
              <div className="space-y-3">
                {['مستمر', 'اجازة', 'موقوف', 'غائب عن العمل'].map((status) => (
                  <label key={status} className="flex items-center">
                    <input
                      type="radio"
                      name="employeeStatus"
                      value={status}
                      checked={formData.employeeStatus === status}
                      onChange={(e) => setFormData(prev => ({ ...prev, employeeStatus: e.target.value }))}
                      className="ml-2 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-gray-700">{status}</span>
                  </label>
                ))}
              </div>

              {/* Photo Upload */}
              <div className="mt-6 pt-6 border-t">
                <h4 className="text-md font-semibold text-gray-900 mb-3">الصورة الشخصية</h4>
                <div className="space-y-3">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={(e) => {
                      const file = e.target.files?.[0] || null;
                      setFormData(prev => ({ ...prev, photo: file }));
                    }}
                    className="w-full text-sm text-gray-500 file:ml-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                  />
                  {formData.photo && (
                    <div className="text-sm text-green-600">
                      تم اختيار: {formData.photo.name}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Main Form - Left Side */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">إضافة موظف جديد</h2>
              <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                {error}
              </div>
            )}

            {success && (
              <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
                {success}
              </div>
            )}

            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                الاسم الكامل *
              </label>
              <input
                type="text"
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
            </div>

            <div>
              <label htmlFor="rankKey" className="block text-sm font-medium text-gray-700 mb-2">
                الرتبة *
              </label>
              <select
                id="rankKey"
                value={formData.rankKey}
                onChange={(e) => handleRankChange(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              >
                <option value="">اختر الرتبة</option>
                <optgroup label="الضباط">
                  {ranks.filter(r => r.category === 'officer').map(rank => (
                    <option key={rank.key} value={rank.key}>{rank.label}</option>
                  ))}
                </optgroup>
                <optgroup label="ضباط الصف">
                  {ranks.filter(r => r.category === 'nco').map(rank => (
                    <option key={rank.key} value={rank.key}>{rank.label}</option>
                  ))}
                </optgroup>
                <optgroup label="الموظفون">
                  {ranks.filter(r => r.category === 'employee').map(rank => (
                    <option key={rank.key} value={rank.key}>{rank.label}</option>
                  ))}
                </optgroup>
              </select>
            </div>

            <div>
              <label htmlFor="militaryNumber" className="block text-sm font-medium text-gray-700 mb-2">
                الرقم العسكري *
              </label>
              <input
                type="text"
                id="militaryNumber"
                value={formData.militaryNumber}
                onChange={(e) => setFormData(prev => ({ ...prev, militaryNumber: e.target.value }))}
                placeholder={getMilitaryNumberPlaceholder()}
                title="الرقم العسكري"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
                readOnly={getSelectedRank()?.category === 'officer' || getSelectedRank()?.category === 'employee'}
              />
              {getSelectedRank() && (
                <p className="mt-1 text-sm text-gray-500">
                  {getSelectedRank()?.category === 'officer' && 'للضباط: القيمة "بلا" تلقائياً'}
                  {getSelectedRank()?.category === 'nco' && 'لضباط الصف: أدخل الرقم العسكري'}
                  {getSelectedRank()?.category === 'employee' && 'للموظفين: القيمة "موظف" تلقائياً'}
                </p>
              )}
            </div>

            <div>
              <label htmlFor="nationalId" className="block text-sm font-medium text-gray-700 mb-2">
                الرقم الوطني * (12 رقم)
              </label>
              <input
                type="text"
                id="nationalId"
                value={formData.nationalId}
                onChange={(e) => setFormData(prev => ({ ...prev, nationalId: e.target.value.replace(/\D/g, '').slice(0, 12) }))}
                placeholder="123456789012"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono"
                required
                maxLength={12}
              />
              <p className="mt-1 text-sm text-gray-500">
                {formData.nationalId.length}/12 رقم
              </p>
            </div>

            <div>
              <label htmlFor="unitId" className="block text-sm font-medium text-gray-700 mb-2">
                الوحدة
              </label>
              <select
                id="unitId"
                value={formData.unitId}
                onChange={(e) => setFormData(prev => ({ ...prev, unitId: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">اختر الوحدة (اختياري)</option>
                {units.map(unit => (
                  <option key={unit.id} value={unit.id}>{unit.name}</option>
                ))}
              </select>
            </div>

            {/* Personal Information Section */}
            <div className="border-t pt-6 mt-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">البيانات الشخصية</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="birthDate" className="block text-sm font-medium text-gray-700 mb-2">
                    تاريخ الميلاد
                  </label>
                  <input
                    type="date"
                    id="birthDate"
                    value={formData.birthDate}
                    onChange={(e) => setFormData(prev => ({ ...prev, birthDate: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label htmlFor="birthPlace" className="block text-sm font-medium text-gray-700 mb-2">
                    مكان الميلاد
                  </label>
                  <input
                    type="text"
                    id="birthPlace"
                    value={formData.birthPlace}
                    onChange={(e) => setFormData(prev => ({ ...prev, birthPlace: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="مثال: طرابلس"
                  />
                </div>

                <div>
                  <label htmlFor="motherName" className="block text-sm font-medium text-gray-700 mb-2">
                    اسم الأم
                  </label>
                  <input
                    type="text"
                    id="motherName"
                    value={formData.motherName}
                    onChange={(e) => setFormData(prev => ({ ...prev, motherName: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="اسم الأم الثلاثي"
                  />
                </div>

                <div>
                  <label htmlFor="maritalStatus" className="block text-sm font-medium text-gray-700 mb-2">
                    الحالة الاجتماعية
                  </label>
                  <select
                    id="maritalStatus"
                    value={formData.maritalStatus}
                    onChange={(e) => setFormData(prev => ({ ...prev, maritalStatus: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">اختر الحالة الاجتماعية</option>
                    <option value="أعزب">أعزب</option>
                    <option value="متزوج">متزوج</option>
                    <option value="مطلق">مطلق</option>
                    <option value="أرمل">أرمل</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Work Information Section */}
            <div className="border-t pt-6 mt-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">بيانات العمل</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="appointmentDate" className="block text-sm font-medium text-gray-700 mb-2">
                    تاريخ التعيين
                  </label>
                  <input
                    type="date"
                    id="appointmentDate"
                    value={formData.appointmentDate}
                    onChange={(e) => setFormData(prev => ({ ...prev, appointmentDate: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="اختر تاريخ التعيين"
                  />
                </div>

                <div>
                  <label htmlFor="lastPromotionDate" className="block text-sm font-medium text-gray-700 mb-2">
                    تاريخ آخر ترقية
                  </label>
                  <input
                    type="date"
                    id="lastPromotionDate"
                    value={formData.lastPromotionDate}
                    onChange={(e) => setFormData(prev => ({ ...prev, lastPromotionDate: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>

            {/* Bank Information Section */}
            <div className="border-t pt-6 mt-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">بيانات المصرف</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="bankName" className="block text-sm font-medium text-gray-700 mb-2">
                    اسم المصرف
                  </label>
                  <input
                    type="text"
                    id="bankName"
                    value={formData.bankName}
                    onChange={(e) => setFormData(prev => ({ ...prev, bankName: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="مثال: مصرف الجمهورية"
                  />
                </div>

                <div>
                  <label htmlFor="bankBranch" className="block text-sm font-medium text-gray-700 mb-2">
                    فرع المصرف
                  </label>
                  <input
                    type="text"
                    id="bankBranch"
                    value={formData.bankBranch}
                    onChange={(e) => setFormData(prev => ({ ...prev, bankBranch: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="مثال: فرع الخمس"
                  />
                </div>

                <div className="md:col-span-2">
                  <label htmlFor="bankAccount" className="block text-sm font-medium text-gray-700 mb-2">
                    رقم الحساب المصرفي (15 رقم)
                  </label>
                  <input
                    type="text"
                    id="bankAccount"
                    value={formData.bankAccount}
                    onChange={(e) => setFormData(prev => ({ ...prev, bankAccount: e.target.value.replace(/\D/g, '').slice(0, 15) }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono"
                    placeholder="***************"
                    maxLength={15}
                  />
                  <p className="mt-1 text-sm text-gray-500">
                    {formData.bankAccount.length}/15 رقم
                  </p>
                </div>
              </div>
            </div>

            {/* Leave Information Section */}
            <div className="border-t pt-6 mt-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">الإجازات</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="leaveBalance" className="block text-sm font-medium text-gray-700 mb-2">
                    رصيد الإجازات (بالأيام)
                  </label>
                  <input
                    type="number"
                    id="leaveBalance"
                    value={formData.leaveBalance}
                    onChange={(e) => setFormData(prev => ({ ...prev, leaveBalance: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="30"
                    min="0"
                    max="365"
                  />
                </div>

                <div>
                  <label htmlFor="leaveType" className="block text-sm font-medium text-gray-700 mb-2">
                    نوع الإجازة الافتراضي
                  </label>
                  <select
                    id="leaveType"
                    value={formData.leaveType}
                    onChange={(e) => setFormData(prev => ({ ...prev, leaveType: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="سنوية">سنوية</option>
                    <option value="مرضية">مرضية</option>
                    <option value="بدون مرتب">بدون مرتب</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Education Information Section */}
            <div className="border-t pt-6 mt-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">البيانات الدراسية</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="qualification" className="block text-sm font-medium text-gray-700 mb-2">
                    المؤهل العلمي
                  </label>
                  <select
                    id="qualification"
                    value={formData.qualification}
                    onChange={(e) => setFormData(prev => ({ ...prev, qualification: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">اختر المؤهل العلمي</option>
                    <option value="ابتدائي">ابتدائي</option>
                    <option value="إعدادي">إعدادي</option>
                    <option value="ثانوي">ثانوي</option>
                    <option value="دبلوم متوسط">دبلوم متوسط</option>
                    <option value="دبلوم عالي">دبلوم عالي</option>
                    <option value="بكالوريوس">بكالوريوس</option>
                    <option value="ماجستير">ماجستير</option>
                    <option value="دكتوراه">دكتوراه</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="qualificationDate" className="block text-sm font-medium text-gray-700 mb-2">
                    تاريخ الحصول على المؤهل
                  </label>
                  <input
                    type="date"
                    id="qualificationDate"
                    value={formData.qualificationDate}
                    onChange={(e) => setFormData(prev => ({ ...prev, qualificationDate: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>

            <div className="flex space-x-4 space-x-reverse pt-6">
              <button
                type="submit"
                disabled={loading}
                className="flex-1 bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed font-medium"
              >
                {loading ? 'جاري الحفظ...' : 'حفظ الموظف'}
              </button>
              <Link
                href="/employees"
                className="flex-1 bg-gray-300 text-gray-700 py-3 px-4 rounded-lg hover:bg-gray-400 transition-colors text-center font-medium"
              >
                إلغاء
              </Link>
            </div>
              </form>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

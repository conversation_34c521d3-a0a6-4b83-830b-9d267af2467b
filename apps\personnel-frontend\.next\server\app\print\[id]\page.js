(()=>{var a={};a.id=780,a.ids=[780],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},640:(a,b,c)=>{let d=c(18805),e=c(96892),f=c(710),g=c(82961),h=c(99278),i=c(8561),j=c(66085),k=c(33969),l=c(30277),m=c(69168),n=c(5734),o=c(34809),p=c(51768);function q(a,b,c){let d,e,f=a.size,g=n.getEncodedBits(b,c);for(d=0;d<15;d++)e=(g>>d&1)==1,d<6?a.set(d,8,e,!0):d<8?a.set(d+1,8,e,!0):a.set(f-15+d,8,e,!0),d<8?a.set(8,f-d-1,e,!0):d<9?a.set(8,15-d-1+1,e,!0):a.set(8,15-d-1,e,!0);a.set(f-8,8,1,!0)}b.create=function(a,b){let c,n;if(void 0===a||""===a)throw Error("No input text");let r=e.M;return void 0!==b&&(r=e.from(b.errorCorrectionLevel,e.M),c=m.from(b.version),n=j.from(b.maskPattern),b.toSJISFunc&&d.setToSJISFunction(b.toSJISFunc)),function(a,b,c,e){let n;if(Array.isArray(a))n=p.fromArray(a);else if("string"==typeof a){let d=b;if(!d){let b=p.rawSplit(a);d=m.getBestVersionForData(b,c)}n=p.fromString(a,d||40)}else throw Error("Invalid data");let r=m.getBestVersionForData(n,c);if(!r)throw Error("The amount of data is too big to be stored in a QR Code");if(b){if(b<r)throw Error("\nThe chosen QR Code version cannot contain this amount of data.\nMinimum version required to store current data is: "+r+".\n")}else b=r;let s=function(a,b,c){let e=new f;c.forEach(function(b){e.put(b.mode.bit,4),e.put(b.getLength(),o.getCharCountIndicator(b.mode,a)),b.write(e)});let g=(d.getSymbolTotalCodewords(a)-k.getTotalCodewordsCount(a,b))*8;for(e.getLengthInBits()+4<=g&&e.put(0,4);e.getLengthInBits()%8!=0;)e.putBit(0);let h=(g-e.getLengthInBits())/8;for(let a=0;a<h;a++)e.put(a%2?17:236,8);return function(a,b,c){let e,f,g=d.getSymbolTotalCodewords(b),h=g-k.getTotalCodewordsCount(b,c),i=k.getBlocksCount(b,c),j=g%i,m=i-j,n=Math.floor(g/i),o=Math.floor(h/i),p=o+1,q=n-o,r=new l(q),s=0,t=Array(i),u=Array(i),v=0,w=new Uint8Array(a.buffer);for(let a=0;a<i;a++){let b=a<m?o:p;t[a]=w.slice(s,s+b),u[a]=r.encode(t[a]),s+=b,v=Math.max(v,b)}let x=new Uint8Array(g),y=0;for(e=0;e<v;e++)for(f=0;f<i;f++)e<t[f].length&&(x[y++]=t[f][e]);for(e=0;e<q;e++)for(f=0;f<i;f++)x[y++]=u[f][e];return x}(e,a,b)}(b,c,n),t=new g(d.getSymbolSize(b));!function(a,b){let c=a.size,d=i.getPositions(b);for(let b=0;b<d.length;b++){let e=d[b][0],f=d[b][1];for(let b=-1;b<=7;b++)if(!(e+b<=-1)&&!(c<=e+b))for(let d=-1;d<=7;d++)f+d<=-1||c<=f+d||(b>=0&&b<=6&&(0===d||6===d)||d>=0&&d<=6&&(0===b||6===b)||b>=2&&b<=4&&d>=2&&d<=4?a.set(e+b,f+d,!0,!0):a.set(e+b,f+d,!1,!0))}}(t,b);let u=t.size;for(let a=8;a<u-8;a++){let b=a%2==0;t.set(a,6,b,!0),t.set(6,a,b,!0)}return!function(a,b){let c=h.getPositions(b);for(let b=0;b<c.length;b++){let d=c[b][0],e=c[b][1];for(let b=-2;b<=2;b++)for(let c=-2;c<=2;c++)-2===b||2===b||-2===c||2===c||0===b&&0===c?a.set(d+b,e+c,!0,!0):a.set(d+b,e+c,!1,!0)}}(t,b),q(t,c,0),b>=7&&function(a,b){let c,d,e,f=a.size,g=m.getEncodedBits(b);for(let b=0;b<18;b++)c=Math.floor(b/3),d=b%3+f-8-3,e=(g>>b&1)==1,a.set(c,d,e,!0),a.set(d,c,e,!0)}(t,b),!function(a,b){let c=a.size,d=-1,e=c-1,f=7,g=0;for(let h=c-1;h>0;h-=2)for(6===h&&h--;;){for(let c=0;c<2;c++)if(!a.isReserved(e,h-c)){let d=!1;g<b.length&&(d=(b[g]>>>f&1)==1),a.set(e,h-c,d),-1==--f&&(g++,f=7)}if((e+=d)<0||c<=e){e-=d,d=-d;break}}}(t,s),isNaN(e)&&(e=j.getBestMask(t,q.bind(null,t,c))),j.applyMask(e,t),q(t,c,e),{modules:t,version:b,errorCorrectionLevel:c,maskPattern:e,segments:n}}(a,c,r,n)}},710:a=>{function b(){this.buffer=[],this.length=0}b.prototype={get:function(a){let b=Math.floor(a/8);return(this.buffer[b]>>>7-a%8&1)==1},put:function(a,b){for(let c=0;c<b;c++)this.putBit((a>>>b-c-1&1)==1)},getLengthInBits:function(){return this.length},putBit:function(a){let b=Math.floor(this.length/8);this.buffer.length<=b&&this.buffer.push(0),a&&(this.buffer[b]|=128>>>this.length%8),this.length++}},a.exports=b},1115:()=>{},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4794:(a,b,c)=>{let d=c(34809),e=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function f(a){this.mode=d.ALPHANUMERIC,this.data=a}f.getBitsLength=function(a){return 11*Math.floor(a/2)+a%2*6},f.prototype.getLength=function(){return this.data.length},f.prototype.getBitsLength=function(){return f.getBitsLength(this.data.length)},f.prototype.write=function(a){let b;for(b=0;b+2<=this.data.length;b+=2){let c=45*e.indexOf(this.data[b]);c+=e.indexOf(this.data[b+1]),a.put(c,11)}this.data.length%2&&a.put(e.indexOf(this.data[b]),6)},a.exports=f},5734:(a,b,c)=>{let d=c(18805),e=d.getBCHDigit(1335);b.getEncodedBits=function(a,b){let c=a.bit<<3|b,f=c<<10;for(;d.getBCHDigit(f)-e>=0;)f^=1335<<d.getBCHDigit(f)-e;return(c<<10|f)^21522}},7355:(a,b,c)=>{"use strict";let d=c(28354),e=c(27910),f=c(34552),g=c(74345),h=c(37027),i=b.O=function(a){e.call(this),a=a||{},this.width=0|a.width,this.height=0|a.height,this.data=this.width>0&&this.height>0?Buffer.alloc(4*this.width*this.height):null,a.fill&&this.data&&this.data.fill(0),this.gamma=0,this.readable=this.writable=!0,this._parser=new f(a),this._parser.on("error",this.emit.bind(this,"error")),this._parser.on("close",this._handleClose.bind(this)),this._parser.on("metadata",this._metadata.bind(this)),this._parser.on("gamma",this._gamma.bind(this)),this._parser.on("parsed",(function(a){this.data=a,this.emit("parsed",a)}).bind(this)),this._packer=new g(a),this._packer.on("data",this.emit.bind(this,"data")),this._packer.on("end",this.emit.bind(this,"end")),this._parser.on("close",this._handleClose.bind(this)),this._packer.on("error",this.emit.bind(this,"error"))};d.inherits(i,e),i.sync=h,i.prototype.pack=function(){return this.data&&this.data.length?process.nextTick((function(){this._packer.pack(this.data,this.width,this.height,this.gamma)}).bind(this)):this.emit("error","No data provided"),this},i.prototype.parse=function(a,b){if(b){let a,c;a=(function(a){this.removeListener("error",c),this.data=a,b(null,this)}).bind(this),c=(function(c){this.removeListener("parsed",a),b(c,null)}).bind(this),this.once("parsed",a),this.once("error",c)}return this.end(a),this},i.prototype.write=function(a){return this._parser.write(a),!0},i.prototype.end=function(a){this._parser.end(a)},i.prototype._metadata=function(a){this.width=a.width,this.height=a.height,this.emit("metadata",a)},i.prototype._gamma=function(a){this.gamma=a},i.prototype._handleClose=function(){this._parser.writable||this._packer.readable||this.emit("close")},i.bitblt=function(a,b,c,d,e,f,g,h){if(d|=0,e|=0,f|=0,g|=0,h|=0,(c|=0)>a.width||d>a.height||c+e>a.width||d+f>a.height)throw Error("bitblt reading outside image");if(g>b.width||h>b.height||g+e>b.width||h+f>b.height)throw Error("bitblt writing outside image");for(let i=0;i<f;i++)a.data.copy(b.data,(h+i)*b.width+g<<2,(d+i)*a.width+c<<2,(d+i)*a.width+c+e<<2)},i.prototype.bitblt=function(a,b,c,d,e,f,g){return i.bitblt(this,a,b,c,d,e,f,g),this},i.adjustGamma=function(a){if(a.gamma){for(let b=0;b<a.height;b++)for(let c=0;c<a.width;c++){let d=a.width*b+c<<2;for(let b=0;b<3;b++){let c=a.data[d+b]/255;c=Math.pow(c,1/2.2/a.gamma),a.data[d+b]=Math.round(255*c)}}a.gamma=0}},i.prototype.adjustGamma=function(){i.adjustGamma(this)}},7719:(a,b)=>{let c="\x1b[37m",d="\x1b[30m",e="\x1b[0m",f="\x1b[47m"+d,g="\x1b[40m"+c,h=function(a,b,c,d){let e=b+1;return c>=e||d>=e||d<-1||c<-1?"0":c>=b||d>=b||d<0||c<0?"1":a[d*b+c]?"2":"1"},i=function(a,b,c,d){return h(a,b,c,d)+h(a,b,c,d+1)};b.render=function(a,b,h){var j,k;let l=a.modules.size,m=a.modules.data,n=!!(b&&b.inverse),o=b&&b.inverse?g:f,p={"00":e+" "+o,"01":e+(j=n?d:c)+"▄"+o,"02":e+(k=n?c:d)+"▄"+o,10:e+j+"▀"+o,11:" ",12:"▄",20:e+k+"▀"+o,21:"▀",22:"█"},q=e+"\n"+o,r=o;for(let a=-1;a<l+1;a+=2){for(let b=-1;b<l;b++)r+=p[i(m,l,b,a)];r+=p[i(m,l,l,a)]+q}return r+=e,"function"==typeof h&&h(null,r),r}},8561:(a,b,c)=>{let d=c(18805).getSymbolSize;b.getPositions=function(a){let b=d(a);return[[0,0],[b-7,0],[0,b-7]]}},9699:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(43560);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},14276:()=>{},14590:(a,b,c)=>{let d=c(89097);b.render=function(a,b,c){var e;let f=c,g=b;void 0!==f||b&&b.getContext||(f=b,b=void 0),b||(g=function(){try{return document.createElement("canvas")}catch(a){throw Error("You need to specify a canvas element")}}()),f=d.getOptions(f);let h=d.getImageWidth(a.modules.size,f),i=g.getContext("2d"),j=i.createImageData(h,h);return d.qrToImageData(j.data,a,f),e=g,i.clearRect(0,0,e.width,e.height),e.style||(e.style={}),e.height=h,e.width=h,e.style.height=h+"px",e.style.width=h+"px",i.putImageData(j,0,0),g},b.renderToDataURL=function(a,c,d){let e=d;void 0!==e||c&&c.getContext||(e=c,c=void 0),e||(e={});let f=b.render(a,c,e),g=e.type||"image/png",h=e.rendererOpts||{};return f.toDataURL(g,h.quality)}},17723:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(25459).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\print\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\mergeb HR\\apps\\personnel-frontend\\src\\app\\print\\[id]\\page.tsx","default")},18695:(a,b,c)=>{let d=c(29021),e=c(7355).O,f=c(89097);b.render=function(a,b){let c=f.getOptions(b),d=c.rendererOpts,g=f.getImageWidth(a.modules.size,c);d.width=g,d.height=g;let h=new e(d);return f.qrToImageData(h.data,a,c),h},b.renderToDataURL=function(a,c,d){void 0===d&&(d=c,c=void 0),b.renderToBuffer(a,c,function(a,b){a&&d(a);let c="data:image/png;base64,";c+=b.toString("base64"),d(null,c)})},b.renderToBuffer=function(a,c,d){void 0===d&&(d=c,c=void 0);let e=b.render(a,c),f=[];e.on("error",d),e.on("data",function(a){f.push(a)}),e.on("end",function(){d(null,Buffer.concat(f))}),e.pack()},b.renderToFile=function(a,c,e,f){void 0===f&&(f=e,e=void 0);let g=!1,h=(...a)=>{g||(g=!0,f.apply(null,a))},i=d.createWriteStream(a);i.on("error",h),i.on("close",h),b.renderToFileStream(i,c,e)},b.renderToFileStream=function(a,c,d){b.render(c,d).pack().pipe(a)}},18805:(a,b)=>{let c,d=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];b.getSymbolSize=function(a){if(!a)throw Error('"version" cannot be null or undefined');if(a<1||a>40)throw Error('"version" should be in range from 1 to 40');return 4*a+17},b.getSymbolTotalCodewords=function(a){return d[a]},b.getBCHDigit=function(a){let b=0;for(;0!==a;)b++,a>>>=1;return b},b.setToSJISFunction=function(a){if("function"!=typeof a)throw Error('"toSJISFunc" is not a valid function.');c=a},b.isKanjiModeEnabled=function(){return void 0!==c},b.toSJIS=function(a){return c(a)}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23023:(a,b,c)=>{let d=c(89097);function e(a,b){let c=a.a/255,d=b+'="'+a.hex+'"';return c<1?d+" "+b+'-opacity="'+c.toFixed(2).slice(1)+'"':d}function f(a,b,c){let d=a+b;return void 0!==c&&(d+=" "+c),d}b.render=function(a,b,c){let g=d.getOptions(b),h=a.modules.size,i=a.modules.data,j=h+2*g.margin,k=g.color.light.a?"<path "+e(g.color.light,"fill")+' d="M0 0h'+j+"v"+j+'H0z"/>':"",l="<path "+e(g.color.dark,"stroke")+' d="'+function(a,b,c){let d="",e=0,g=!1,h=0;for(let i=0;i<a.length;i++){let j=Math.floor(i%b),k=Math.floor(i/b);j||g||(g=!0),a[i]?(h++,i>0&&j>0&&a[i-1]||(d+=g?f("M",j+c,.5+k+c):f("m",e,0),e=0,g=!1),j+1<b&&a[i+1]||(d+=f("h",h),h=0)):e++}return d}(i,h,g.margin)+'"/>',m='<svg xmlns="http://www.w3.org/2000/svg" '+(g.width?'width="'+g.width+'" height="'+g.width+'" ':"")+('viewBox="0 0 '+j+" ")+j+'" shape-rendering="crispEdges">'+k+l+"</svg>\n";return"function"==typeof c&&c(null,m),m}},24e3:(a,b,c)=>{let d=c(34809);function e(a){this.mode=d.NUMERIC,this.data=a.toString()}e.getBitsLength=function(a){return 10*Math.floor(a/3)+(a%3?a%3*3+1:0)},e.prototype.getLength=function(){return this.data.length},e.prototype.getBitsLength=function(){return e.getBitsLength(this.data.length)},e.prototype.write=function(a){let b,c;for(b=0;b+3<=this.data.length;b+=3)c=parseInt(this.data.substr(b,3),10),a.put(c,10);let d=this.data.length-b;d>0&&(c=parseInt(this.data.substr(b),10),a.put(c,3*d+1))},a.exports=e},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},28955:a=>{"use strict";var b={single_source_shortest_paths:function(a,c,d){var e,f,g,h,i,j,k,l={},m={};m[c]=0;var n=b.PriorityQueue.make();for(n.push(c,0);!n.empty();)for(g in f=(e=n.pop()).value,h=e.cost,i=a[f]||{})i.hasOwnProperty(g)&&(j=h+i[g],k=m[g],(void 0===m[g]||k>j)&&(m[g]=j,n.push(g,j),l[g]=f));if(void 0!==d&&void 0===m[d])throw Error("Could not find a path from "+c+" to "+d+".");return l},extract_shortest_path_from_predecessor_list:function(a,b){for(var c=[],d=b;d;)c.push(d),a[d],d=a[d];return c.reverse(),c},find_path:function(a,c,d){var e=b.single_source_shortest_paths(a,c,d);return b.extract_shortest_path_from_predecessor_list(e,d)},PriorityQueue:{make:function(a){var c,d=b.PriorityQueue,e={};for(c in a=a||{},d)d.hasOwnProperty(c)&&(e[c]=d[c]);return e.queue=[],e.sorter=a.sorter||d.default_sorter,e},default_sorter:function(a,b){return a.cost-b.cost},push:function(a,b){this.queue.push({value:a,cost:b}),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return 0===this.queue.length}}};a.exports=b},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29478:(a,b,c)=>{let d=c(77709),e=c(7719);b.render=function(a,b,c){return b&&b.small?e.render(a,b,c):d.render(a,b,c)}},29948:(a,b,c)=>{let d=c(34809),e=c(18805);function f(a){this.mode=d.KANJI,this.data=a}f.getBitsLength=function(a){return 13*a},f.prototype.getLength=function(){return this.data.length},f.prototype.getBitsLength=function(){return f.getBitsLength(this.data.length)},f.prototype.write=function(a){let b;for(b=0;b<this.data.length;b++){let c=e.toSJIS(this.data[b]);if(c>=33088&&c<=40956)c-=33088;else if(c>=57408&&c<=60351)c-=49472;else throw Error("Invalid SJIS character: "+this.data[b]+"\nMake sure your charset is UTF-8");c=(c>>>8&255)*192+(255&c),a.put(c,13)}},a.exports=f},30277:(a,b,c)=>{let d=c(43724);function e(a){this.genPoly=void 0,this.degree=a,this.degree&&this.initialize(this.degree)}e.prototype.initialize=function(a){this.degree=a,this.genPoly=d.generateECPolynomial(this.degree)},e.prototype.encode=function(a){if(!this.genPoly)throw Error("Encoder not initialized");let b=new Uint8Array(a.length+this.degree);b.set(a);let c=d.mod(b,this.genPoly),e=this.degree-c.length;if(e>0){let a=new Uint8Array(this.degree);return a.set(c,e),a}return c},a.exports=e},33631:a=>{"use strict";a.exports=function(a,b,c){let d=a+b-c,e=Math.abs(d-a),f=Math.abs(d-b),g=Math.abs(d-c);return e<=f&&e<=g?a:f<=g?b:c}},33873:a=>{"use strict";a.exports=require("path")},33969:(a,b,c)=>{let d=c(96892),e=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],f=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];b.getBlocksCount=function(a,b){switch(b){case d.L:return e[(a-1)*4+0];case d.M:return e[(a-1)*4+1];case d.Q:return e[(a-1)*4+2];case d.H:return e[(a-1)*4+3];default:return}},b.getTotalCodewordsCount=function(a,b){switch(b){case d.L:return f[(a-1)*4+0];case d.M:return f[(a-1)*4+1];case d.Q:return f[(a-1)*4+2];case d.H:return f[(a-1)*4+3];default:return}}},34552:(a,b,c)=>{"use strict";let d=c(28354),e=c(74075),f=c(36833),g=c(87815),h=c(84289),i=c(49822),j=c(70050),k=a.exports=function(a){f.call(this),this._parser=new h(a,{read:this.read.bind(this),error:this._handleError.bind(this),metadata:this._handleMetaData.bind(this),gamma:this.emit.bind(this,"gamma"),palette:this._handlePalette.bind(this),transColor:this._handleTransColor.bind(this),finished:this._finished.bind(this),inflateData:this._inflateData.bind(this),simpleTransparency:this._simpleTransparency.bind(this),headersFinished:this._headersFinished.bind(this)}),this._options=a,this.writable=!0,this._parser.start()};d.inherits(k,f),k.prototype._handleError=function(a){this.emit("error",a),this.writable=!1,this.destroy(),this._inflate&&this._inflate.destroy&&this._inflate.destroy(),this._filter&&(this._filter.destroy(),this._filter.on("error",function(){})),this.errord=!0},k.prototype._inflateData=function(a){if(!this._inflate)if(this._bitmapInfo.interlace)this._inflate=e.createInflate(),this._inflate.on("error",this.emit.bind(this,"error")),this._filter.on("complete",this._complete.bind(this)),this._inflate.pipe(this._filter);else{let a=((this._bitmapInfo.width*this._bitmapInfo.bpp*this._bitmapInfo.depth+7>>3)+1)*this._bitmapInfo.height,b=Math.max(a,e.Z_MIN_CHUNK);this._inflate=e.createInflate({chunkSize:b});let c=a,d=this.emit.bind(this,"error");this._inflate.on("error",function(a){c&&d(a)}),this._filter.on("complete",this._complete.bind(this));let f=this._filter.write.bind(this._filter);this._inflate.on("data",function(a){c&&(a.length>c&&(a=a.slice(0,c)),c-=a.length,f(a))}),this._inflate.on("end",this._filter.end.bind(this._filter))}this._inflate.write(a)},k.prototype._handleMetaData=function(a){this._metaData=a,this._bitmapInfo=Object.create(a),this._filter=new g(this._bitmapInfo)},k.prototype._handleTransColor=function(a){this._bitmapInfo.transColor=a},k.prototype._handlePalette=function(a){this._bitmapInfo.palette=a},k.prototype._simpleTransparency=function(){this._metaData.alpha=!0},k.prototype._headersFinished=function(){this.emit("metadata",this._metaData)},k.prototype._finished=function(){this.errord||(this._inflate?this._inflate.end():this.emit("error","No Inflate block"))},k.prototype._complete=function(a){let b;if(!this.errord){try{let c=i.dataToBitMap(a,this._bitmapInfo);b=j(c,this._bitmapInfo),c=null}catch(a){this._handleError(a);return}this.emit("parsed",b)}}},34809:(a,b,c)=>{let d=c(89701),e=c(75399);b.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},b.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},b.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},b.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},b.MIXED={bit:-1},b.getCharCountIndicator=function(a,b){if(!a.ccBits)throw Error("Invalid mode: "+a);if(!d.isValid(b))throw Error("Invalid version: "+b);return b>=1&&b<10?a.ccBits[0]:b<27?a.ccBits[1]:a.ccBits[2]},b.getBestModeForData=function(a){return e.testNumeric(a)?b.NUMERIC:e.testAlphanumeric(a)?b.ALPHANUMERIC:e.testKanji(a)?b.KANJI:b.BYTE},b.toString=function(a){if(a&&a.id)return a.id;throw Error("Invalid mode")},b.isValid=function(a){return a&&a.bit&&a.ccBits},b.from=function(a,c){if(b.isValid(a))return a;try{if("string"!=typeof a)throw Error("Param is not a string");switch(a.toLowerCase()){case"numeric":return b.NUMERIC;case"alphanumeric":return b.ALPHANUMERIC;case"kanji":return b.KANJI;case"byte":return b.BYTE;default:throw Error("Unknown mode: "+a)}}catch(a){return c}}},35459:()=>{},35722:(a,b,c)=>{"use strict";let d=c(42049),e=c(33631);function f(a,b,c){let d=a*b;return 8!==c&&(d=Math.ceil(d/(8/c))),d}let g=a.exports=function(a,b){let c=a.width,e=a.height,g=a.interlace,h=a.bpp,i=a.depth;if(this.read=b.read,this.write=b.write,this.complete=b.complete,this._imageIndex=0,this._images=[],g){let a=d.getImagePasses(c,e);for(let b=0;b<a.length;b++)this._images.push({byteWidth:f(a[b].width,h,i),height:a[b].height,lineIndex:0})}else this._images.push({byteWidth:f(c,h,i),height:e,lineIndex:0});8===i?this._xComparison=h:16===i?this._xComparison=2*h:this._xComparison=1};g.prototype.start=function(){this.read(this._images[this._imageIndex].byteWidth+1,this._reverseFilterLine.bind(this))},g.prototype._unFilterType1=function(a,b,c){let d=this._xComparison,e=d-1;for(let f=0;f<c;f++){let c=a[1+f],g=f>e?b[f-d]:0;b[f]=c+g}},g.prototype._unFilterType2=function(a,b,c){let d=this._lastLine;for(let e=0;e<c;e++){let c=a[1+e],f=d?d[e]:0;b[e]=c+f}},g.prototype._unFilterType3=function(a,b,c){let d=this._xComparison,e=d-1,f=this._lastLine;for(let g=0;g<c;g++){let c=a[1+g],h=f?f[g]:0,i=Math.floor(((g>e?b[g-d]:0)+h)/2);b[g]=c+i}},g.prototype._unFilterType4=function(a,b,c){let d=this._xComparison,f=d-1,g=this._lastLine;for(let h=0;h<c;h++){let c=a[1+h],i=g?g[h]:0,j=e(h>f?b[h-d]:0,i,h>f&&g?g[h-d]:0);b[h]=c+j}},g.prototype._reverseFilterLine=function(a){let b,c=a[0],d=this._images[this._imageIndex],e=d.byteWidth;if(0===c)b=a.slice(1,e+1);else switch(b=Buffer.alloc(e),c){case 1:this._unFilterType1(a,b,e);break;case 2:this._unFilterType2(a,b,e);break;case 3:this._unFilterType3(a,b,e);break;case 4:this._unFilterType4(a,b,e);break;default:throw Error("Unrecognised filter type - "+c)}this.write(b),d.lineIndex++,d.lineIndex>=d.height?(this._lastLine=null,this._imageIndex++,d=this._images[this._imageIndex]):this._lastLine=b,d?this.read(d.byteWidth+1,this._reverseFilterLine.bind(this)):(this._lastLine=null,this.complete())}},36530:(a,b,c)=>{"use strict";a.exports=c(56191)},36833:(a,b,c)=>{"use strict";let d=c(28354),e=c(27910),f=a.exports=function(){e.call(this),this._buffers=[],this._buffered=0,this._reads=[],this._paused=!1,this._encoding="utf8",this.writable=!0};d.inherits(f,e),f.prototype.read=function(a,b){this._reads.push({length:Math.abs(a),allowLess:a<0,func:b}),process.nextTick((function(){this._process(),this._paused&&this._reads&&this._reads.length>0&&(this._paused=!1,this.emit("drain"))}).bind(this))},f.prototype.write=function(a,b){let c;return this.writable?(c=Buffer.isBuffer(a)?a:Buffer.from(a,b||this._encoding),this._buffers.push(c),this._buffered+=c.length,this._process(),this._reads&&0===this._reads.length&&(this._paused=!0),this.writable&&!this._paused):(this.emit("error",Error("Stream not writable")),!1)},f.prototype.end=function(a,b){a&&this.write(a,b),this.writable=!1,this._buffers&&(0===this._buffers.length?this._end():(this._buffers.push(null),this._process()))},f.prototype.destroySoon=f.prototype.end,f.prototype._end=function(){this._reads.length>0&&this.emit("error",Error("Unexpected end of input")),this.destroy()},f.prototype.destroy=function(){this._buffers&&(this.writable=!1,this._reads=null,this._buffers=null,this.emit("close"))},f.prototype._processReadAllowingLess=function(a){this._reads.shift();let b=this._buffers[0];b.length>a.length?(this._buffered-=a.length,this._buffers[0]=b.slice(a.length),a.func.call(this,b.slice(0,a.length))):(this._buffered-=b.length,this._buffers.shift(),a.func.call(this,b))},f.prototype._processRead=function(a){this._reads.shift();let b=0,c=0,d=Buffer.alloc(a.length);for(;b<a.length;){let e=this._buffers[c++],f=Math.min(e.length,a.length-b);e.copy(d,b,0,f),b+=f,f!==e.length&&(this._buffers[--c]=e.slice(f))}c>0&&this._buffers.splice(0,c),this._buffered-=a.length,a.func.call(this,d)},f.prototype._process=function(){try{for(;this._buffered>0&&this._reads&&this._reads.length>0;){let a=this._reads[0];if(a.allowLess)this._processReadAllowingLess(a);else if(this._buffered>=a.length)this._processRead(a);else break}this._buffers&&!this.writable&&this._end()}catch(a){this.emit("error",a)}}},37027:(a,b,c)=>{"use strict";let d=c(99985),e=c(70822);b.read=function(a,b){return d(a,b||{})},b.write=function(a,b){return e(a,b)}},37355:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,48365,23)),Promise.resolve().then(c.t.bind(c,64596,23)),Promise.resolve().then(c.t.bind(c,56186,23)),Promise.resolve().then(c.t.bind(c,67805,23)),Promise.resolve().then(c.t.bind(c,27561,23)),Promise.resolve().then(c.t.bind(c,47569,23)),Promise.resolve().then(c.t.bind(c,42747,23)),Promise.resolve().then(c.t.bind(c,56676,23)),Promise.resolve().then(c.bind(c,97225))},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},42049:(a,b)=>{"use strict";let c=[{x:[0],y:[0]},{x:[4],y:[0]},{x:[0,4],y:[4]},{x:[2,6],y:[0,4]},{x:[0,2,4,6],y:[2,6]},{x:[1,3,5,7],y:[0,2,4,6]},{x:[0,1,2,3,4,5,6,7],y:[1,3,5,7]}];b.getImagePasses=function(a,b){let d=[],e=a%8,f=b%8,g=(a-e)/8,h=(b-f)/8;for(let a=0;a<c.length;a++){let b=c[a],i=g*b.x.length,j=h*b.y.length;for(let a=0;a<b.x.length;a++)if(b.x[a]<e)i++;else break;for(let a=0;a<b.y.length;a++)if(b.y[a]<f)j++;else break;i>0&&j>0&&d.push({width:i,height:j,index:a})}return d},b.getInterlaceIterator=function(a){return function(b,d,e){let f=b%c[e].x.length,g=(b-f)/c[e].x.length*8+c[e].x[f],h=d%c[e].y.length;return 4*g+((d-h)/c[e].y.length*8+c[e].y[h])*a*4}}},42931:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,58671,23)),Promise.resolve().then(c.t.bind(c,56542,23)),Promise.resolve().then(c.t.bind(c,88248,23)),Promise.resolve().then(c.t.bind(c,49743,23)),Promise.resolve().then(c.t.bind(c,96231,23)),Promise.resolve().then(c.t.bind(c,10959,23)),Promise.resolve().then(c.t.bind(c,72041,23)),Promise.resolve().then(c.t.bind(c,95094,23)),Promise.resolve().then(c.t.bind(c,67487,23))},43724:(a,b,c)=>{let d=c(99534);b.mul=function(a,b){let c=new Uint8Array(a.length+b.length-1);for(let e=0;e<a.length;e++)for(let f=0;f<b.length;f++)c[e+f]^=d.mul(a[e],b[f]);return c},b.mod=function(a,b){let c=new Uint8Array(a);for(;c.length-b.length>=0;){let a=c[0];for(let e=0;e<b.length;e++)c[e]^=d.mul(b[e],a);let e=0;for(;e<c.length&&0===c[e];)e++;c=c.slice(e)}return c},b.generateECPolynomial=function(a){let c=new Uint8Array([1]);for(let e=0;e<a;e++)c=b.mul(c,new Uint8Array([1,d.exp(e)]));return c}},46003:(a,b,c)=>{"use strict";let d=c(12412).ok,e=c(74075),f=c(28354),g=c(79428).kMaxLength;function h(a){if(!(this instanceof h))return new h(a);a&&a.chunkSize<e.Z_MIN_CHUNK&&(a.chunkSize=e.Z_MIN_CHUNK),e.Inflate.call(this,a),this._offset=void 0===this._offset?this._outOffset:this._offset,this._buffer=this._buffer||this._outBuffer,a&&null!=a.maxLength&&(this._maxLength=a.maxLength)}function i(a,b){b&&process.nextTick(b),a._handle&&(a._handle.close(),a._handle=null)}function j(a,b){var c=new h(b),d=a;if("string"==typeof d&&(d=Buffer.from(d)),!(d instanceof Buffer))throw TypeError("Not a string or buffer");let f=c._finishFlushFlag;return null==f&&(f=e.Z_FINISH),c._processChunk(d,f)}h.prototype._processChunk=function(a,b,c){let f,h;if("function"==typeof c)return e.Inflate._processChunk.call(this,a,b,c);let j=this,k=a&&a.length,l=this._chunkSize-this._offset,m=this._maxLength,n=0,o=[],p=0;this.on("error",function(a){f=a}),d(this._handle,"zlib binding closed");do h=(h=this._handle.writeSync(b,a,n,k,this._buffer,this._offset,l))||this._writeState;while(!this._hadError&&function(a,b){if(j._hadError)return;let c=l-b;if(d(c>=0,"have should not go down"),c>0){let a=j._buffer.slice(j._offset,j._offset+c);if(j._offset+=c,a.length>m&&(a=a.slice(0,m)),o.push(a),p+=a.length,0==(m-=a.length))return!1}return(0===b||j._offset>=j._chunkSize)&&(l=j._chunkSize,j._offset=0,j._buffer=Buffer.allocUnsafe(j._chunkSize)),0===b&&(n+=k-a,k=a,!0)}(h[0],h[1]));if(this._hadError)throw f;if(p>=g)throw i(this),RangeError("Cannot create final Buffer. It would be larger than 0x"+g.toString(16)+" bytes");let q=Buffer.concat(o,p);return i(this),q},f.inherits(h,e.Inflate),a.exports=b=j,b.Inflate=h,b.createInflate=function(a){return new h(a)},b.inflateSync=j},47493:(a,b,c)=>{let d=c(89097),e={WW:" ",WB:"▄",BB:"█",BW:"▀"},f={BB:" ",BW:"▄",WW:"█",WB:"▀"};b.render=function(a,b,c){let g=d.getOptions(b),h=e;("#ffffff"===g.color.dark.hex||"#000000"===g.color.light.hex)&&(h=f);let i=a.modules.size,j=a.modules.data,k="",l=Array(i+2*g.margin+1).join(h.WW);l=Array(g.margin/2+1).join(l+"\n");let m=Array(g.margin+1).join(h.WW);k+=l;for(let a=0;a<i;a+=2){k+=m;for(let b=0;b<i;b++){var n;let c=j[a*i+b],d=j[(a+1)*i+b];k+=(n=h,c&&d?n.BB:c&&!d?n.BW:!c&&d?n.WB:n.WW)}k+=m+"\n"}return k+=l.slice(0,-1),"function"==typeof c&&c(null,k),k},b.renderToFile=function(a,d,e,f){void 0===f&&(f=e,e=void 0);let g=c(29021),h=b.render(d,e);g.writeFile(a,h,f)}},47570:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>h,metadata:()=>g});var d=c(5939),e=c(50157),f=c.n(e);c(14276);let g={title:"نظام إدارة الموارد البشرية - مرجب",description:"نظام شامل لإدارة الموارد البشرية والقوة العمومية",keywords:["الموارد البشرية","إدارة الموظفين","القوة العمومية","مرجب"],authors:[{name:"Mergeb HR System"}],viewport:"width=device-width, initial-scale=1",robots:"noindex, nofollow"};function h({children:a}){return(0,d.jsx)("html",{lang:"ar",dir:"rtl",children:(0,d.jsx)("body",{className:`${f().variable} font-sans antialiased bg-gradient-to-br from-secondary-50 via-white to-primary-50 min-h-screen`,children:(0,d.jsx)("div",{className:"relative",children:a})})})}},49822:(a,b,c)=>{"use strict";let d=c(42049),e=[function(){},function(a,b,c,d){if(d===b.length)throw Error("Ran out of data");let e=b[d];a[c]=e,a[c+1]=e,a[c+2]=e,a[c+3]=255},function(a,b,c,d){if(d+1>=b.length)throw Error("Ran out of data");let e=b[d];a[c]=e,a[c+1]=e,a[c+2]=e,a[c+3]=b[d+1]},function(a,b,c,d){if(d+2>=b.length)throw Error("Ran out of data");a[c]=b[d],a[c+1]=b[d+1],a[c+2]=b[d+2],a[c+3]=255},function(a,b,c,d){if(d+3>=b.length)throw Error("Ran out of data");a[c]=b[d],a[c+1]=b[d+1],a[c+2]=b[d+2],a[c+3]=b[d+3]}],f=[function(){},function(a,b,c,d){let e=b[0];a[c]=e,a[c+1]=e,a[c+2]=e,a[c+3]=d},function(a,b,c){let d=b[0];a[c]=d,a[c+1]=d,a[c+2]=d,a[c+3]=b[1]},function(a,b,c,d){a[c]=b[0],a[c+1]=b[1],a[c+2]=b[2],a[c+3]=d},function(a,b,c){a[c]=b[0],a[c+1]=b[1],a[c+2]=b[2],a[c+3]=b[3]}];b.dataToBitMap=function(a,b){let c,g,h,i,j,k,l=b.width,m=b.height,n=b.depth,o=b.bpp,p=b.interlace;8!==n&&(c=[],g=0,h={get:function(b){for(;c.length<b;)!function(){let b,d,e,f;if(g===a.length)throw Error("Ran out of data");let h=a[g];switch(g++,n){default:throw Error("unrecognised depth");case 16:e=a[g],g++,c.push((h<<8)+e);break;case 4:e=15&h,f=h>>4,c.push(f,e);break;case 2:b=3&h,d=h>>2&3,e=h>>4&3,f=h>>6&3,c.push(f,e,d,b);break;case 1:b=h>>4&1,d=h>>5&1,e=h>>6&1,f=h>>7&1,c.push(f,e,d,b,h>>3&1,h>>2&1,h>>1&1,1&h)}}();let d=c.slice(0,b);return c=c.slice(b),d},resetAfterLine:function(){c.length=0},end:function(){if(g!==a.length)throw Error("extra data found")}}),i=n<=8?Buffer.alloc(l*m*4):new Uint16Array(l*m*4);let q=Math.pow(2,n)-1,r=0;if(p)j=d.getImagePasses(l,m),k=d.getInterlaceIterator(l,m);else{let a=0;k=function(){let b=a;return a+=4,b},j=[{width:l,height:m}]}for(let b=0;b<j.length;b++)8===n?r=function(a,b,c,d,f,g){let h=a.width,i=a.height,j=a.index;for(let a=0;a<i;a++)for(let i=0;i<h;i++){let h=c(i,a,j);e[d](b,f,h,g),g+=d}return g}(j[b],i,k,o,a,r):function(a,b,c,d,e,g){let h=a.width,i=a.height,j=a.index;for(let a=0;a<i;a++){for(let i=0;i<h;i++){let h=e.get(d),k=c(i,a,j);f[d](b,h,k,g)}e.resetAfterLine()}}(j[b],i,k,o,h,q);if(8===n){if(r!==a.length)throw Error("extra data found")}else h.end();return i}},51768:(a,b,c)=>{let d=c(34809),e=c(24e3),f=c(4794),g=c(90701),h=c(29948),i=c(75399),j=c(18805),k=c(28955);function l(a){return unescape(encodeURIComponent(a)).length}function m(a,b,c){let d,e=[];for(;null!==(d=a.exec(c));)e.push({data:d[0],index:d.index,mode:b,length:d[0].length});return e}function n(a){let b,c,e=m(i.NUMERIC,d.NUMERIC,a),f=m(i.ALPHANUMERIC,d.ALPHANUMERIC,a);return j.isKanjiModeEnabled()?(b=m(i.BYTE,d.BYTE,a),c=m(i.KANJI,d.KANJI,a)):(b=m(i.BYTE_KANJI,d.BYTE,a),c=[]),e.concat(f,b,c).sort(function(a,b){return a.index-b.index}).map(function(a){return{data:a.data,mode:a.mode,length:a.length}})}function o(a,b){switch(b){case d.NUMERIC:return e.getBitsLength(a);case d.ALPHANUMERIC:return f.getBitsLength(a);case d.KANJI:return h.getBitsLength(a);case d.BYTE:return g.getBitsLength(a)}}function p(a,b){let c,i=d.getBestModeForData(a);if((c=d.from(b,i))!==d.BYTE&&c.bit<i.bit)throw Error('"'+a+'" cannot be encoded with mode '+d.toString(c)+".\n Suggested mode is: "+d.toString(i));switch(c===d.KANJI&&!j.isKanjiModeEnabled()&&(c=d.BYTE),c){case d.NUMERIC:return new e(a);case d.ALPHANUMERIC:return new f(a);case d.KANJI:return new h(a);case d.BYTE:return new g(a)}}b.fromArray=function(a){return a.reduce(function(a,b){return"string"==typeof b?a.push(p(b,null)):b.data&&a.push(p(b.data,b.mode)),a},[])},b.fromString=function(a,c){let e=function(a,b){let c={},e={start:{}},f=["start"];for(let g=0;g<a.length;g++){let h=a[g],i=[];for(let a=0;a<h.length;a++){let j=h[a],k=""+g+a;i.push(k),c[k]={node:j,lastCount:0},e[k]={};for(let a=0;a<f.length;a++){let g=f[a];c[g]&&c[g].node.mode===j.mode?(e[g][k]=o(c[g].lastCount+j.length,j.mode)-o(c[g].lastCount,j.mode),c[g].lastCount+=j.length):(c[g]&&(c[g].lastCount=j.length),e[g][k]=o(j.length,j.mode)+4+d.getCharCountIndicator(j.mode,b))}}f=i}for(let a=0;a<f.length;a++)e[f[a]].end=0;return{map:e,table:c}}(function(a){let b=[];for(let c=0;c<a.length;c++){let e=a[c];switch(e.mode){case d.NUMERIC:b.push([e,{data:e.data,mode:d.ALPHANUMERIC,length:e.length},{data:e.data,mode:d.BYTE,length:e.length}]);break;case d.ALPHANUMERIC:b.push([e,{data:e.data,mode:d.BYTE,length:e.length}]);break;case d.KANJI:b.push([e,{data:e.data,mode:d.BYTE,length:l(e.data)}]);break;case d.BYTE:b.push([{data:e.data,mode:d.BYTE,length:l(e.data)}])}}return b}(n(a,j.isKanjiModeEnabled())),c),f=k.find_path(e.map,"start","end"),g=[];for(let a=1;a<f.length-1;a++)g.push(e.table[f[a]].node);return b.fromArray(g.reduce(function(a,b){let c=a.length-1>=0?a[a.length-1]:null;return c&&c.mode===b.mode?a[a.length-1].data+=b.data:a.push(b),a},[]))},b.rawSplit=function(a){return b.fromArray(n(a,j.isKanjiModeEnabled()))}},56191:(a,b,c)=>{let d=c(59064),e=c(640),f=c(18695),g=c(47493),h=c(29478),i=c(85350);function j(a,b,c){if(void 0===a)throw Error("String required as first argument");if(void 0===c&&(c=b,b={}),"function"!=typeof c)if(d())b=c||{},c=null;else throw Error("Callback required as last argument");return{opts:b,cb:c}}function k(a){switch(a){case"svg":return i;case"txt":case"utf8":return g;default:return f}}function l(a,b,c){if(!c.cb)return new Promise(function(d,f){try{let g=e.create(b,c.opts);return a(g,c.opts,function(a,b){return a?f(a):d(b)})}catch(a){f(a)}});try{let d=e.create(b,c.opts);return a(d,c.opts,c.cb)}catch(a){c.cb(a)}}b.create=e.create,b.toCanvas=c(59426).toCanvas,b.toString=function(a,b,c){let d=j(a,b,c);return l(function(a){switch(a){case"svg":return i;case"terminal":return h;default:return g}}(d.opts?d.opts.type:void 0).render,a,d)},b.toDataURL=function(a,b,c){let d=j(a,b,c);return l(k(d.opts.type).renderToDataURL,a,d)},b.toBuffer=function(a,b,c){let d=j(a,b,c);return l(k(d.opts.type).renderToBuffer,a,d)},b.toFile=function(a,b,c,e){if("string"!=typeof a||"string"!=typeof b&&"object"!=typeof b)throw Error("Invalid argument");if(arguments.length<3&&!d())throw Error("Too few arguments provided");let f=j(b,c,e);return l(k(f.opts.type||a.slice((a.lastIndexOf(".")-1>>>0)+2).toLowerCase()).renderToFile.bind(null,a),b,f)},b.toFileStream=function(a,b,c){if(arguments.length<2)throw Error("Too few arguments provided");let d=j(b,c,a.emit.bind(a,"error"));l(k("png").renderToFileStream.bind(null,a),b,d)}},58535:(a,b,c)=>{Promise.resolve().then(c.bind(c,17723))},59064:a=>{a.exports=function(){return"function"==typeof Promise&&Promise.prototype&&Promise.prototype.then}},59426:(a,b,c)=>{let d=c(59064),e=c(640),f=c(14590),g=c(23023);function h(a,b,c,f,g){let h=[].slice.call(arguments,1),i=h.length,j="function"==typeof h[i-1];if(!j&&!d())throw Error("Callback required as last argument");if(j){if(i<2)throw Error("Too few arguments provided");2===i?(g=c,c=b,b=f=void 0):3===i&&(b.getContext&&void 0===g?(g=f,f=void 0):(g=f,f=c,c=b,b=void 0))}else{if(i<1)throw Error("Too few arguments provided");return 1===i?(c=b,b=f=void 0):2!==i||b.getContext||(f=c,c=b,b=void 0),new Promise(function(d,g){try{let g=e.create(c,f);d(a(g,b,f))}catch(a){g(a)}})}try{let d=e.create(c,f);g(null,a(d,b,f))}catch(a){g(a)}}e.create,b.toCanvas=h.bind(null,f.render),h.bind(null,f.renderToDataURL),h.bind(null,function(a,b,c){return g.render(a,c)})},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64850:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>D.a,__next_app__:()=>J,handler:()=>L,pages:()=>I,routeModule:()=>K,tree:()=>H});var d=c(73653),e=c(97714),f=c(85250),g=c(37587),h=c(22369),i=c(1889),j=c(96232),k=c(22841),l=c(46537),m=c(46027),n=c(78559),o=c(75928),p=c(19374),q=c(65971),r=c(261),s=c(79898),t=c(32967),u=c(26713),v=c(40139),w=c(14248),x=c(59580),y=c(57749),z=c(53123),A=c(89745),B=c(86439),C=c(58671),D=c.n(C),E=c(18283),F=c(39818),G={};for(let a in E)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(G[a]=()=>E[a]);c.d(b,G);let H={children:["",{children:["print",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,17723)),"C:\\Users\\<USER>\\Desktop\\mergeb HR\\apps\\personnel-frontend\\src\\app\\print\\[id]\\page.tsx"]}]},{}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,9699))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,47570)),"C:\\Users\\<USER>\\Desktop\\mergeb HR\\apps\\personnel-frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,58671,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,17983,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,15034,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,54693,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,9699))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,I=["C:\\Users\\<USER>\\Desktop\\mergeb HR\\apps\\personnel-frontend\\src\\app\\print\\[id]\\page.tsx"],J={require:c,loadChunk:()=>Promise.resolve()},K=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/print/[id]/page",pathname:"/print/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:H},distDir:".next",relativeProjectDir:""});async function L(a,b,d){var C;let G="/print/[id]/page";"/index"===G&&(G="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await K.prepare(a,b,{srcPage:G,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(G),{isOnDemandRevalidate:ah}=O,ai=K.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(C=$.routes[ag]??$.dynamicRoutes[ag])?void 0:C.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===K.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&K.isDev&&(aB=aa),K.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...E,tree:H,pages:I,GlobalError:D(),handler:L,routeModule:K,__next_app__:J};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:G,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=K.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return K.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:K,page:G,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),K.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>K.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:K.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!K.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===K.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await K.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await K.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),K.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!K.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&F.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await K.onRequestError(a,b,{routerKind:"App Router",routePath:G,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},66085:(a,b)=>{b.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};let c={N1:3,N2:3,N3:40,N4:10};b.isValid=function(a){return null!=a&&""!==a&&!isNaN(a)&&a>=0&&a<=7},b.from=function(a){return b.isValid(a)?parseInt(a,10):void 0},b.getPenaltyN1=function(a){let b=a.size,d=0,e=0,f=0,g=null,h=null;for(let i=0;i<b;i++){e=f=0,g=h=null;for(let j=0;j<b;j++){let b=a.get(i,j);b===g?e++:(e>=5&&(d+=c.N1+(e-5)),g=b,e=1),(b=a.get(j,i))===h?f++:(f>=5&&(d+=c.N1+(f-5)),h=b,f=1)}e>=5&&(d+=c.N1+(e-5)),f>=5&&(d+=c.N1+(f-5))}return d},b.getPenaltyN2=function(a){let b=a.size,d=0;for(let c=0;c<b-1;c++)for(let e=0;e<b-1;e++){let b=a.get(c,e)+a.get(c,e+1)+a.get(c+1,e)+a.get(c+1,e+1);(4===b||0===b)&&d++}return d*c.N2},b.getPenaltyN3=function(a){let b=a.size,d=0,e=0,f=0;for(let c=0;c<b;c++){e=f=0;for(let g=0;g<b;g++)e=e<<1&2047|a.get(c,g),g>=10&&(1488===e||93===e)&&d++,f=f<<1&2047|a.get(g,c),g>=10&&(1488===f||93===f)&&d++}return d*c.N3},b.getPenaltyN4=function(a){let b=0,d=a.data.length;for(let c=0;c<d;c++)b+=a.data[c];return Math.abs(Math.ceil(100*b/d/5)-10)*c.N4},b.applyMask=function(a,c){let d=c.size;for(let e=0;e<d;e++)for(let f=0;f<d;f++)c.isReserved(f,e)||c.xor(f,e,function(a,c,d){switch(a){case b.Patterns.PATTERN000:return(c+d)%2==0;case b.Patterns.PATTERN001:return c%2==0;case b.Patterns.PATTERN010:return d%3==0;case b.Patterns.PATTERN011:return(c+d)%3==0;case b.Patterns.PATTERN100:return(Math.floor(c/2)+Math.floor(d/3))%2==0;case b.Patterns.PATTERN101:return c*d%2+c*d%3==0;case b.Patterns.PATTERN110:return(c*d%2+c*d%3)%2==0;case b.Patterns.PATTERN111:return(c*d%3+(c+d)%2)%2==0;default:throw Error("bad maskPattern:"+a)}}(a,f,e))},b.getBestMask=function(a,c){let d=Object.keys(b.Patterns).length,e=0,f=1/0;for(let g=0;g<d;g++){c(g),b.applyMask(g,a);let d=b.getPenaltyN1(a)+b.getPenaltyN2(a)+b.getPenaltyN3(a)+b.getPenaltyN4(a);b.applyMask(g,a),d<f&&(f=d,e=g)}return e}},67970:a=>{"use strict";let b=[];!function(){for(let a=0;a<256;a++){let c=a;for(let a=0;a<8;a++)1&c?c=0xedb88320^c>>>1:c>>>=1;b[a]=c}}();let c=a.exports=function(){this._crc=-1};c.prototype.write=function(a){for(let c=0;c<a.length;c++)this._crc=b[(this._crc^a[c])&255]^this._crc>>>8;return!0},c.prototype.crc32=function(){return -1^this._crc},c.crc32=function(a){let c=-1;for(let d=0;d<a.length;d++)c=b[(c^a[d])&255]^c>>>8;return -1^c}},69168:(a,b,c)=>{let d=c(18805),e=c(33969),f=c(96892),g=c(34809),h=c(89701),i=d.getBCHDigit(7973);function j(a,b){return g.getCharCountIndicator(a,b)+4}b.from=function(a,b){return h.isValid(a)?parseInt(a,10):b},b.getCapacity=function(a,b,c){if(!h.isValid(a))throw Error("Invalid QR Code version");void 0===c&&(c=g.BYTE);let f=(d.getSymbolTotalCodewords(a)-e.getTotalCodewordsCount(a,b))*8;if(c===g.MIXED)return f;let i=f-j(c,a);switch(c){case g.NUMERIC:return Math.floor(i/10*3);case g.ALPHANUMERIC:return Math.floor(i/11*2);case g.KANJI:return Math.floor(i/13);case g.BYTE:default:return Math.floor(i/8)}},b.getBestVersionForData=function(a,c){let d,e=f.from(c,f.M);if(Array.isArray(a)){if(a.length>1){for(let c=1;c<=40;c++)if(function(a,b){let c=0;return a.forEach(function(a){let d=j(a.mode,b);c+=d+a.getBitsLength()}),c}(a,c)<=b.getCapacity(c,e,g.MIXED))return c;return}if(0===a.length)return 1;d=a[0]}else d=a;return function(a,c,d){for(let e=1;e<=40;e++)if(c<=b.getCapacity(e,d,a))return e}(d.mode,d.getLength(),e)},b.getEncodedBits=function(a){if(!h.isValid(a)||a<7)throw Error("Invalid QR Code version");let b=a<<12;for(;d.getBCHDigit(b)-i>=0;)b^=7973<<d.getBCHDigit(b)-i;return a<<12|b}},70050:a=>{"use strict";a.exports=function(a,b){let c=b.depth,d=b.width,e=b.height,f=b.colorType,g=b.transColor,h=b.palette,i=a;return 3===f?!function(a,b,c,d,e){let f=0;for(let g=0;g<d;g++)for(let d=0;d<c;d++){let c=e[a[f]];if(!c)throw Error("index "+a[f]+" not in palette");for(let a=0;a<4;a++)b[f+a]=c[a];f+=4}}(a,i,d,e,h):(g&&function(a,b,c,d,e){let f=0;for(let g=0;g<d;g++)for(let d=0;d<c;d++){let c=!1;if(1===e.length?e[0]===a[f]&&(c=!0):e[0]===a[f]&&e[1]===a[f+1]&&e[2]===a[f+2]&&(c=!0),c)for(let a=0;a<4;a++)b[f+a]=0;f+=4}}(a,i,d,e,g),8!==c&&(16===c&&(i=Buffer.alloc(d*e*4)),!function(a,b,c,d,e){let f=Math.pow(2,e)-1,g=0;for(let e=0;e<d;e++)for(let d=0;d<c;d++){for(let c=0;c<4;c++)b[g+c]=Math.floor(255*a[g+c]/f+.5);g+=4}}(a,i,d,e,c))),i}},70822:(a,b,c)=>{"use strict";let d=!0,e=c(74075);e.deflateSync||(d=!1);let f=c(74361),g=c(75712);a.exports=function(a,b){if(!d)throw Error("To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0");let c=new g(b||{}),h=[];h.push(Buffer.from(f.PNG_SIGNATURE)),h.push(c.packIHDR(a.width,a.height)),a.gamma&&h.push(c.packGAMA(a.gamma));let i=c.filterData(a.data,a.width,a.height),j=e.deflateSync(i,c.getDeflateOptions());if(i=null,!j||!j.length)throw Error("bad png - invalid compressed data response");return h.push(c.packIDAT(j)),h.push(c.packIEND()),Buffer.concat(h)}},71159:(a,b,c)=>{"use strict";var d=c(30291);c.o(d,"useParams")&&c.d(b,{useParams:function(){return d.useParams}}),c.o(d,"useRouter")&&c.d(b,{useRouter:function(){return d.useRouter}})},71981:a=>{"use strict";let b=a.exports=function(a){this._buffer=a,this._reads=[]};b.prototype.read=function(a,b){this._reads.push({length:Math.abs(a),allowLess:a<0,func:b})},b.prototype.process=function(){for(;this._reads.length>0&&this._buffer.length;){let a=this._reads[0];if(this._buffer.length&&(this._buffer.length>=a.length||a.allowLess)){this._reads.shift();let b=this._buffer;this._buffer=b.slice(a.length),a.func.call(this,b.slice(0,a.length))}else break}return this._reads.length>0?Error("There are some read requests waitng on finished stream"):this._buffer.length>0?Error("unrecognised content at end of stream"):void 0}},74075:a=>{"use strict";a.exports=require("zlib")},74345:(a,b,c)=>{"use strict";let d=c(28354),e=c(27910),f=c(74361),g=c(75712),h=a.exports=function(a){e.call(this),this._packer=new g(a||{}),this._deflate=this._packer.createDeflate(),this.readable=!0};d.inherits(h,e),h.prototype.pack=function(a,b,c,d){this.emit("data",Buffer.from(f.PNG_SIGNATURE)),this.emit("data",this._packer.packIHDR(b,c)),d&&this.emit("data",this._packer.packGAMA(d));let e=this._packer.filterData(a,b,c);this._deflate.on("error",this.emit.bind(this,"error")),this._deflate.on("data",(function(a){this.emit("data",this._packer.packIDAT(a))}).bind(this)),this._deflate.on("end",(function(){this.emit("data",this._packer.packIEND()),this.emit("end")}).bind(this)),this._deflate.end(e)}},74361:a=>{"use strict";a.exports={PNG_SIGNATURE:[137,80,78,71,13,10,26,10],TYPE_IHDR:0x49484452,TYPE_IEND:0x49454e44,TYPE_IDAT:0x49444154,TYPE_PLTE:0x504c5445,TYPE_tRNS:0x74524e53,TYPE_gAMA:0x67414d41,COLORTYPE_GRAYSCALE:0,COLORTYPE_PALETTE:1,COLORTYPE_COLOR:2,COLORTYPE_ALPHA:4,COLORTYPE_PALETTE_COLOR:3,COLORTYPE_COLOR_ALPHA:6,COLORTYPE_TO_BPP_MAP:{0:1,2:3,3:1,4:2,6:4},GAMMA_DIVISION:1e5}},75399:(a,b)=>{let c="[0-9]+",d="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+",e="(?:(?![A-Z0-9 $%*+\\-./:]|"+(d=d.replace(/u/g,"\\u"))+")(?:.|[\r\n]))+";b.KANJI=RegExp(d,"g"),b.BYTE_KANJI=RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),b.BYTE=RegExp(e,"g"),b.NUMERIC=RegExp(c,"g"),b.ALPHANUMERIC=RegExp("[A-Z $%*+\\-./:]+","g");let f=RegExp("^"+d+"$"),g=RegExp("^"+c+"$"),h=RegExp("^[A-Z0-9 $%*+\\-./:]+$");b.testKanji=function(a){return f.test(a)},b.testNumeric=function(a){return g.test(a)},b.testAlphanumeric=function(a){return h.test(a)}},75712:(a,b,c)=>{"use strict";let d=c(74361),e=c(67970),f=c(93729),g=c(84448),h=c(74075),i=a.exports=function(a){if(this._options=a,a.deflateChunkSize=a.deflateChunkSize||32768,a.deflateLevel=null!=a.deflateLevel?a.deflateLevel:9,a.deflateStrategy=null!=a.deflateStrategy?a.deflateStrategy:3,a.inputHasAlpha=null==a.inputHasAlpha||a.inputHasAlpha,a.deflateFactory=a.deflateFactory||h.createDeflate,a.bitDepth=a.bitDepth||8,a.colorType="number"==typeof a.colorType?a.colorType:d.COLORTYPE_COLOR_ALPHA,a.inputColorType="number"==typeof a.inputColorType?a.inputColorType:d.COLORTYPE_COLOR_ALPHA,-1===[d.COLORTYPE_GRAYSCALE,d.COLORTYPE_COLOR,d.COLORTYPE_COLOR_ALPHA,d.COLORTYPE_ALPHA].indexOf(a.colorType))throw Error("option color type:"+a.colorType+" is not supported at present");if(-1===[d.COLORTYPE_GRAYSCALE,d.COLORTYPE_COLOR,d.COLORTYPE_COLOR_ALPHA,d.COLORTYPE_ALPHA].indexOf(a.inputColorType))throw Error("option input color type:"+a.inputColorType+" is not supported at present");if(8!==a.bitDepth&&16!==a.bitDepth)throw Error("option bit depth:"+a.bitDepth+" is not supported at present")};i.prototype.getDeflateOptions=function(){return{chunkSize:this._options.deflateChunkSize,level:this._options.deflateLevel,strategy:this._options.deflateStrategy}},i.prototype.createDeflate=function(){return this._options.deflateFactory(this.getDeflateOptions())},i.prototype.filterData=function(a,b,c){let e=f(a,b,c,this._options),h=d.COLORTYPE_TO_BPP_MAP[this._options.colorType];return g(e,b,c,this._options,h)},i.prototype._packChunk=function(a,b){let c=b?b.length:0,d=Buffer.alloc(c+12);return d.writeUInt32BE(c,0),d.writeUInt32BE(a,4),b&&b.copy(d,8),d.writeInt32BE(e.crc32(d.slice(4,d.length-4)),d.length-4),d},i.prototype.packGAMA=function(a){let b=Buffer.alloc(4);return b.writeUInt32BE(Math.floor(a*d.GAMMA_DIVISION),0),this._packChunk(d.TYPE_gAMA,b)},i.prototype.packIHDR=function(a,b){let c=Buffer.alloc(13);return c.writeUInt32BE(a,0),c.writeUInt32BE(b,4),c[8]=this._options.bitDepth,c[9]=this._options.colorType,c[10]=0,c[11]=0,c[12]=0,this._packChunk(d.TYPE_IHDR,c)},i.prototype.packIDAT=function(a){return this._packChunk(d.TYPE_IDAT,a)},i.prototype.packIEND=function(){return this._packChunk(d.TYPE_IEND,null)}},77709:(a,b)=>{b.render=function(a,b,c){let d=a.modules.size,e=a.modules.data,f="\x1b[47m  \x1b[0m",g="",h=Array(d+3).join(f),i=[,,].join(f);g+=h+"\n";for(let a=0;a<d;++a){g+=f;for(let b=0;b<d;b++)g+=e[a*d+b]?"\x1b[40m  \x1b[0m":f;g+=i+"\n"}return g+=h+"\n","function"==typeof c&&c(null,g),g}},79428:a=>{"use strict";a.exports=require("buffer")},82961:a=>{function b(a){if(!a||a<1)throw Error("BitMatrix size must be defined and greater than 0");this.size=a,this.data=new Uint8Array(a*a),this.reservedBit=new Uint8Array(a*a)}b.prototype.set=function(a,b,c,d){let e=a*this.size+b;this.data[e]=c,d&&(this.reservedBit[e]=!0)},b.prototype.get=function(a,b){return this.data[a*this.size+b]},b.prototype.xor=function(a,b,c){this.data[a*this.size+b]^=c},b.prototype.isReserved=function(a,b){return this.reservedBit[a*this.size+b]},a.exports=b},84289:(a,b,c)=>{"use strict";let d=c(74361),e=c(67970),f=a.exports=function(a,b){this._options=a,a.checkCRC=!1!==a.checkCRC,this._hasIHDR=!1,this._hasIEND=!1,this._emittedHeadersFinished=!1,this._palette=[],this._colorType=0,this._chunks={},this._chunks[d.TYPE_IHDR]=this._handleIHDR.bind(this),this._chunks[d.TYPE_IEND]=this._handleIEND.bind(this),this._chunks[d.TYPE_IDAT]=this._handleIDAT.bind(this),this._chunks[d.TYPE_PLTE]=this._handlePLTE.bind(this),this._chunks[d.TYPE_tRNS]=this._handleTRNS.bind(this),this._chunks[d.TYPE_gAMA]=this._handleGAMA.bind(this),this.read=b.read,this.error=b.error,this.metadata=b.metadata,this.gamma=b.gamma,this.transColor=b.transColor,this.palette=b.palette,this.parsed=b.parsed,this.inflateData=b.inflateData,this.finished=b.finished,this.simpleTransparency=b.simpleTransparency,this.headersFinished=b.headersFinished||function(){}};f.prototype.start=function(){this.read(d.PNG_SIGNATURE.length,this._parseSignature.bind(this))},f.prototype._parseSignature=function(a){let b=d.PNG_SIGNATURE;for(let c=0;c<b.length;c++)if(a[c]!==b[c])return void this.error(Error("Invalid file signature"));this.read(8,this._parseChunkBegin.bind(this))},f.prototype._parseChunkBegin=function(a){let b=a.readUInt32BE(0),c=a.readUInt32BE(4),f="";for(let b=4;b<8;b++)f+=String.fromCharCode(a[b]);let g=!!(32&a[4]);return this._hasIHDR||c===d.TYPE_IHDR?(this._crc=new e,this._crc.write(Buffer.from(f)),this._chunks[c])?this._chunks[c](b):g?void this.read(b+4,this._skipChunk.bind(this)):void this.error(Error("Unsupported critical chunk type "+f)):void this.error(Error("Expected IHDR on beggining"))},f.prototype._skipChunk=function(){this.read(8,this._parseChunkBegin.bind(this))},f.prototype._handleChunkEnd=function(){this.read(4,this._parseChunkEnd.bind(this))},f.prototype._parseChunkEnd=function(a){let b=a.readInt32BE(0),c=this._crc.crc32();if(this._options.checkCRC&&c!==b)return void this.error(Error("Crc error - "+b+" - "+c));this._hasIEND||this.read(8,this._parseChunkBegin.bind(this))},f.prototype._handleIHDR=function(a){this.read(a,this._parseIHDR.bind(this))},f.prototype._parseIHDR=function(a){this._crc.write(a);let b=a.readUInt32BE(0),c=a.readUInt32BE(4),e=a[8],f=a[9],g=a[10],h=a[11],i=a[12];if(8!==e&&4!==e&&2!==e&&1!==e&&16!==e)return void this.error(Error("Unsupported bit depth "+e));if(!(f in d.COLORTYPE_TO_BPP_MAP))return void this.error(Error("Unsupported color type"));if(0!==g)return void this.error(Error("Unsupported compression method"));if(0!==h)return void this.error(Error("Unsupported filter method"));if(0!==i&&1!==i)return void this.error(Error("Unsupported interlace method"));this._colorType=f;let j=d.COLORTYPE_TO_BPP_MAP[this._colorType];this._hasIHDR=!0,this.metadata({width:b,height:c,depth:e,interlace:!!i,palette:!!(f&d.COLORTYPE_PALETTE),color:!!(f&d.COLORTYPE_COLOR),alpha:!!(f&d.COLORTYPE_ALPHA),bpp:j,colorType:f}),this._handleChunkEnd()},f.prototype._handlePLTE=function(a){this.read(a,this._parsePLTE.bind(this))},f.prototype._parsePLTE=function(a){this._crc.write(a);let b=Math.floor(a.length/3);for(let c=0;c<b;c++)this._palette.push([a[3*c],a[3*c+1],a[3*c+2],255]);this.palette(this._palette),this._handleChunkEnd()},f.prototype._handleTRNS=function(a){this.simpleTransparency(),this.read(a,this._parseTRNS.bind(this))},f.prototype._parseTRNS=function(a){if(this._crc.write(a),this._colorType===d.COLORTYPE_PALETTE_COLOR){if(0===this._palette.length)return void this.error(Error("Transparency chunk must be after palette"));if(a.length>this._palette.length)return void this.error(Error("More transparent colors than palette size"));for(let b=0;b<a.length;b++)this._palette[b][3]=a[b];this.palette(this._palette)}this._colorType===d.COLORTYPE_GRAYSCALE&&this.transColor([a.readUInt16BE(0)]),this._colorType===d.COLORTYPE_COLOR&&this.transColor([a.readUInt16BE(0),a.readUInt16BE(2),a.readUInt16BE(4)]),this._handleChunkEnd()},f.prototype._handleGAMA=function(a){this.read(a,this._parseGAMA.bind(this))},f.prototype._parseGAMA=function(a){this._crc.write(a),this.gamma(a.readUInt32BE(0)/d.GAMMA_DIVISION),this._handleChunkEnd()},f.prototype._handleIDAT=function(a){this._emittedHeadersFinished||(this._emittedHeadersFinished=!0,this.headersFinished()),this.read(-a,this._parseIDAT.bind(this,a))},f.prototype._parseIDAT=function(a,b){if(this._crc.write(b),this._colorType===d.COLORTYPE_PALETTE_COLOR&&0===this._palette.length)throw Error("Expected palette not found");this.inflateData(b);let c=a-b.length;c>0?this._handleIDAT(c):this._handleChunkEnd()},f.prototype._handleIEND=function(a){this.read(a,this._parseIEND.bind(this))},f.prototype._parseIEND=function(a){this._crc.write(a),this._hasIEND=!0,this._handleChunkEnd(),this.finished&&this.finished()}},84448:(a,b,c)=>{"use strict";let d=c(33631),e={0:function(a,b,c,d,e){for(let f=0;f<c;f++)d[e+f]=a[b+f]},1:function(a,b,c,d,e,f){for(let g=0;g<c;g++){let c=g>=f?a[b+g-f]:0,h=a[b+g]-c;d[e+g]=h}},2:function(a,b,c,d,e){for(let f=0;f<c;f++){let g=b>0?a[b+f-c]:0,h=a[b+f]-g;d[e+f]=h}},3:function(a,b,c,d,e,f){for(let g=0;g<c;g++){let h=g>=f?a[b+g-f]:0,i=b>0?a[b+g-c]:0,j=a[b+g]-(h+i>>1);d[e+g]=j}},4:function(a,b,c,e,f,g){for(let h=0;h<c;h++){let i=h>=g?a[b+h-g]:0,j=b>0?a[b+h-c]:0,k=b>0&&h>=g?a[b+h-(c+g)]:0,l=a[b+h]-d(i,j,k);e[f+h]=l}}},f={0:function(a,b,c){let d=0,e=b+c;for(let c=b;c<e;c++)d+=Math.abs(a[c]);return d},1:function(a,b,c,d){let e=0;for(let f=0;f<c;f++){let c=f>=d?a[b+f-d]:0;e+=Math.abs(a[b+f]-c)}return e},2:function(a,b,c){let d=0,e=b+c;for(let f=b;f<e;f++){let e=b>0?a[f-c]:0;d+=Math.abs(a[f]-e)}return d},3:function(a,b,c,d){let e=0;for(let f=0;f<c;f++){let g=f>=d?a[b+f-d]:0,h=b>0?a[b+f-c]:0;e+=Math.abs(a[b+f]-(g+h>>1))}return e},4:function(a,b,c,e){let f=0;for(let g=0;g<c;g++){let h=g>=e?a[b+g-e]:0,i=b>0?a[b+g-c]:0,j=b>0&&g>=e?a[b+g-(c+e)]:0;f+=Math.abs(a[b+g]-d(h,i,j))}return f}};a.exports=function(a,b,c,d,g){let h;if("filterType"in d&&-1!==d.filterType)if("number"==typeof d.filterType)h=[d.filterType];else throw Error("unrecognised filter types");else h=[0,1,2,3,4];16===d.bitDepth&&(g*=2);let i=b*g,j=0,k=0,l=Buffer.alloc((i+1)*c),m=h[0];for(let b=0;b<c;b++){if(h.length>1){let b=1/0;for(let c=0;c<h.length;c++){let d=f[h[c]](a,k,i,g);d<b&&(m=h[c],b=d)}}l[j]=m,j++,e[m](a,k,i,l,j,g),j+=i,k+=i}return l}},85350:(a,b,c)=>{b.render=c(23023).render,b.renderToFile=function(a,d,e,f){void 0===f&&(f=e,e=void 0);let g=c(29021),h=b.render(d,e);g.writeFile(a,'<?xml version="1.0" encoding="utf-8"?><!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">'+h,f)}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},87493:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>g});var d=c(78157),e=c(31768),f=c(71159);function g(){let[a,b]=(0,e.useState)(null),[c,g]=(0,e.useState)([]),[h,i]=(0,e.useState)(""),[j,k]=(0,e.useState)(!0),[l,m]=(0,e.useState)(""),n=(0,f.useRouter)();(0,f.useParams)().id;let o=a=>a?new Date(a).toLocaleDateString("ar-SA"):"غير محدد";return j?(0,d.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-gray-600",children:"جاري تحميل البيانات..."})]})}):l||!a?(0,d.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("p",{className:"text-red-600 text-lg mb-4",children:l||"الموظف غير موجود"}),(0,d.jsx)("button",{type:"button",onClick:()=>n.push("/employees"),className:"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors",children:"العودة لقائمة الموظفين"})]})}):(0,d.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,d.jsx)("div",{className:"no-print bg-gray-50 border-b p-4",children:(0,d.jsxs)("div",{className:"max-w-4xl mx-auto flex justify-between items-center",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4 space-x-reverse",children:[(0,d.jsx)("h1",{className:"text-lg font-semibold text-gray-900",children:"طباعة بيانات الموظف"}),(0,d.jsx)("button",{type:"button",onClick:()=>n.push("/employees"),className:"text-blue-600 hover:text-blue-800 text-sm",children:"← العودة لقائمة الموظفين"})]}),(0,d.jsx)("button",{type:"button",onClick:()=>{window.print()},className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"طباعة"})]})}),(0,d.jsxs)("div",{className:"max-w-4xl mx-auto p-8",children:[(0,d.jsxs)("div",{className:"text-center mb-8 border-b-2 border-gray-300 pb-6",children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"الجمهورية الليبية"}),(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-2",children:"وزارة الداخلية"}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-700",children:"بطاقة بيانات الموظف"})]}),(0,d.jsx)("div",{className:"flex justify-center mb-6",children:(0,d.jsx)("div",{className:"w-32 h-40 bg-gray-200 border-2 border-gray-300 flex items-center justify-center",children:(0,d.jsx)("span",{className:"text-gray-500 text-sm",children:"صورة الموظف"})})}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-6 mb-8",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex border-b border-gray-200 pb-2",children:[(0,d.jsx)("span",{className:"font-semibold text-gray-700 w-32",children:"الاسم الكامل:"}),(0,d.jsx)("span",{className:"text-gray-900",children:a.name})]}),(0,d.jsxs)("div",{className:"flex border-b border-gray-200 pb-2",children:[(0,d.jsx)("span",{className:"font-semibold text-gray-700 w-32",children:"الرتبة:"}),(0,d.jsx)("span",{className:"text-gray-900",children:(a=>{let b=c.find(b=>b.key===a);return b?b.label:a})(a.rankKey)})]}),(0,d.jsxs)("div",{className:"flex border-b border-gray-200 pb-2",children:[(0,d.jsx)("span",{className:"font-semibold text-gray-700 w-32",children:"الفئة:"}),(0,d.jsx)("span",{className:"text-gray-900",children:(a=>{let b=c.find(b=>b.key===a);if(!b)return"غير محدد";switch(b.category){case"officer":return"ضابط";case"nco":return"ضابط صف";case"employee":return"موظف";default:return"غير محدد"}})(a.rankKey)})]}),(0,d.jsxs)("div",{className:"flex border-b border-gray-200 pb-2",children:[(0,d.jsx)("span",{className:"font-semibold text-gray-700 w-32",children:"الرقم العسكري:"}),(0,d.jsx)("span",{className:"text-gray-900 font-mono",children:a.militaryNumber})]}),(0,d.jsxs)("div",{className:"flex border-b border-gray-200 pb-2",children:[(0,d.jsx)("span",{className:"font-semibold text-gray-700 w-32",children:"الرقم الوطني:"}),(0,d.jsx)("span",{className:"text-gray-900 font-mono",children:a.nationalId})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex border-b border-gray-200 pb-2",children:[(0,d.jsx)("span",{className:"font-semibold text-gray-700 w-32",children:"تاريخ التعيين:"}),(0,d.jsx)("span",{className:"text-gray-900",children:o(a.appointmentDate)})]}),(0,d.jsxs)("div",{className:"flex border-b border-gray-200 pb-2",children:[(0,d.jsx)("span",{className:"font-semibold text-gray-700 w-32",children:"تاريخ الميلاد:"}),(0,d.jsx)("span",{className:"text-gray-900",children:o(a.birthDate)})]}),(0,d.jsxs)("div",{className:"flex border-b border-gray-200 pb-2",children:[(0,d.jsx)("span",{className:"font-semibold text-gray-700 w-32",children:"اسم الأم:"}),(0,d.jsx)("span",{className:"text-gray-900",children:a.motherName||"غير محدد"})]}),(0,d.jsxs)("div",{className:"flex border-b border-gray-200 pb-2",children:[(0,d.jsx)("span",{className:"font-semibold text-gray-700 w-32",children:"فصيلة الدم:"}),(0,d.jsx)("span",{className:"text-gray-900",children:a.bloodType||"غير محدد"})]}),(0,d.jsxs)("div",{className:"flex border-b border-gray-200 pb-2",children:[(0,d.jsx)("span",{className:"font-semibold text-gray-700 w-32",children:"الوحدة:"}),(0,d.jsx)("span",{className:"text-gray-900",children:"الخمس"})]})]})]}),(0,d.jsxs)("div",{className:"flex justify-between items-end mt-12",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-24 h-24 border border-gray-300 mb-2 flex items-center justify-center",children:h?(0,d.jsx)("img",{src:h,alt:"QR Code",className:"w-full h-full"}):(0,d.jsx)("span",{className:"text-gray-500 text-xs",children:"QR Code"})}),(0,d.jsx)("p",{className:"text-xs text-gray-600",children:"رمز الاستجابة السريعة"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"border-t border-gray-400 w-48 mb-2"}),(0,d.jsx)("p",{className:"text-sm text-gray-700",children:"توقيع المسؤول"})]})]}),(0,d.jsxs)("div",{className:"mt-12 pt-6 border-t border-gray-300 text-center text-sm text-gray-600",children:[(0,d.jsxs)("p",{children:["تاريخ الطباعة: ",new Date().toLocaleDateString("ar-SA")]}),(0,d.jsx)("p",{className:"mt-1",children:"نظام إدارة الموارد البشرية - Mergeb HR"})]})]})]})}c(36530)},87815:(a,b,c)=>{"use strict";let d=c(28354),e=c(36833),f=c(35722),g=a.exports=function(a){e.call(this);let b=[],c=this;this._filter=new f(a,{read:this.read.bind(this),write:function(a){b.push(a)},complete:function(){c.emit("complete",Buffer.concat(b))}}),this._filter.start()};d.inherits(g,e)},88799:(a,b,c)=>{Promise.resolve().then(c.bind(c,87493))},89097:(a,b)=>{function c(a){if("number"==typeof a&&(a=a.toString()),"string"!=typeof a)throw Error("Color should be defined as hex string");let b=a.slice().replace("#","").split("");if(b.length<3||5===b.length||b.length>8)throw Error("Invalid hex color: "+a);(3===b.length||4===b.length)&&(b=Array.prototype.concat.apply([],b.map(function(a){return[a,a]}))),6===b.length&&b.push("F","F");let c=parseInt(b.join(""),16);return{r:c>>24&255,g:c>>16&255,b:c>>8&255,a:255&c,hex:"#"+b.slice(0,6).join("")}}b.getOptions=function(a){a||(a={}),a.color||(a.color={});let b=void 0===a.margin||null===a.margin||a.margin<0?4:a.margin,d=a.width&&a.width>=21?a.width:void 0,e=a.scale||4;return{width:d,scale:d?4:e,margin:b,color:{dark:c(a.color.dark||"#000000ff"),light:c(a.color.light||"#ffffffff")},type:a.type,rendererOpts:a.rendererOpts||{}}},b.getScale=function(a,b){return b.width&&b.width>=a+2*b.margin?b.width/(a+2*b.margin):b.scale},b.getImageWidth=function(a,c){let d=b.getScale(a,c);return Math.floor((a+2*c.margin)*d)},b.qrToImageData=function(a,c,d){let e=c.modules.size,f=c.modules.data,g=b.getScale(e,d),h=Math.floor((e+2*d.margin)*g),i=d.margin*g,j=[d.color.light,d.color.dark];for(let b=0;b<h;b++)for(let c=0;c<h;c++){let k=(b*h+c)*4,l=d.color.light;b>=i&&c>=i&&b<h-i&&c<h-i&&(l=j[+!!f[Math.floor((b-i)/g)*e+Math.floor((c-i)/g)]]),a[k++]=l.r,a[k++]=l.g,a[k++]=l.b,a[k]=l.a}}},89701:(a,b)=>{b.isValid=function(a){return!isNaN(a)&&a>=1&&a<=40}},90701:(a,b,c)=>{let d=c(34809);function e(a){this.mode=d.BYTE,"string"==typeof a?this.data=new TextEncoder().encode(a):this.data=new Uint8Array(a)}e.getBitsLength=function(a){return 8*a},e.prototype.getLength=function(){return this.data.length},e.prototype.getBitsLength=function(){return e.getBitsLength(this.data.length)},e.prototype.write=function(a){for(let b=0,c=this.data.length;b<c;b++)a.put(this.data[b],8)},a.exports=e},93729:(a,b,c)=>{"use strict";let d=c(74361);a.exports=function(a,b,c,e){let f=-1!==[d.COLORTYPE_COLOR_ALPHA,d.COLORTYPE_ALPHA].indexOf(e.colorType);if(e.colorType===e.inputColorType){let b,c=(new DataView(b=new ArrayBuffer(2)).setInt16(0,256,!0),256!==new Int16Array(b)[0]);if(8===e.bitDepth||16===e.bitDepth&&c)return a}let g=16!==e.bitDepth?a:new Uint16Array(a.buffer),h=255,i=d.COLORTYPE_TO_BPP_MAP[e.inputColorType];4!==i||e.inputHasAlpha||(i=3);let j=d.COLORTYPE_TO_BPP_MAP[e.colorType];16===e.bitDepth&&(h=65535,j*=2);let k=Buffer.alloc(b*c*j),l=0,m=0,n=e.bgColor||{};void 0===n.red&&(n.red=h),void 0===n.green&&(n.green=h),void 0===n.blue&&(n.blue=h);for(let a=0;a<c;a++)for(let a=0;a<b;a++){let a=function(){let a,b,c,i=h;switch(e.inputColorType){case d.COLORTYPE_COLOR_ALPHA:i=g[l+3],a=g[l],b=g[l+1],c=g[l+2];break;case d.COLORTYPE_COLOR:a=g[l],b=g[l+1],c=g[l+2];break;case d.COLORTYPE_ALPHA:i=g[l+1],b=a=g[l],c=a;break;case d.COLORTYPE_GRAYSCALE:b=a=g[l],c=a;break;default:throw Error("input color type:"+e.inputColorType+" is not supported at present")}return e.inputHasAlpha&&!f&&(i/=h,a=Math.min(Math.max(Math.round((1-i)*n.red+i*a),0),h),b=Math.min(Math.max(Math.round((1-i)*n.green+i*b),0),h),c=Math.min(Math.max(Math.round((1-i)*n.blue+i*c),0),h)),{red:a,green:b,blue:c,alpha:i}}(g,l);switch(e.colorType){case d.COLORTYPE_COLOR_ALPHA:case d.COLORTYPE_COLOR:8===e.bitDepth?(k[m]=a.red,k[m+1]=a.green,k[m+2]=a.blue,f&&(k[m+3]=a.alpha)):(k.writeUInt16BE(a.red,m),k.writeUInt16BE(a.green,m+2),k.writeUInt16BE(a.blue,m+4),f&&k.writeUInt16BE(a.alpha,m+6));break;case d.COLORTYPE_ALPHA:case d.COLORTYPE_GRAYSCALE:{let b=(a.red+a.green+a.blue)/3;8===e.bitDepth?(k[m]=b,f&&(k[m+1]=a.alpha)):(k.writeUInt16BE(b,m),f&&k.writeUInt16BE(a.alpha,m+2));break}default:throw Error("unrecognised color Type "+e.colorType)}l+=i,m+=j}return k}},96892:(a,b)=>{b.L={bit:1},b.M={bit:0},b.Q={bit:3},b.H={bit:2},b.isValid=function(a){return a&&void 0!==a.bit&&a.bit>=0&&a.bit<4},b.from=function(a,c){if(b.isValid(a))return a;try{if("string"!=typeof a)throw Error("Param is not a string");switch(a.toLowerCase()){case"l":case"low":return b.L;case"m":case"medium":return b.M;case"q":case"quartile":return b.Q;case"h":case"high":return b.H;default:throw Error("Unknown EC Level: "+a)}}catch(a){return c}}},98528:(a,b,c)=>{"use strict";let d=c(71981),e=c(35722);b.process=function(a,b){let c=[],f=new d(a);return new e(b,{read:f.read.bind(f),write:function(a){c.push(a)},complete:function(){}}).start(),f.process(),Buffer.concat(c)}},99278:(a,b,c)=>{let d=c(18805).getSymbolSize;b.getRowColCoords=function(a){if(1===a)return[];let b=Math.floor(a/7)+2,c=d(a),e=145===c?26:2*Math.ceil((c-13)/(2*b-2)),f=[c-7];for(let a=1;a<b-1;a++)f[a]=f[a-1]-e;return f.push(6),f.reverse()},b.getPositions=function(a){let c=[],d=b.getRowColCoords(a),e=d.length;for(let a=0;a<e;a++)for(let b=0;b<e;b++)(0!==a||0!==b)&&(0!==a||b!==e-1)&&(a!==e-1||0!==b)&&c.push([d[a],d[b]]);return c}},99534:(a,b)=>{let c=new Uint8Array(512),d=new Uint8Array(256);!function(){let a=1;for(let b=0;b<255;b++)c[b]=a,d[a]=b,256&(a<<=1)&&(a^=285);for(let a=255;a<512;a++)c[a]=c[a-255]}(),b.log=function(a){if(a<1)throw Error("log("+a+")");return d[a]},b.exp=function(a){return c[a]},b.mul=function(a,b){return 0===a||0===b?0:c[d[a]+d[b]]}},99985:(a,b,c)=>{"use strict";let d=!0,e=c(74075),f=c(46003);e.deflateSync||(d=!1);let g=c(71981),h=c(98528),i=c(84289),j=c(49822),k=c(70050);a.exports=function(a,b){let c,l,m,n;if(!d)throw Error("To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0");let o=[],p=new g(a);if(new i(b,{read:p.read.bind(p),error:function(a){c=a},metadata:function(a){l=a},gamma:function(a){m=a},palette:function(a){l.palette=a},transColor:function(a){l.transColor=a},inflateData:function(a){o.push(a)},simpleTransparency:function(){l.alpha=!0}}).start(),p.process(),c)throw c;let q=Buffer.concat(o);if(o.length=0,l.interlace)n=e.inflateSync(q);else{let a=((l.width*l.bpp*l.depth+7>>3)+1)*l.height;n=f(q,{chunkSize:a,maxLength:a})}if(q=null,!n||!n.length)throw Error("bad png - invalid inflate data response");let r=h.process(n,l);q=null;let s=j.dataToBitMap(r,l);r=null;let t=k(s,l);return l.data=t,l.gamma=m||0,l}}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[635,533],()=>b(b.s=64850));module.exports=c})();
# 🏛️ نظام إدارة الموارد البشرية - مرجب

نظام شامل لإدارة الموارد البشرية والقوة العمومية للمؤسسات الحكومية العراقية.

## 🌟 الأنظمة المتاحة

### 👥 نظام الموارد البشرية (المنفذ 3001)
- إدارة الموظفين والرتب والوحدات
- استيراد من Excel مع التحقق
- إدارة المستخدمين والصلاحيات
- التقارير والإحصائيات
- النسخ الاحتياطية

### 🔫 نظام القوة العمومية (المنفذ 3002)
- إدارة الأسلحة والمركبات واللاسلكي
- تسليم واستلام الأصول
- متابعة حالة الصيانة
- إحصائيات مفصلة
- واجهة منفصلة ومستقلة

## ✨ الميزات الرئيسية

### 🎨 **تصميم عصري وجذاب**
- خلفيات متدرجة جميلة
- بطاقات شفافة مع تمويه خلفي
- أزرار بألوان متدرجة
- تأثيرات hover وانتقالات سلسة
- أيقونات حديثة ورموز تعبيرية

### 🔍 **بحث متقدم وسريع**
- بحث فوري أثناء الكتابة
- فلاتر متقدمة قابلة للطي
- بحث في جميع الحقول
- نتائج سريعة ودقيقة

### 🗑️ **إمكانيات حذف آمنة**
- تأكيدات قبل الحذف
- رسائل واضحة للمستخدم
- حماية من الحذف العرضي

### ⚡ **أداء فائق السرعة**
- استجابة فورية (< 50ms)
- قاعدة بيانات محلية سريعة
- لا حاجة لـ Docker أو قواعد بيانات خارجية

### 📊 **إدارة شاملة**
- إدارة الموظفين مع عمليات CRUD كاملة
- إدارة الرتب والوحدات
- إنتاج التقارير والطباعة
- استيراد/تصدير Excel
- إدارة المستخدمين مع الصلاحيات
- النسخ الاحتياطية والاستعادة

## 🚀 التشغيل السريع

### الطريقة الأولى: التشغيل المباشر (موصى به)

1. **المتطلبات:**
   - Node.js 18+ مثبت
   - Git مثبت

2. **تشغيل النظام:**
   ```bash
   # استنساخ وتشغيل
   git clone <repository-url>
   cd mergeb-hr

   # Windows - انقر مرتين على الملف
   start-system.bat

   # أو استخدم الملف القديم
   run-local.bat
   ```

3. **الوصول للنظام:**
   - نظام الموارد البشرية: http://127.0.0.1:3001
   - نظام القوة العمومية: http://127.0.0.1:3002
   - Personnel API: http://127.0.0.1:4001
   - Assets API: http://127.0.0.1:4002

## 🏗️ هيكل النظام

```
mergeb-hr/
├── apps/
│   ├── personnel-api/      # API الموارد البشرية
│   ├── personnel-frontend/ # واجهة الموارد البشرية
│   ├── assets-api/         # API القوة العمومية
│   └── assets-frontend/    # واجهة القوة العمومية
├── start-system.bat       # تشغيل سريع محسن
├── run-local.bat          # تشغيل سريع أساسي
└── README.md              # هذا الملف
```

## 🔐 تسجيل الدخول الافتراضي

- اسم المستخدم: أي قيمة
- كلمة المرور: أي قيمة

النظام يستخدم مصادقة تجريبية للتطوير.

ملاحظات حول البيانات:
- ترتيب الرتب سيتم حفظه كثوابت مشتركة لضمان ترتيب التقارير.
- قواعد الرقم العسكري:
  - لضباط الصف: رقم عسكري إجباري
  - الضباط: يُكتب "بلا"
  - الموظفون: يُكتب "موظف"

الطباعة والتقارير:
- سيتم اعتماد تنسيقات A4 وهوامش 2 سم من كل الجهات.
- التصدير: PDF, Excel, Word

الصلاحيات (RBAC):
- مدير النظام، مشرف شؤون الموظفين، مستخدم عادي
- صلاحيات: قراءة فقط، تعديل، حذف

الخطوات التالية:
- بعد موافقتك سنقوم بتوليد هياكل Next.js و Express وتثبيت الاعتمادات وكتابة نقاط البداية والراوترات الأساسية.


# 🏛️ نظام مرجب الشامل - الإصدار 2.0

نظام متطور وحديث لإدارة الموارد البشرية والقوة العمومية للمؤسسات الحكومية العراقية.

## 🆕 جديد في الإصدار 2.0

### 🎨 **تصميم حديث بالكامل**
- واجهة مستخدم عصرية مع Tailwind CSS 4.0
- تصميم متجاوب يدعم جميع الأجهزة
- ألوان متدرجة وتأثيرات بصرية جذابة
- دعم كامل للغة العربية (RTL)
- إمكانية الوصول المحسنة (Accessibility)

### ⚡ **أداء محسن**
- سرعة تحميل فائقة
- استجابة فورية للواجهات
- تحسينات في قاعدة البيانات
- معالجة محسنة للبيانات الكبيرة

### 🔧 **أدوات تطوير محسنة**
- ملفات تشغيل محسنة مع مراقبة الحالة
- وضع تطوير منفصل للمطورين
- رسائل خطأ واضحة ومفيدة
- فحص صحة الخدمات التلقائي

## 🌟 الأنظمة المتاحة

### 👥 نظام الموارد البشرية (المنفذ 3001)
- 📋 إدارة شاملة للموظفين والرتب والوحدات
- 📊 استيراد محسن من Excel مع التحقق المتقدم
- 👤 إدارة المستخدمين والصلاحيات
- 📈 تقارير تفاعلية وإحصائيات مرئية
- 💾 نسخ احتياطية آمنة ومجدولة
- 🖼️ دعم الصور الشخصية للموظفين
- 🔍 بحث ذكي ومتقدم

### 🔫 نظام القوة العمومية (المنفذ 3002)
- 🔫 إدارة الأسلحة والذخائر
- 🚗 إدارة المركبات والآليات
- 📻 إدارة أجهزة اللاسلكي والاتصالات
- 📦 تسليم واستلام الأصول مع التتبع
- 🔧 متابعة حالة الصيانة والإصلاحات
- 📊 إحصائيات مفصلة ولوحات معلومات
- 🏷️ نظام QR Code للتتبع السريع

## ✨ الميزات الرئيسية

### 🎨 **تصميم عصري وجذاب**
- خلفيات متدرجة جميلة
- بطاقات شفافة مع تمويه خلفي
- أزرار بألوان متدرجة
- تأثيرات hover وانتقالات سلسة
- أيقونات حديثة ورموز تعبيرية

### 🔍 **بحث متقدم وسريع**
- بحث فوري أثناء الكتابة
- فلاتر متقدمة قابلة للطي
- بحث في جميع الحقول
- نتائج سريعة ودقيقة

### 🗑️ **إمكانيات حذف آمنة**
- تأكيدات قبل الحذف
- رسائل واضحة للمستخدم
- حماية من الحذف العرضي

### ⚡ **أداء فائق السرعة**
- استجابة فورية (< 50ms)
- قاعدة بيانات محلية سريعة
- لا حاجة لـ Docker أو قواعد بيانات خارجية

### 📊 **إدارة شاملة**
- إدارة الموظفين مع عمليات CRUD كاملة
- إدارة الرتب والوحدات
- إنتاج التقارير والطباعة
- استيراد/تصدير Excel
- إدارة المستخدمين مع الصلاحيات
- النسخ الاحتياطية والاستعادة

## 🚀 التشغيل السريع

### 🎯 للاستخدام العادي (مستحسن)
```bash
# تشغيل النظام بالكامل مع فحص الحالة والمراقبة
start-system.bat
```

### 🔧 للمطورين
```bash
# وضع التطوير مع Hot Reload وأدوات التطوير
dev-start.bat
```

### 🛑 إيقاف النظام
```bash
# إيقاف جميع الخدمات بأمان
stop-system.bat
```

### 📋 المتطلبات
- **Node.js 18+** مثبت
- **Windows 10/11** (ملفات .bat)
- **متصفح حديث** (Chrome, Firefox, Edge)

### 🔗 الروابط والمنافذ

#### 🌐 واجهات المستخدم
- **نظام الموارد البشرية**: http://127.0.0.1:3001
- **نظام القوة العمومية**: http://127.0.0.1:3002

#### 🔌 APIs
- **Personnel API**: http://127.0.0.1:4001
- **Assets API**: http://127.0.0.1:4002

#### 🔍 فحص الحالة
- **Personnel API Health**: http://127.0.0.1:4001/health
- **Assets API Health**: http://127.0.0.1:4002/health

## 🏗️ هيكل النظام المحدث

```
mergeb-hr/
├── apps/
│   ├── personnel-api/      # 🔌 API الموارد البشرية (TypeScript)
│   ├── personnel-frontend/ # 🎨 واجهة الموارد البشرية (Next.js + Tailwind)
│   ├── assets-api/         # 🔌 API القوة العمومية (TypeScript)
│   └── assets-frontend/    # 🎨 واجهة القوة العمومية (Next.js + Tailwind)
├── packages/
│   └── shared/             # 📦 مكونات مشتركة ومكتبات
├── start-system.bat        # 🚀 تشغيل النظام الشامل (محسن)
├── dev-start.bat           # 🔧 وضع التطوير للمطورين
├── stop-system.bat         # 🛑 إيقاف جميع الخدمات
├── run-local.bat           # 📜 تشغيل أساسي (قديم)
└── README.md               # 📖 دليل الاستخدام
```

### 🔧 ملفات التشغيل الجديدة

- **`start-system.bat`**: تشغيل شامل مع فحص الحالة والمراقبة
- **`dev-start.bat`**: وضع التطوير مع Hot Reload
- **`stop-system.bat`**: إيقاف آمن لجميع الخدمات

## 🔐 تسجيل الدخول الافتراضي

- اسم المستخدم: أي قيمة
- كلمة المرور: أي قيمة

النظام يستخدم مصادقة تجريبية للتطوير.

ملاحظات حول البيانات:
- ترتيب الرتب سيتم حفظه كثوابت مشتركة لضمان ترتيب التقارير.
- قواعد الرقم العسكري:
  - لضباط الصف: رقم عسكري إجباري
  - الضباط: يُكتب "بلا"
  - الموظفون: يُكتب "موظف"

الطباعة والتقارير:
- سيتم اعتماد تنسيقات A4 وهوامش 2 سم من كل الجهات.
- التصدير: PDF, Excel, Word

الصلاحيات (RBAC):
- مدير النظام، مشرف شؤون الموظفين، مستخدم عادي
- صلاحيات: قراءة فقط، تعديل، حذف

الخطوات التالية:
- بعد موافقتك سنقوم بتوليد هياكل Next.js و Express وتثبيت الاعتمادات وكتابة نقاط البداية والراوترات الأساسية.


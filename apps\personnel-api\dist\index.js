import 'dotenv/config';
import express from 'express';
import helmet from 'helmet';
import cors from 'cors';
import jwt from 'jsonwebtoken';
import { RANKS, UNITS, isOfficer, isNCO, isEmployee } from '@mergeb/shared';
import { authRequired, requirePermission } from './middleware/auth.js';
// Using in-memory storage for faster performance
const employees = [];
const users = [
    {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        role: 'admin',
        isActive: true,
        createdAt: new Date().toISOString(),
        lastLogin: new Date().toISOString()
    }
];
let nextEmployeeId = 1;
let nextUserId = 2;
const app = express();
app.use(helmet());
app.use(cors());
app.use(express.json());
const PORT = process.env.PORT || 4001;
const JWT_SECRET = process.env.JWT_SECRET || 'dev_secret';
// auth: public routes first
app.post('/auth/login', (req, res) => {
    // TODO: validate user against DB
    const token = jwt.sign({ role: 'admin', sub: 'demo' }, JWT_SECRET, { expiresIn: '8h' });
    res.json({ token });
});
app.get('/health', (_, res) => {
    res.json({ ok: true, db: 'in-memory', performance: 'optimized' });
});
// protected
app.use(authRequired);
app.get('/ranks', requirePermission('read'), (_, res) => {
    res.json(RANKS);
});
app.get('/units', requirePermission('read'), (_, res) => {
    res.json(UNITS);
});
// User management endpoints
app.get('/users', requirePermission('write'), (req, res) => {
    res.json(users);
});
app.post('/users', requirePermission('write'), (req, res) => {
    const { username, email, password, role, isActive } = req.body;
    const newUser = {
        id: nextUserId++,
        username,
        email,
        role,
        isActive: isActive !== false,
        createdAt: new Date().toISOString(),
        lastLogin: null
    };
    users.push(newUser);
    res.json(newUser);
});
app.put('/users/:id', requirePermission('write'), (req, res) => {
    const { id } = req.params;
    const { username, email, password, role, isActive } = req.body;
    const userIndex = users.findIndex(u => u.id === parseInt(id));
    if (userIndex === -1) {
        return res.status(404).json({ error: 'المستخدم غير موجود' });
    }
    users[userIndex] = {
        ...users[userIndex],
        username,
        email,
        role,
        isActive
    };
    res.json(users[userIndex]);
});
app.delete('/users/:id', requirePermission('write'), (req, res) => {
    const { id } = req.params;
    const userIndex = users.findIndex(u => u.id === parseInt(id));
    if (userIndex === -1) {
        return res.status(404).json({ error: 'المستخدم غير موجود' });
    }
    users.splice(userIndex, 1);
    res.json({ success: true });
});
app.patch('/users/:id/status', requirePermission('write'), (req, res) => {
    const { id } = req.params;
    const { isActive } = req.body;
    const userIndex = users.findIndex(u => u.id === parseInt(id));
    if (userIndex === -1) {
        return res.status(404).json({ error: 'المستخدم غير موجود' });
    }
    users[userIndex].isActive = isActive;
    res.json({ success: true });
});
// Employee CRUD operations
app.get('/employees', requirePermission('read'), (req, res) => {
    const { search, rank, unit, status } = req.query;
    let filteredEmployees = [...employees];
    // Search functionality
    if (search) {
        const searchTerm = search.toLowerCase();
        filteredEmployees = filteredEmployees.filter(emp => emp.name.toLowerCase().includes(searchTerm) ||
            emp.nationalId.includes(searchTerm) ||
            emp.militaryNumber.toLowerCase().includes(searchTerm));
    }
    // Filter by rank
    if (rank) {
        filteredEmployees = filteredEmployees.filter(emp => emp.rankKey === rank);
    }
    // Filter by unit
    if (unit) {
        filteredEmployees = filteredEmployees.filter(emp => emp.unitId === parseInt(unit));
    }
    // Filter by status
    if (status) {
        filteredEmployees = filteredEmployees.filter(emp => emp.employmentStatus === status);
    }
    res.json(filteredEmployees);
});
app.post('/employees', requirePermission('write'), async (req, res) => {
    const { name, rankKey, militaryNumber, nationalId, unitId, birthDate, birthPlace, motherName, maritalStatus, appointmentDate, lastPromotionDate, bankName, bankBranch, bankAccount, leaveBalance, leaveType, bloodType, employeeStatus, qualification, qualificationDate } = req.body || {};
    // Validation
    const rank = RANKS.find(r => r.key === rankKey);
    if (!rank)
        return res.status(400).json({ error: 'رتبة غير صحيحة' });
    if (isNCO(rankKey)) {
        if (!militaryNumber || String(militaryNumber).length === 0) {
            return res.status(400).json({ error: 'الرقم العسكري إجباري لضباط الصف' });
        }
    }
    else if (isOfficer(rankKey)) {
        if (militaryNumber !== 'بلا') {
            return res.status(400).json({ error: 'بالنسبة للضباط يجب أن تكون قيمة الرقم العسكري "بلا"' });
        }
    }
    else if (isEmployee(rankKey)) {
        if (militaryNumber !== 'موظف') {
            return res.status(400).json({ error: 'بالنسبة للموظفين يجب أن تكون قيمة الرقم العسكري "موظف"' });
        }
    }
    if (!nationalId || String(nationalId).length !== 12) {
        return res.status(400).json({ error: 'الرقم الوطني يجب أن يكون 12 رقمًا' });
    }
    // Check if national ID already exists
    const existingEmployee = employees.find(emp => emp.nationalId === nationalId);
    if (existingEmployee) {
        return res.status(400).json({ error: 'الرقم الوطني موجود مسبقاً' });
    }
    const newEmployee = {
        id: nextEmployeeId++,
        name,
        rankKey,
        militaryNumber,
        nationalId,
        unitId: unitId || null,
        birthDate: birthDate || null,
        birthPlace: birthPlace || null,
        motherName: motherName || null,
        maritalStatus: maritalStatus || null,
        appointmentDate: appointmentDate || null,
        lastPromotionDate: lastPromotionDate || null,
        bankName: bankName || null,
        bankBranch: bankBranch || null,
        bankAccount: bankAccount || null,
        leaveBalance: leaveBalance || 0,
        leaveType: leaveType || null,
        bloodType: bloodType || null,
        employmentStatus: employeeStatus || 'مستمر',
        qualification: qualification || null,
        qualificationDate: qualificationDate || null,
        createdAt: new Date().toISOString()
    };
    employees.push(newEmployee);
    res.status(201).json(newEmployee);
});
app.get('/employees/:id', requirePermission('read'), (req, res) => {
    const employee = employees.find(emp => emp.id === parseInt(req.params.id));
    if (!employee) {
        return res.status(404).json({ error: 'الموظف غير موجود' });
    }
    res.json(employee);
});
app.delete('/employees/:id', requirePermission('write'), (req, res) => {
    const employeeIndex = employees.findIndex(emp => emp.id === parseInt(req.params.id));
    if (employeeIndex === -1) {
        return res.status(404).json({ error: 'الموظف غير موجود' });
    }
    employees.splice(employeeIndex, 1);
    res.json({ success: true });
});
// Backup and Restore endpoints
app.post('/backup', requirePermission('write'), (req, res) => {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `backup-${timestamp}.json`;
    const backupData = {
        timestamp: new Date().toISOString(),
        employees,
        users,
        version: '1.0'
    };
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.json(backupData);
});
app.post('/restore', requirePermission('write'), (req, res) => {
    try {
        const { backupData } = req.body;
        if (!backupData) {
            return res.status(400).json({ error: 'بيانات النسخة الاحتياطية مطلوبة' });
        }
        // Restore data
        if (backupData.employees) {
            employees.length = 0;
            employees.push(...backupData.employees);
            nextEmployeeId = Math.max(...employees.map((e) => e.id)) + 1;
        }
        if (backupData.users) {
            users.length = 0;
            users.push(...backupData.users);
            nextUserId = Math.max(...users.map((u) => u.id)) + 1;
        }
        res.json({ message: 'تم استعادة النسخة الاحتياطية بنجاح' });
    }
    catch (error) {
        console.error('Error restoring backup:', error);
        res.status(500).json({ error: 'خطأ في استعادة النسخة الاحتياطية: ' + error.message });
    }
});
// reports skeleton
app.get('/reports/by-rank', requirePermission('read'), (_, res) => {
    // TODO: query and sort by rank order
    res.json([]);
});
app.get('/reports/by-unit', requirePermission('read'), (_, res) => {
    res.json([]);
});
app.get('/reports/by-category', requirePermission('read'), (_, res) => {
    res.json([]);
});
app.get('/reports/by-status', requirePermission('read'), (_, res) => {
    res.json([]);
});
app.listen(PORT, () => {
    console.log(`Personnel API listening on :${PORT}`);
});

/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/employees/new/page"],{

/***/ "(app-pages-browser)/../../node_modules/next/dist/api/navigation.js":
/*!******************************************************!*\
  !*** ../../node_modules/next/dist/api/navigation.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/../../node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2FwaS9uYXZpZ2F0aW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFnRDs7QUFFaEQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcaGFtemFcXERlc2t0b3BcXG1lcmdlYiBIUlxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxhcGlcXG5hdmlnYXRpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi4vY2xpZW50L2NvbXBvbmVudHMvbmF2aWdhdGlvbic7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW5hdmlnYXRpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chamza%5C%5CDesktop%5C%5Cmergeb%20HR%5C%5Capps%5C%5Cpersonnel-frontend%5C%5Csrc%5C%5Capp%5C%5Cemployees%5C%5Cnew%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chamza%5C%5CDesktop%5C%5Cmergeb%20HR%5C%5Capps%5C%5Cpersonnel-frontend%5C%5Csrc%5C%5Capp%5C%5Cemployees%5C%5Cnew%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/employees/new/page.tsx */ \"(app-pages-browser)/./src/app/employees/new/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2hhbXphJTVDJTVDRGVza3RvcCU1QyU1Q21lcmdlYiUyMEhSJTVDJTVDYXBwcyU1QyU1Q3BlcnNvbm5lbC1mcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2VtcGxveWVlcyU1QyU1Q25ldyU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsMExBQXVJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxoYW16YVxcXFxEZXNrdG9wXFxcXG1lcmdlYiBIUlxcXFxhcHBzXFxcXHBlcnNvbm5lbC1mcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGVtcGxveWVlc1xcXFxuZXdcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chamza%5C%5CDesktop%5C%5Cmergeb%20HR%5C%5Capps%5C%5Cpersonnel-frontend%5C%5Csrc%5C%5Capp%5C%5Cemployees%5C%5Cnew%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/next/dist/client/app-dir/link.js":
/*!***********************************************************!*\
  !*** ../../node_modules/next/dist/client/app-dir/link.js ***!
  \***********************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    /**\n * A React component that extends the HTML `<a>` element to provide\n * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation. This is the primary way to navigate between routes in Next.js.\n *\n * @remarks\n * - Prefetching is only enabled in production.\n *\n * @see https://nextjs.org/docs/app/api-reference/components/link\n */ default: function() {\n        return LinkComponent;\n    },\n    useLinkStatus: function() {\n        return useLinkStatus;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\"));\nconst _formaturl = __webpack_require__(/*! ../../shared/lib/router/utils/format-url */ \"(app-pages-browser)/../../node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _usemergedref = __webpack_require__(/*! ../use-merged-ref */ \"(app-pages-browser)/../../node_modules/next/dist/client/use-merged-ref.js\");\nconst _utils = __webpack_require__(/*! ../../shared/lib/utils */ \"(app-pages-browser)/../../node_modules/next/dist/shared/lib/utils.js\");\nconst _addbasepath = __webpack_require__(/*! ../add-base-path */ \"(app-pages-browser)/../../node_modules/next/dist/client/add-base-path.js\");\nconst _warnonce = __webpack_require__(/*! ../../shared/lib/utils/warn-once */ \"(app-pages-browser)/../../node_modules/next/dist/shared/lib/utils/warn-once.js\");\nconst _links = __webpack_require__(/*! ../components/links */ \"(app-pages-browser)/../../node_modules/next/dist/client/components/links.js\");\nconst _islocalurl = __webpack_require__(/*! ../../shared/lib/router/utils/is-local-url */ \"(app-pages-browser)/../../node_modules/next/dist/shared/lib/router/utils/is-local-url.js\");\nconst _approuterinstance = __webpack_require__(/*! ../components/app-router-instance */ \"(app-pages-browser)/../../node_modules/next/dist/client/components/app-router-instance.js\");\nconst _erroronce = __webpack_require__(/*! ../../shared/lib/utils/error-once */ \"(app-pages-browser)/../../node_modules/next/dist/shared/lib/utils/error-once.js\");\nconst _segmentcache = __webpack_require__(/*! ../components/segment-cache */ \"(app-pages-browser)/../../node_modules/next/dist/client/components/segment-cache.js\");\nfunction isModifiedEvent(event) {\n    const eventTarget = event.currentTarget;\n    const target = eventTarget.getAttribute('target');\n    return target && target !== '_self' || event.metaKey || event.ctrlKey || event.shiftKey || event.altKey || // triggers resource download\n    event.nativeEvent && event.nativeEvent.which === 2;\n}\nfunction linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate) {\n    const { nodeName } = e.currentTarget;\n    // anchors inside an svg have a lowercase nodeName\n    const isAnchorNodeName = nodeName.toUpperCase() === 'A';\n    if (isAnchorNodeName && isModifiedEvent(e) || e.currentTarget.hasAttribute('download')) {\n        // ignore click for browser’s default behavior\n        return;\n    }\n    if (!(0, _islocalurl.isLocalURL)(href)) {\n        if (replace) {\n            // browser default behavior does not replace the history state\n            // so we need to do it manually\n            e.preventDefault();\n            location.replace(href);\n        }\n        // ignore click for browser’s default behavior\n        return;\n    }\n    e.preventDefault();\n    if (onNavigate) {\n        let isDefaultPrevented = false;\n        onNavigate({\n            preventDefault: ()=>{\n                isDefaultPrevented = true;\n            }\n        });\n        if (isDefaultPrevented) {\n            return;\n        }\n    }\n    _react.default.startTransition(()=>{\n        (0, _approuterinstance.dispatchNavigateAction)(as || href, replace ? 'replace' : 'push', scroll != null ? scroll : true, linkInstanceRef.current);\n    });\n}\nfunction formatStringOrUrl(urlObjOrString) {\n    if (typeof urlObjOrString === 'string') {\n        return urlObjOrString;\n    }\n    return (0, _formaturl.formatUrl)(urlObjOrString);\n}\nfunction LinkComponent(props) {\n    _s();\n    const [linkStatus, setOptimisticLinkStatus] = (0, _react.useOptimistic)(_links.IDLE_LINK_STATUS);\n    let children;\n    const linkInstanceRef = (0, _react.useRef)(null);\n    const { href: hrefProp, as: asProp, children: childrenProp, prefetch: prefetchProp = null, passHref, replace, shallow, scroll, onClick, onMouseEnter: onMouseEnterProp, onTouchStart: onTouchStartProp, legacyBehavior = false, onNavigate, ref: forwardedRef, unstable_dynamicOnHover, ...restProps } = props;\n    children = childrenProp;\n    if (legacyBehavior && (typeof children === 'string' || typeof children === 'number')) {\n        children = /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n            children: children\n        });\n    }\n    const router = _react.default.useContext(_approutercontextsharedruntime.AppRouterContext);\n    const prefetchEnabled = prefetchProp !== false;\n    const fetchStrategy = prefetchProp !== false ? getFetchStrategyFromPrefetchProp(prefetchProp) : _segmentcache.FetchStrategy.PPR;\n    if (true) {\n        function createPropError(args) {\n            return Object.defineProperty(new Error(\"Failed prop type: The prop `\" + args.key + \"` expects a \" + args.expected + \" in `<Link>`, but got `\" + args.actual + \"` instead.\" + ( true ? \"\\nOpen your browser's console to view the Component stack trace.\" : 0)), \"__NEXT_ERROR_CODE\", {\n                value: \"E319\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        // TypeScript trick for type-guarding:\n        const requiredPropsGuard = {\n            href: true\n        };\n        const requiredProps = Object.keys(requiredPropsGuard);\n        requiredProps.forEach((key)=>{\n            if (key === 'href') {\n                if (props[key] == null || typeof props[key] !== 'string' && typeof props[key] !== 'object') {\n                    throw createPropError({\n                        key,\n                        expected: '`string` or `object`',\n                        actual: props[key] === null ? 'null' : typeof props[key]\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // TypeScript trick for type-guarding:\n        const optionalPropsGuard = {\n            as: true,\n            replace: true,\n            scroll: true,\n            shallow: true,\n            passHref: true,\n            prefetch: true,\n            unstable_dynamicOnHover: true,\n            onClick: true,\n            onMouseEnter: true,\n            onTouchStart: true,\n            legacyBehavior: true,\n            onNavigate: true\n        };\n        const optionalProps = Object.keys(optionalPropsGuard);\n        optionalProps.forEach((key)=>{\n            const valType = typeof props[key];\n            if (key === 'as') {\n                if (props[key] && valType !== 'string' && valType !== 'object') {\n                    throw createPropError({\n                        key,\n                        expected: '`string` or `object`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'onClick' || key === 'onMouseEnter' || key === 'onTouchStart' || key === 'onNavigate') {\n                if (props[key] && valType !== 'function') {\n                    throw createPropError({\n                        key,\n                        expected: '`function`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'replace' || key === 'scroll' || key === 'shallow' || key === 'passHref' || key === 'legacyBehavior' || key === 'unstable_dynamicOnHover') {\n                if (props[key] != null && valType !== 'boolean') {\n                    throw createPropError({\n                        key,\n                        expected: '`boolean`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'prefetch') {\n                if (props[key] != null && valType !== 'boolean' && props[key] !== 'auto' && props[key] !== 'unstable_forceStale') {\n                    throw createPropError({\n                        key,\n                        expected: '`boolean | \"auto\" | \"unstable_forceStale\"`',\n                        actual: valType\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n    }\n    if (true) {\n        if (props.locale) {\n            (0, _warnonce.warnOnce)('The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization');\n        }\n        if (!asProp) {\n            let href;\n            if (typeof hrefProp === 'string') {\n                href = hrefProp;\n            } else if (typeof hrefProp === 'object' && typeof hrefProp.pathname === 'string') {\n                href = hrefProp.pathname;\n            }\n            if (href) {\n                const hasDynamicSegment = href.split('/').some((segment)=>segment.startsWith('[') && segment.endsWith(']'));\n                if (hasDynamicSegment) {\n                    throw Object.defineProperty(new Error(\"Dynamic href `\" + href + \"` found in <Link> while using the `/app` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href\"), \"__NEXT_ERROR_CODE\", {\n                        value: \"E267\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n            }\n        }\n    }\n    const { href, as } = _react.default.useMemo({\n        \"LinkComponent.useMemo\": ()=>{\n            const resolvedHref = formatStringOrUrl(hrefProp);\n            return {\n                href: resolvedHref,\n                as: asProp ? formatStringOrUrl(asProp) : resolvedHref\n            };\n        }\n    }[\"LinkComponent.useMemo\"], [\n        hrefProp,\n        asProp\n    ]);\n    // This will return the first child, if multiple are provided it will throw an error\n    let child;\n    if (legacyBehavior) {\n        if (true) {\n            if (onClick) {\n                console.warn('\"onClick\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link');\n            }\n            if (onMouseEnterProp) {\n                console.warn('\"onMouseEnter\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link');\n            }\n            try {\n                child = _react.default.Children.only(children);\n            } catch (err) {\n                if (!children) {\n                    throw Object.defineProperty(new Error(\"No children were passed to <Link> with `href` of `\" + hrefProp + \"` but one child is required https://nextjs.org/docs/messages/link-no-children\"), \"__NEXT_ERROR_CODE\", {\n                        value: \"E320\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                throw Object.defineProperty(new Error(\"Multiple children were passed to <Link> with `href` of `\" + hrefProp + \"` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children\" + ( true ? \" \\nOpen your browser's console to view the Component stack trace.\" : 0)), \"__NEXT_ERROR_CODE\", {\n                    value: \"E266\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        } else {}\n    } else {\n        if (true) {\n            if ((children == null ? void 0 : children.type) === 'a') {\n                throw Object.defineProperty(new Error('Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E209\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        }\n    }\n    const childRef = legacyBehavior ? child && typeof child === 'object' && child.ref : forwardedRef;\n    // Use a callback ref to attach an IntersectionObserver to the anchor tag on\n    // mount. In the future we will also use this to keep track of all the\n    // currently mounted <Link> instances, e.g. so we can re-prefetch them after\n    // a revalidation or refresh.\n    const observeLinkVisibilityOnMount = _react.default.useCallback({\n        \"LinkComponent.useCallback[observeLinkVisibilityOnMount]\": (element)=>{\n            if (router !== null) {\n                linkInstanceRef.current = (0, _links.mountLinkInstance)(element, href, router, fetchStrategy, prefetchEnabled, setOptimisticLinkStatus);\n            }\n            return ({\n                \"LinkComponent.useCallback[observeLinkVisibilityOnMount]\": ()=>{\n                    if (linkInstanceRef.current) {\n                        (0, _links.unmountLinkForCurrentNavigation)(linkInstanceRef.current);\n                        linkInstanceRef.current = null;\n                    }\n                    (0, _links.unmountPrefetchableInstance)(element);\n                }\n            })[\"LinkComponent.useCallback[observeLinkVisibilityOnMount]\"];\n        }\n    }[\"LinkComponent.useCallback[observeLinkVisibilityOnMount]\"], [\n        prefetchEnabled,\n        href,\n        router,\n        fetchStrategy,\n        setOptimisticLinkStatus\n    ]);\n    const mergedRef = (0, _usemergedref.useMergedRef)(observeLinkVisibilityOnMount, childRef);\n    const childProps = {\n        ref: mergedRef,\n        onClick (e) {\n            if (true) {\n                if (!e) {\n                    throw Object.defineProperty(new Error('Component rendered inside next/link has to pass click event to \"onClick\" prop.'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E312\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n            }\n            if (!legacyBehavior && typeof onClick === 'function') {\n                onClick(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onClick === 'function') {\n                child.props.onClick(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (e.defaultPrevented) {\n                return;\n            }\n            linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate);\n        },\n        onMouseEnter (e) {\n            if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n                onMouseEnterProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onMouseEnter === 'function') {\n                child.props.onMouseEnter(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled || \"development\" === 'development') {\n                return;\n            }\n            const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true;\n            (0, _links.onNavigationIntent)(e.currentTarget, upgradeToDynamicPrefetch);\n        },\n        onTouchStart:  false ? 0 : function onTouchStart(e) {\n            if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n                onTouchStartProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onTouchStart === 'function') {\n                child.props.onTouchStart(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled) {\n                return;\n            }\n            const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true;\n            (0, _links.onNavigationIntent)(e.currentTarget, upgradeToDynamicPrefetch);\n        }\n    };\n    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n    // defined, we specify the current 'href', so that repetition is not needed by the user.\n    // If the url is absolute, we can bypass the logic to prepend the basePath.\n    if ((0, _utils.isAbsoluteUrl)(as)) {\n        childProps.href = as;\n    } else if (!legacyBehavior || passHref || child.type === 'a' && !('href' in child.props)) {\n        childProps.href = (0, _addbasepath.addBasePath)(as);\n    }\n    let link;\n    if (legacyBehavior) {\n        if (true) {\n            (0, _erroronce.errorOnce)('`legacyBehavior` is deprecated and will be removed in a future ' + 'release. A codemod is available to upgrade your components:\\n\\n' + 'npx @next/codemod@latest new-link .\\n\\n' + 'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components');\n        }\n        link = /*#__PURE__*/ _react.default.cloneElement(child, childProps);\n    } else {\n        link = /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n            ...restProps,\n            ...childProps,\n            children: children\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(LinkStatusContext.Provider, {\n        value: linkStatus,\n        children: link\n    });\n}\n_s(LinkComponent, \"MNV6IdWv8Lu3MKc7Fm4v59uGRY0=\");\n_c = LinkComponent;\nconst LinkStatusContext = /*#__PURE__*/ (0, _react.createContext)(_links.IDLE_LINK_STATUS);\nconst useLinkStatus = ()=>{\n    return (0, _react.useContext)(LinkStatusContext);\n};\nfunction getFetchStrategyFromPrefetchProp(prefetchProp) {\n    if (false) {} else {\n        return prefetchProp === null || prefetchProp === 'auto' ? _segmentcache.FetchStrategy.PPR : // (although invalid values should've been filtered out by prop validation in dev)\n        _segmentcache.FetchStrategy.Full;\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=link.js.map\nvar _c;\n$RefreshReg$(_c, \"LinkComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/client/app-dir/link.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/next/dist/client/use-merged-ref.js":
/*!*************************************************************!*\
  !*** ../../node_modules/next/dist/client/use-merged-ref.js ***!
  \*************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useMergedRef\", ({\n    enumerable: true,\n    get: function() {\n        return useMergedRef;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\nfunction useMergedRef(refA, refB) {\n    const cleanupA = (0, _react.useRef)(null);\n    const cleanupB = (0, _react.useRef)(null);\n    // NOTE: In theory, we could skip the wrapping if only one of the refs is non-null.\n    // (this happens often if the user doesn't pass a ref to Link/Form/Image)\n    // But this can cause us to leak a cleanup-ref into user code (e.g. via `<Link legacyBehavior>`),\n    // and the user might pass that ref into ref-merging library that doesn't support cleanup refs\n    // (because it hasn't been updated for React 19)\n    // which can then cause things to blow up, because a cleanup-returning ref gets called with `null`.\n    // So in practice, it's safer to be defensive and always wrap the ref, even on React 19.\n    return (0, _react.useCallback)((current)=>{\n        if (current === null) {\n            const cleanupFnA = cleanupA.current;\n            if (cleanupFnA) {\n                cleanupA.current = null;\n                cleanupFnA();\n            }\n            const cleanupFnB = cleanupB.current;\n            if (cleanupFnB) {\n                cleanupB.current = null;\n                cleanupFnB();\n            }\n        } else {\n            if (refA) {\n                cleanupA.current = applyRef(refA, current);\n            }\n            if (refB) {\n                cleanupB.current = applyRef(refB, current);\n            }\n        }\n    }, [\n        refA,\n        refB\n    ]);\n}\nfunction applyRef(refA, current) {\n    if (typeof refA === 'function') {\n        const cleanup = refA(current);\n        if (typeof cleanup === 'function') {\n            return cleanup;\n        } else {\n            return ()=>refA(null);\n        }\n    } else {\n        refA.current = current;\n        return ()=>{\n            refA.current = null;\n        };\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-merged-ref.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/client/use-merged-ref.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!********************************************************************************************!*\
  !*** ../../node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(type, key, props, owner, debugStack, debugTask) {\n      var refProp = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== refProp ? refProp : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        maybeKey,\n        getOwner(),\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (type, config, maybeKey, isStaticChildren) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!**********************************************************************!*\
  !*** ../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \**********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NvbXBpbGVkL3JlYWN0L2pzeC1kZXYtcnVudGltZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixJQUFJLEtBQXFDLEVBQUUsRUFFMUMsQ0FBQztBQUNGLEVBQUUsa01BQXNFO0FBQ3hFIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGhhbXphXFxEZXNrdG9wXFxtZXJnZWIgSFJcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcY29tcGlsZWRcXHJlYWN0XFxqc3gtZGV2LXJ1bnRpbWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5wcm9kdWN0aW9uLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/next/dist/shared/lib/router/utils/format-url.js":
/*!**************************************************************************!*\
  !*** ../../node_modules/next/dist/shared/lib/router/utils/format-url.js ***!
  \**************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("// Format function modified from nodejs\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    formatUrl: function() {\n        return formatUrl;\n    },\n    formatWithValidation: function() {\n        return formatWithValidation;\n    },\n    urlObjectKeys: function() {\n        return urlObjectKeys;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _querystring = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ./querystring */ \"(app-pages-browser)/../../node_modules/next/dist/shared/lib/router/utils/querystring.js\"));\nconst slashedProtocols = /https?|ftp|gopher|file/;\nfunction formatUrl(urlObj) {\n    let { auth, hostname } = urlObj;\n    let protocol = urlObj.protocol || '';\n    let pathname = urlObj.pathname || '';\n    let hash = urlObj.hash || '';\n    let query = urlObj.query || '';\n    let host = false;\n    auth = auth ? encodeURIComponent(auth).replace(/%3A/i, ':') + '@' : '';\n    if (urlObj.host) {\n        host = auth + urlObj.host;\n    } else if (hostname) {\n        host = auth + (~hostname.indexOf(':') ? \"[\" + hostname + \"]\" : hostname);\n        if (urlObj.port) {\n            host += ':' + urlObj.port;\n        }\n    }\n    if (query && typeof query === 'object') {\n        query = String(_querystring.urlQueryToSearchParams(query));\n    }\n    let search = urlObj.search || query && \"?\" + query || '';\n    if (protocol && !protocol.endsWith(':')) protocol += ':';\n    if (urlObj.slashes || (!protocol || slashedProtocols.test(protocol)) && host !== false) {\n        host = '//' + (host || '');\n        if (pathname && pathname[0] !== '/') pathname = '/' + pathname;\n    } else if (!host) {\n        host = '';\n    }\n    if (hash && hash[0] !== '#') hash = '#' + hash;\n    if (search && search[0] !== '?') search = '?' + search;\n    pathname = pathname.replace(/[?#]/g, encodeURIComponent);\n    search = search.replace('#', '%23');\n    return \"\" + protocol + host + pathname + search + hash;\n}\nconst urlObjectKeys = [\n    'auth',\n    'hash',\n    'host',\n    'hostname',\n    'href',\n    'path',\n    'pathname',\n    'port',\n    'protocol',\n    'query',\n    'search',\n    'slashes'\n];\nfunction formatWithValidation(url) {\n    if (true) {\n        if (url !== null && typeof url === 'object') {\n            Object.keys(url).forEach((key)=>{\n                if (!urlObjectKeys.includes(key)) {\n                    console.warn(\"Unknown key passed via urlObject into url.format: \" + key);\n                }\n            });\n        }\n    }\n    return formatUrl(url);\n} //# sourceMappingURL=format-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/shared/lib/router/utils/format-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/next/dist/shared/lib/router/utils/is-local-url.js":
/*!****************************************************************************!*\
  !*** ../../node_modules/next/dist/shared/lib/router/utils/is-local-url.js ***!
  \****************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"isLocalURL\", ({\n    enumerable: true,\n    get: function() {\n        return isLocalURL;\n    }\n}));\nconst _utils = __webpack_require__(/*! ../../utils */ \"(app-pages-browser)/../../node_modules/next/dist/shared/lib/utils.js\");\nconst _hasbasepath = __webpack_require__(/*! ../../../../client/has-base-path */ \"(app-pages-browser)/../../node_modules/next/dist/client/has-base-path.js\");\nfunction isLocalURL(url) {\n    // prevent a hydration mismatch on href for url with anchor refs\n    if (!(0, _utils.isAbsoluteUrl)(url)) return true;\n    try {\n        // absolute urls can be local if they are on the same origin\n        const locationOrigin = (0, _utils.getLocationOrigin)();\n        const resolved = new URL(url, locationOrigin);\n        return resolved.origin === locationOrigin && (0, _hasbasepath.hasBasePath)(resolved.pathname);\n    } catch (_) {\n        return false;\n    }\n} //# sourceMappingURL=is-local-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvcm91dGVyL3V0aWxzL2lzLWxvY2FsLXVybC5qcyIsIm1hcHBpbmdzIjoiOzs7OzhDQU1nQkE7OztlQUFBQTs7O21DQU5pQzt5Q0FDckI7QUFLckIsU0FBU0EsV0FBV0MsR0FBVztJQUNwQyxnRUFBZ0U7SUFDaEUsSUFBSSxDQUFDQyxDQUFBQSxHQUFBQSxPQUFBQSxhQUFBQSxFQUFjRCxNQUFNLE9BQU87SUFDaEMsSUFBSTtRQUNGLDREQUE0RDtRQUM1RCxNQUFNRSxpQkFBaUJDLENBQUFBLEdBQUFBLE9BQUFBLGlCQUFBQTtRQUN2QixNQUFNQyxXQUFXLElBQUlDLElBQUlMLEtBQUtFO1FBQzlCLE9BQU9FLFNBQVNFLE1BQU0sS0FBS0osa0JBQWtCSyxDQUFBQSxHQUFBQSxhQUFBQSxXQUFBQSxFQUFZSCxTQUFTSSxRQUFRO0lBQzVFLEVBQUUsT0FBT0MsR0FBRztRQUNWLE9BQU87SUFDVDtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNyY1xcc2hhcmVkXFxsaWJcXHJvdXRlclxcdXRpbHNcXGlzLWxvY2FsLXVybC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpc0Fic29sdXRlVXJsLCBnZXRMb2NhdGlvbk9yaWdpbiB9IGZyb20gJy4uLy4uL3V0aWxzJ1xuaW1wb3J0IHsgaGFzQmFzZVBhdGggfSBmcm9tICcuLi8uLi8uLi8uLi9jbGllbnQvaGFzLWJhc2UtcGF0aCdcblxuLyoqXG4gKiBEZXRlY3RzIHdoZXRoZXIgYSBnaXZlbiB1cmwgaXMgcm91dGFibGUgYnkgdGhlIE5leHQuanMgcm91dGVyIChicm93c2VyIG9ubHkpLlxuICovXG5leHBvcnQgZnVuY3Rpb24gaXNMb2NhbFVSTCh1cmw6IHN0cmluZyk6IGJvb2xlYW4ge1xuICAvLyBwcmV2ZW50IGEgaHlkcmF0aW9uIG1pc21hdGNoIG9uIGhyZWYgZm9yIHVybCB3aXRoIGFuY2hvciByZWZzXG4gIGlmICghaXNBYnNvbHV0ZVVybCh1cmwpKSByZXR1cm4gdHJ1ZVxuICB0cnkge1xuICAgIC8vIGFic29sdXRlIHVybHMgY2FuIGJlIGxvY2FsIGlmIHRoZXkgYXJlIG9uIHRoZSBzYW1lIG9yaWdpblxuICAgIGNvbnN0IGxvY2F0aW9uT3JpZ2luID0gZ2V0TG9jYXRpb25PcmlnaW4oKVxuICAgIGNvbnN0IHJlc29sdmVkID0gbmV3IFVSTCh1cmwsIGxvY2F0aW9uT3JpZ2luKVxuICAgIHJldHVybiByZXNvbHZlZC5vcmlnaW4gPT09IGxvY2F0aW9uT3JpZ2luICYmIGhhc0Jhc2VQYXRoKHJlc29sdmVkLnBhdGhuYW1lKVxuICB9IGNhdGNoIChfKSB7XG4gICAgcmV0dXJuIGZhbHNlXG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJpc0xvY2FsVVJMIiwidXJsIiwiaXNBYnNvbHV0ZVVybCIsImxvY2F0aW9uT3JpZ2luIiwiZ2V0TG9jYXRpb25PcmlnaW4iLCJyZXNvbHZlZCIsIlVSTCIsIm9yaWdpbiIsImhhc0Jhc2VQYXRoIiwicGF0aG5hbWUiLCJfIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/shared/lib/router/utils/is-local-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/next/dist/shared/lib/router/utils/querystring.js":
/*!***************************************************************************!*\
  !*** ../../node_modules/next/dist/shared/lib/router/utils/querystring.js ***!
  \***************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    assign: function() {\n        return assign;\n    },\n    searchParamsToUrlQuery: function() {\n        return searchParamsToUrlQuery;\n    },\n    urlQueryToSearchParams: function() {\n        return urlQueryToSearchParams;\n    }\n});\nfunction searchParamsToUrlQuery(searchParams) {\n    const query = {};\n    for (const [key, value] of searchParams.entries()){\n        const existing = query[key];\n        if (typeof existing === 'undefined') {\n            query[key] = value;\n        } else if (Array.isArray(existing)) {\n            existing.push(value);\n        } else {\n            query[key] = [\n                existing,\n                value\n            ];\n        }\n    }\n    return query;\n}\nfunction stringifyUrlQueryParam(param) {\n    if (typeof param === 'string') {\n        return param;\n    }\n    if (typeof param === 'number' && !isNaN(param) || typeof param === 'boolean') {\n        return String(param);\n    } else {\n        return '';\n    }\n}\nfunction urlQueryToSearchParams(query) {\n    const searchParams = new URLSearchParams();\n    for (const [key, value] of Object.entries(query)){\n        if (Array.isArray(value)) {\n            for (const item of value){\n                searchParams.append(key, stringifyUrlQueryParam(item));\n            }\n        } else {\n            searchParams.set(key, stringifyUrlQueryParam(value));\n        }\n    }\n    return searchParams;\n}\nfunction assign(target) {\n    for(var _len = arguments.length, searchParamsList = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        searchParamsList[_key - 1] = arguments[_key];\n    }\n    for (const searchParams of searchParamsList){\n        for (const key of searchParams.keys()){\n            target.delete(key);\n        }\n        for (const [key, value] of searchParams.entries()){\n            target.append(key, value);\n        }\n    }\n    return target;\n} //# sourceMappingURL=querystring.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/shared/lib/router/utils/querystring.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/next/dist/shared/lib/utils.js":
/*!********************************************************!*\
  !*** ../../node_modules/next/dist/shared/lib/utils.js ***!
  \********************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DecodeError: function() {\n        return DecodeError;\n    },\n    MiddlewareNotFoundError: function() {\n        return MiddlewareNotFoundError;\n    },\n    MissingStaticPage: function() {\n        return MissingStaticPage;\n    },\n    NormalizeError: function() {\n        return NormalizeError;\n    },\n    PageNotFoundError: function() {\n        return PageNotFoundError;\n    },\n    SP: function() {\n        return SP;\n    },\n    ST: function() {\n        return ST;\n    },\n    WEB_VITALS: function() {\n        return WEB_VITALS;\n    },\n    execOnce: function() {\n        return execOnce;\n    },\n    getDisplayName: function() {\n        return getDisplayName;\n    },\n    getLocationOrigin: function() {\n        return getLocationOrigin;\n    },\n    getURL: function() {\n        return getURL;\n    },\n    isAbsoluteUrl: function() {\n        return isAbsoluteUrl;\n    },\n    isResSent: function() {\n        return isResSent;\n    },\n    loadGetInitialProps: function() {\n        return loadGetInitialProps;\n    },\n    normalizeRepeatedSlashes: function() {\n        return normalizeRepeatedSlashes;\n    },\n    stringifyError: function() {\n        return stringifyError;\n    }\n});\nconst WEB_VITALS = [\n    'CLS',\n    'FCP',\n    'FID',\n    'INP',\n    'LCP',\n    'TTFB'\n];\nfunction execOnce(fn) {\n    let used = false;\n    let result;\n    return function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        if (!used) {\n            used = true;\n            result = fn(...args);\n        }\n        return result;\n    };\n}\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/;\nconst isAbsoluteUrl = (url)=>ABSOLUTE_URL_REGEX.test(url);\nfunction getLocationOrigin() {\n    const { protocol, hostname, port } = window.location;\n    return protocol + \"//\" + hostname + (port ? ':' + port : '');\n}\nfunction getURL() {\n    const { href } = window.location;\n    const origin = getLocationOrigin();\n    return href.substring(origin.length);\n}\nfunction getDisplayName(Component) {\n    return typeof Component === 'string' ? Component : Component.displayName || Component.name || 'Unknown';\n}\nfunction isResSent(res) {\n    return res.finished || res.headersSent;\n}\nfunction normalizeRepeatedSlashes(url) {\n    const urlParts = url.split('?');\n    const urlNoQuery = urlParts[0];\n    return urlNoQuery // first we replace any non-encoded backslashes with forward\n    // then normalize repeated forward slashes\n    .replace(/\\\\/g, '/').replace(/\\/\\/+/g, '/') + (urlParts[1] ? \"?\" + urlParts.slice(1).join('?') : '');\n}\nasync function loadGetInitialProps(App, ctx) {\n    if (true) {\n        var _App_prototype;\n        if ((_App_prototype = App.prototype) == null ? void 0 : _App_prototype.getInitialProps) {\n            const message = '\"' + getDisplayName(App) + '.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.';\n            throw Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n                value: \"E394\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n    }\n    // when called from _app `ctx` is nested in `ctx`\n    const res = ctx.res || ctx.ctx && ctx.ctx.res;\n    if (!App.getInitialProps) {\n        if (ctx.ctx && ctx.Component) {\n            // @ts-ignore pageProps default\n            return {\n                pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx)\n            };\n        }\n        return {};\n    }\n    const props = await App.getInitialProps(ctx);\n    if (res && isResSent(res)) {\n        return props;\n    }\n    if (!props) {\n        const message = '\"' + getDisplayName(App) + '.getInitialProps()\" should resolve to an object. But found \"' + props + '\" instead.';\n        throw Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n            value: \"E394\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    if (true) {\n        if (Object.keys(props).length === 0 && !ctx.ctx) {\n            console.warn(\"\" + getDisplayName(App) + \" returned an empty object from `getInitialProps`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps\");\n        }\n    }\n    return props;\n}\nconst SP = typeof performance !== 'undefined';\nconst ST = SP && [\n    'mark',\n    'measure',\n    'getEntriesByName'\n].every((method)=>typeof performance[method] === 'function');\nclass DecodeError extends Error {\n}\nclass NormalizeError extends Error {\n}\nclass PageNotFoundError extends Error {\n    constructor(page){\n        super();\n        this.code = 'ENOENT';\n        this.name = 'PageNotFoundError';\n        this.message = \"Cannot find module for page: \" + page;\n    }\n}\nclass MissingStaticPage extends Error {\n    constructor(page, message){\n        super();\n        this.message = \"Failed to load static file for page: \" + page + \" \" + message;\n    }\n}\nclass MiddlewareNotFoundError extends Error {\n    constructor(){\n        super();\n        this.code = 'ENOENT';\n        this.message = \"Cannot find the middleware module\";\n    }\n}\nfunction stringifyError(error) {\n    return JSON.stringify({\n        message: error.message,\n        stack: error.stack\n    });\n} //# sourceMappingURL=utils.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/shared/lib/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/next/dist/shared/lib/utils/error-once.js":
/*!*******************************************************************!*\
  !*** ../../node_modules/next/dist/shared/lib/utils/error-once.js ***!
  \*******************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"errorOnce\", ({\n    enumerable: true,\n    get: function() {\n        return errorOnce;\n    }\n}));\nlet errorOnce = (_)=>{};\nif (true) {\n    const errors = new Set();\n    errorOnce = (msg)=>{\n        if (!errors.has(msg)) {\n            console.error(msg);\n        }\n        errors.add(msg);\n    };\n} //# sourceMappingURL=error-once.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvdXRpbHMvZXJyb3Itb25jZS5qcyIsIm1hcHBpbmdzIjoiOzs7OzZDQVdTQTs7O2VBQUFBOzs7QUFYVCxJQUFJQSxZQUFZLENBQUNDLEtBQWU7QUFDaEMsSUFBSUMsSUFBb0IsRUFBbUI7SUFDekMsTUFBTUcsU0FBUyxJQUFJQztJQUNuQk4sWUFBWSxDQUFDTztRQUNYLElBQUksQ0FBQ0YsT0FBT0csR0FBRyxDQUFDRCxNQUFNO1lBQ3BCRSxRQUFRQyxLQUFLLENBQUNIO1FBQ2hCO1FBQ0FGLE9BQU9NLEdBQUcsQ0FBQ0o7SUFDYjtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGhhbXphXFxzcmNcXHNoYXJlZFxcbGliXFx1dGlsc1xcZXJyb3Itb25jZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJsZXQgZXJyb3JPbmNlID0gKF86IHN0cmluZykgPT4ge31cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIGNvbnN0IGVycm9ycyA9IG5ldyBTZXQ8c3RyaW5nPigpXG4gIGVycm9yT25jZSA9IChtc2c6IHN0cmluZykgPT4ge1xuICAgIGlmICghZXJyb3JzLmhhcyhtc2cpKSB7XG4gICAgICBjb25zb2xlLmVycm9yKG1zZylcbiAgICB9XG4gICAgZXJyb3JzLmFkZChtc2cpXG4gIH1cbn1cblxuZXhwb3J0IHsgZXJyb3JPbmNlIH1cbiJdLCJuYW1lcyI6WyJlcnJvck9uY2UiLCJfIiwicHJvY2VzcyIsImVudiIsIk5PREVfRU5WIiwiZXJyb3JzIiwiU2V0IiwibXNnIiwiaGFzIiwiY29uc29sZSIsImVycm9yIiwiYWRkIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/shared/lib/utils/error-once.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/employees/new/page.tsx":
/*!****************************************!*\
  !*** ./src/app/employees/new/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NewEmployeePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/../../node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction NewEmployeePage() {\n    var _getSelectedRank, _getSelectedRank1, _getSelectedRank2, _getSelectedRank3, _getSelectedRank4;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        rankKey: '',\n        militaryNumber: '',\n        nationalId: '',\n        unitId: '',\n        // Personal Information\n        birthDate: '',\n        birthPlace: '',\n        motherName: '',\n        maritalStatus: '',\n        // Work Information\n        appointmentDate: '',\n        lastPromotionDate: '',\n        // Bank Information\n        bankName: '',\n        bankBranch: '',\n        bankAccount: '',\n        // Leave Information\n        leaveBalance: '',\n        leaveType: 'سنوية',\n        // Employee Status\n        employeeStatus: 'مستمر',\n        // Education Information\n        qualification: '',\n        qualificationDate: '',\n        // Photo\n        photo: null\n    });\n    const [ranks, setRanks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [units, setUnits] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NewEmployeePage.useEffect\": ()=>{\n            const token = localStorage.getItem('token');\n            if (!token) {\n                router.push('/login');\n                return;\n            }\n            fetchData(token);\n        }\n    }[\"NewEmployeePage.useEffect\"], [\n        router\n    ]);\n    const fetchData = async (token)=>{\n        try {\n            // Fetch ranks\n            const ranksResponse = await fetch('http://127.0.0.1:4001/ranks', {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (ranksResponse.ok) {\n                const ranksData = await ranksResponse.json();\n                setRanks(ranksData);\n            }\n            // Fetch units\n            const unitsResponse = await fetch('http://127.0.0.1:4001/units', {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (unitsResponse.ok) {\n                const unitsData = await ranksResponse.json();\n                // Mock units data since API returns array of strings\n                setUnits([\n                    {\n                        id: 1,\n                        name: 'الخمس'\n                    },\n                    {\n                        id: 2,\n                        name: 'سوق الخميس'\n                    },\n                    {\n                        id: 3,\n                        name: 'كعام'\n                    },\n                    {\n                        id: 4,\n                        name: 'الدوريات'\n                    },\n                    {\n                        id: 5,\n                        name: 'غنيمة'\n                    }\n                ]);\n            }\n        } catch (err) {\n            setError('خطأ في تحميل البيانات');\n        }\n    };\n    const getSelectedRank = ()=>{\n        return ranks.find((r)=>r.key === formData.rankKey);\n    };\n    const getMilitaryNumberPlaceholder = ()=>{\n        const rank = getSelectedRank();\n        if (!rank) return 'اختر الرتبة أولاً';\n        switch(rank.category){\n            case 'officer':\n                return 'بلا';\n            case 'nco':\n                return 'أدخل الرقم العسكري';\n            case 'employee':\n                return 'موظف';\n            default:\n                return '';\n        }\n    };\n    const validateMilitaryNumber = ()=>{\n        const rank = getSelectedRank();\n        if (!rank) return 'يجب اختيار الرتبة أولاً';\n        switch(rank.category){\n            case 'officer':\n                if (formData.militaryNumber !== 'بلا') {\n                    return 'بالنسبة للضباط يجب أن تكون قيمة الرقم العسكري \"بلا\"';\n                }\n                break;\n            case 'nco':\n                if (!formData.militaryNumber || formData.militaryNumber.trim() === '') {\n                    return 'الرقم العسكري إجباري لضباط الصف';\n                }\n                if (formData.militaryNumber === 'بلا' || formData.militaryNumber === 'موظف') {\n                    return 'أدخل رقماً عسكرياً صحيحاً لضباط الصف';\n                }\n                break;\n            case 'employee':\n                if (formData.militaryNumber !== 'موظف') {\n                    return 'بالنسبة للموظفين يجب أن تكون قيمة الرقم العسكري \"موظف\"';\n                }\n                break;\n        }\n        return '';\n    };\n    const handleRankChange = (rankKey)=>{\n        const rank = ranks.find((r)=>r.key === rankKey);\n        let militaryNumber = '';\n        if (rank) {\n            switch(rank.category){\n                case 'officer':\n                    militaryNumber = 'بلا';\n                    break;\n                case 'employee':\n                    militaryNumber = 'موظف';\n                    break;\n                case 'nco':\n                    militaryNumber = '';\n                    break;\n            }\n        }\n        setFormData((prev)=>({\n                ...prev,\n                rankKey,\n                militaryNumber\n            }));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        setError('');\n        setSuccess('');\n        // Validate national ID\n        if (formData.nationalId.length !== 12) {\n            setError('الرقم الوطني يجب أن يكون 12 رقماً');\n            setLoading(false);\n            return;\n        }\n        // Validate bank account if provided\n        if (formData.bankAccount && formData.bankAccount.length !== 15) {\n            setError('رقم الحساب المصرفي يجب أن يكون 15 رقماً');\n            setLoading(false);\n            return;\n        }\n        // Validate military number\n        const militaryNumberError = validateMilitaryNumber();\n        if (militaryNumberError) {\n            setError(militaryNumberError);\n            setLoading(false);\n            return;\n        }\n        try {\n            const token = localStorage.getItem('token');\n            const response = await fetch('http://127.0.0.1:4001/employees', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    name: formData.name,\n                    rankKey: formData.rankKey,\n                    militaryNumber: formData.militaryNumber,\n                    nationalId: formData.nationalId,\n                    unitId: formData.unitId ? parseInt(formData.unitId) : undefined,\n                    birthDate: formData.birthDate || null,\n                    birthPlace: formData.birthPlace || null,\n                    motherName: formData.motherName || null,\n                    maritalStatus: formData.maritalStatus || null,\n                    appointmentDate: formData.appointmentDate || null,\n                    lastPromotionDate: formData.lastPromotionDate || null,\n                    bankName: formData.bankName || null,\n                    bankBranch: formData.bankBranch || null,\n                    bankAccount: formData.bankAccount || null,\n                    leaveBalance: formData.leaveBalance ? parseInt(formData.leaveBalance) : 0,\n                    leaveType: formData.leaveType || null,\n                    employeeStatus: formData.employeeStatus || null,\n                    qualification: formData.qualification || null,\n                    qualificationDate: formData.qualificationDate || null\n                })\n            });\n            if (response.ok) {\n                setSuccess('تم إضافة الموظف بنجاح');\n                setTimeout(()=>{\n                    router.push('/employees');\n                }, 2000);\n            } else {\n                const errorData = await response.json();\n                setError(errorData.error || 'خطأ في إضافة الموظف');\n            }\n        } catch (err) {\n            setError('خطأ في الاتصال بالخادم');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleLogout = ()=>{\n        localStorage.removeItem('token');\n        router.push('/login');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-semibold text-gray-900\",\n                                        children: \"إضافة موظف جديد\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/employees\",\n                                        className: \"text-blue-600 hover:text-blue-800 text-sm\",\n                                        children: \"← العودة لقائمة الموظفين\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleLogout,\n                                className: \"bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors text-sm\",\n                                children: \"تسجيل الخروج\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                lineNumber: 253,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1 order-first lg:order-last\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-6 sticky top-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"حالة الموظف\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            'مستمر',\n                                            'اجازة',\n                                            'موقوف',\n                                            'غائب عن العمل'\n                                        ].map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"radio\",\n                                                        name: \"employeeStatus\",\n                                                        value: status,\n                                                        checked: formData.employeeStatus === status,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    employeeStatus: e.target.value\n                                                                })),\n                                                        className: \"ml-2 text-blue-600 focus:ring-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-700\",\n                                                        children: status\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, status, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6 pt-6 border-t\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-md font-semibold text-gray-900 mb-3\",\n                                                children: \"الصورة الشخصية\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"file\",\n                                                        accept: \"image/*\",\n                                                        title: \"اختر الصورة الشخصية\",\n                                                        \"aria-label\": \"اختر الصورة الشخصية\",\n                                                        onChange: (e)=>{\n                                                            var _e_target_files;\n                                                            const file = ((_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0]) || null;\n                                                            setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    photo: file\n                                                                }));\n                                                        },\n                                                        className: \"w-full text-sm text-gray-500 file:ml-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    formData.photo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-green-600\",\n                                                        children: [\n                                                            \"تم اختيار: \",\n                                                            formData.photo.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-6\",\n                                        children: \"إضافة موظف جديد\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSubmit,\n                                        className: \"space-y-6\",\n                                        children: [\n                                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg\",\n                                                children: error\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 15\n                                            }, this),\n                                            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg\",\n                                                children: success\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"name\",\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"الاسم الكامل *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        id: \"name\",\n                                                        value: formData.name,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    name: e.target.value\n                                                                })),\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"rankKey\",\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"الرتبة *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        id: \"rankKey\",\n                                                        value: formData.rankKey,\n                                                        onChange: (e)=>handleRankChange(e.target.value),\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                        required: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"اختر الرتبة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                lineNumber: 364,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"optgroup\", {\n                                                                label: \"الضباط\",\n                                                                children: ranks.filter((r)=>r.category === 'officer').map((rank)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: rank.key,\n                                                                        children: rank.label\n                                                                    }, rank.key, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 367,\n                                                                        columnNumber: 21\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                lineNumber: 365,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"optgroup\", {\n                                                                label: \"ضباط الصف\",\n                                                                children: ranks.filter((r)=>r.category === 'nco').map((rank)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: rank.key,\n                                                                        children: rank.label\n                                                                    }, rank.key, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 372,\n                                                                        columnNumber: 21\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                lineNumber: 370,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"optgroup\", {\n                                                                label: \"الموظفون\",\n                                                                children: ranks.filter((r)=>r.category === 'employee').map((rank)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: rank.key,\n                                                                        children: rank.label\n                                                                    }, rank.key, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 377,\n                                                                        columnNumber: 21\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"militaryNumber\",\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"الرقم العسكري *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        id: \"militaryNumber\",\n                                                        value: formData.militaryNumber,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    militaryNumber: e.target.value\n                                                                })),\n                                                        placeholder: getMilitaryNumberPlaceholder(),\n                                                        title: \"الرقم العسكري\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                        required: true,\n                                                        readOnly: ((_getSelectedRank = getSelectedRank()) === null || _getSelectedRank === void 0 ? void 0 : _getSelectedRank.category) === 'officer' || ((_getSelectedRank1 = getSelectedRank()) === null || _getSelectedRank1 === void 0 ? void 0 : _getSelectedRank1.category) === 'employee'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    getSelectedRank() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-gray-500\",\n                                                        children: [\n                                                            ((_getSelectedRank2 = getSelectedRank()) === null || _getSelectedRank2 === void 0 ? void 0 : _getSelectedRank2.category) === 'officer' && 'للضباط: القيمة \"بلا\" تلقائياً',\n                                                            ((_getSelectedRank3 = getSelectedRank()) === null || _getSelectedRank3 === void 0 ? void 0 : _getSelectedRank3.category) === 'nco' && 'لضباط الصف: أدخل الرقم العسكري',\n                                                            ((_getSelectedRank4 = getSelectedRank()) === null || _getSelectedRank4 === void 0 ? void 0 : _getSelectedRank4.category) === 'employee' && 'للموظفين: القيمة \"موظف\" تلقائياً'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"nationalId\",\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"الرقم الوطني * (12 رقم)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        id: \"nationalId\",\n                                                        value: formData.nationalId,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    nationalId: e.target.value.replace(/\\D/g, '').slice(0, 12)\n                                                                })),\n                                                        placeholder: \"123456789012\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono\",\n                                                        required: true,\n                                                        maxLength: 12\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-gray-500\",\n                                                        children: [\n                                                            formData.nationalId.length,\n                                                            \"/12 رقم\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"unitId\",\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"الوحدة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        id: \"unitId\",\n                                                        value: formData.unitId,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    unitId: e.target.value\n                                                                })),\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"اختر الوحدة (اختياري)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                lineNumber: 436,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            units.map((unit)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: unit.id,\n                                                                    children: unit.name\n                                                                }, unit.id, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 438,\n                                                                    columnNumber: 19\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-6 mt-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                        children: \"البيانات الشخصية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"birthDate\",\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"تاريخ الميلاد\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 449,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"date\",\n                                                                        id: \"birthDate\",\n                                                                        value: formData.birthDate,\n                                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                                    ...prev,\n                                                                                    birthDate: e.target.value\n                                                                                })),\n                                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 452,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                lineNumber: 448,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"birthPlace\",\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"مكان الميلاد\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 462,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        id: \"birthPlace\",\n                                                                        value: formData.birthPlace,\n                                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                                    ...prev,\n                                                                                    birthPlace: e.target.value\n                                                                                })),\n                                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                        placeholder: \"مثال: طرابلس\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 465,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"motherName\",\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"اسم الأم\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 476,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        id: \"motherName\",\n                                                                        value: formData.motherName,\n                                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                                    ...prev,\n                                                                                    motherName: e.target.value\n                                                                                })),\n                                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                        placeholder: \"اسم الأم الثلاثي\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 479,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"maritalStatus\",\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"الحالة الاجتماعية\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 490,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        id: \"maritalStatus\",\n                                                                        value: formData.maritalStatus,\n                                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                                    ...prev,\n                                                                                    maritalStatus: e.target.value\n                                                                                })),\n                                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"\",\n                                                                                children: \"اختر الحالة الاجتماعية\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                                lineNumber: 499,\n                                                                                columnNumber: 21\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"أعزب\",\n                                                                                children: \"أعزب\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                                lineNumber: 500,\n                                                                                columnNumber: 21\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"متزوج\",\n                                                                                children: \"متزوج\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                                lineNumber: 501,\n                                                                                columnNumber: 21\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"مطلق\",\n                                                                                children: \"مطلق\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                                lineNumber: 502,\n                                                                                columnNumber: 21\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"أرمل\",\n                                                                                children: \"أرمل\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                                lineNumber: 503,\n                                                                                columnNumber: 21\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 493,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                lineNumber: 489,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-6 mt-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                        children: \"بيانات العمل\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                        lineNumber: 511,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"appointmentDate\",\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"تاريخ التعيين\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 515,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"date\",\n                                                                        id: \"appointmentDate\",\n                                                                        value: formData.appointmentDate,\n                                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                                    ...prev,\n                                                                                    appointmentDate: e.target.value\n                                                                                })),\n                                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                        placeholder: \"اختر تاريخ التعيين\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 518,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                lineNumber: 514,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"lastPromotionDate\",\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"تاريخ آخر ترقية\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 529,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"date\",\n                                                                        id: \"lastPromotionDate\",\n                                                                        value: formData.lastPromotionDate,\n                                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                                    ...prev,\n                                                                                    lastPromotionDate: e.target.value\n                                                                                })),\n                                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 532,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                lineNumber: 528,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                        lineNumber: 513,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                lineNumber: 510,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-6 mt-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                        children: \"بيانات المصرف\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"bankName\",\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"اسم المصرف\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 549,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        id: \"bankName\",\n                                                                        value: formData.bankName,\n                                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                                    ...prev,\n                                                                                    bankName: e.target.value\n                                                                                })),\n                                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                        placeholder: \"مثال: مصرف الجمهورية\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 552,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                lineNumber: 548,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"bankBranch\",\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"فرع المصرف\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 563,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        id: \"bankBranch\",\n                                                                        value: formData.bankBranch,\n                                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                                    ...prev,\n                                                                                    bankBranch: e.target.value\n                                                                                })),\n                                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                        placeholder: \"مثال: فرع الخمس\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 566,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                lineNumber: 562,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"md:col-span-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"bankAccount\",\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"رقم الحساب المصرفي (15 رقم)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 577,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        id: \"bankAccount\",\n                                                                        value: formData.bankAccount,\n                                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                                    ...prev,\n                                                                                    bankAccount: e.target.value.replace(/\\D/g, '').slice(0, 15)\n                                                                                })),\n                                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono\",\n                                                                        placeholder: \"***************\",\n                                                                        maxLength: 15\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 580,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"mt-1 text-sm text-gray-500\",\n                                                                        children: [\n                                                                            formData.bankAccount.length,\n                                                                            \"/15 رقم\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 589,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                lineNumber: 576,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                        lineNumber: 547,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                lineNumber: 544,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-6 mt-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                        children: \"الإجازات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                        lineNumber: 598,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"leaveBalance\",\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"رصيد الإجازات (بالأيام)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 602,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        id: \"leaveBalance\",\n                                                                        value: formData.leaveBalance,\n                                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                                    ...prev,\n                                                                                    leaveBalance: e.target.value\n                                                                                })),\n                                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                        placeholder: \"30\",\n                                                                        min: \"0\",\n                                                                        max: \"365\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 605,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                lineNumber: 601,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"leaveType\",\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"نوع الإجازة الافتراضي\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 618,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        id: \"leaveType\",\n                                                                        value: formData.leaveType,\n                                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                                    ...prev,\n                                                                                    leaveType: e.target.value\n                                                                                })),\n                                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"سنوية\",\n                                                                                children: \"سنوية\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                                lineNumber: 627,\n                                                                                columnNumber: 21\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"مرضية\",\n                                                                                children: \"مرضية\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                                lineNumber: 628,\n                                                                                columnNumber: 21\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"بدون مرتب\",\n                                                                                children: \"بدون مرتب\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                                lineNumber: 629,\n                                                                                columnNumber: 21\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 621,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                lineNumber: 617,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                        lineNumber: 600,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                lineNumber: 597,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-6 mt-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                        children: \"البيانات الدراسية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                        lineNumber: 637,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"qualification\",\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"المؤهل العلمي\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 641,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        id: \"qualification\",\n                                                                        value: formData.qualification,\n                                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                                    ...prev,\n                                                                                    qualification: e.target.value\n                                                                                })),\n                                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"\",\n                                                                                children: \"اختر المؤهل العلمي\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                                lineNumber: 650,\n                                                                                columnNumber: 21\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"ابتدائي\",\n                                                                                children: \"ابتدائي\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                                lineNumber: 651,\n                                                                                columnNumber: 21\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"إعدادي\",\n                                                                                children: \"إعدادي\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                                lineNumber: 652,\n                                                                                columnNumber: 21\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"ثانوي\",\n                                                                                children: \"ثانوي\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                                lineNumber: 653,\n                                                                                columnNumber: 21\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"دبلوم متوسط\",\n                                                                                children: \"دبلوم متوسط\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                                lineNumber: 654,\n                                                                                columnNumber: 21\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"دبلوم عالي\",\n                                                                                children: \"دبلوم عالي\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                                lineNumber: 655,\n                                                                                columnNumber: 21\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"بكالوريوس\",\n                                                                                children: \"بكالوريوس\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                                lineNumber: 656,\n                                                                                columnNumber: 21\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"ماجستير\",\n                                                                                children: \"ماجستير\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                                lineNumber: 657,\n                                                                                columnNumber: 21\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"دكتوراه\",\n                                                                                children: \"دكتوراه\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                                lineNumber: 658,\n                                                                                columnNumber: 21\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 644,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                lineNumber: 640,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"qualificationDate\",\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"تاريخ الحصول على المؤهل\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 663,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"date\",\n                                                                        id: \"qualificationDate\",\n                                                                        value: formData.qualificationDate,\n                                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                                    ...prev,\n                                                                                    qualificationDate: e.target.value\n                                                                                })),\n                                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 666,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                                lineNumber: 662,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                        lineNumber: 639,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                lineNumber: 636,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-4 space-x-reverse pt-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        disabled: loading,\n                                                        className: \"flex-1 bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed font-medium\",\n                                                        children: loading ? 'جاري الحفظ...' : 'حفظ الموظف'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                        lineNumber: 678,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"/employees\",\n                                                        className: \"flex-1 bg-gray-300 text-gray-700 py-3 px-4 rounded-lg hover:bg-gray-400 transition-colors text-center font-medium\",\n                                                        children: \"إلغاء\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                        lineNumber: 685,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                                lineNumber: 677,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n                lineNumber: 275,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\",\n        lineNumber: 252,\n        columnNumber: 5\n    }, this);\n}\n_s(NewEmployeePage, \"tZ70fBHQAGTmU5ZAk5n7u5ta9kg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = NewEmployeePage;\nvar _c;\n$RefreshReg$(_c, \"NewEmployeePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/employees/new/page.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chamza%5C%5CDesktop%5C%5Cmergeb%20HR%5C%5Capps%5C%5Cpersonnel-frontend%5C%5Csrc%5C%5Capp%5C%5Cemployees%5C%5Cnew%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);
(()=>{var a={};a.id=105,a.ids=[105],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},1115:()=>{},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9699:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(43560);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14276:()=>{},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23930:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(25459).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\mergeb HR\\apps\\personnel-frontend\\src\\app\\dashboard\\page.tsx","default")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},35459:()=>{},36063:(a,b,c)=>{Promise.resolve().then(c.bind(c,23930))},37355:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,48365,23)),Promise.resolve().then(c.t.bind(c,64596,23)),Promise.resolve().then(c.t.bind(c,56186,23)),Promise.resolve().then(c.t.bind(c,67805,23)),Promise.resolve().then(c.t.bind(c,27561,23)),Promise.resolve().then(c.t.bind(c,47569,23)),Promise.resolve().then(c.t.bind(c,42747,23)),Promise.resolve().then(c.t.bind(c,56676,23)),Promise.resolve().then(c.bind(c,97225))},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},42931:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,58671,23)),Promise.resolve().then(c.t.bind(c,56542,23)),Promise.resolve().then(c.t.bind(c,88248,23)),Promise.resolve().then(c.t.bind(c,49743,23)),Promise.resolve().then(c.t.bind(c,96231,23)),Promise.resolve().then(c.t.bind(c,10959,23)),Promise.resolve().then(c.t.bind(c,72041,23)),Promise.resolve().then(c.t.bind(c,95094,23)),Promise.resolve().then(c.t.bind(c,67487,23))},47570:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>h,metadata:()=>g});var d=c(5939),e=c(50157),f=c.n(e);c(14276);let g={title:"نظام إدارة الموارد البشرية - مرجب",description:"نظام شامل لإدارة الموارد البشرية والقوة العمومية",keywords:["الموارد البشرية","إدارة الموظفين","القوة العمومية","مرجب"],authors:[{name:"Mergeb HR System"}],viewport:"width=device-width, initial-scale=1",robots:"noindex, nofollow"};function h({children:a}){return(0,d.jsx)("html",{lang:"ar",dir:"rtl",children:(0,d.jsx)("body",{className:`${f().variable} font-sans antialiased bg-gradient-to-br from-secondary-50 via-white to-primary-50 min-h-screen`,children:(0,d.jsx)("div",{className:"relative",children:a})})})}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71159:(a,b,c)=>{"use strict";var d=c(30291);c.o(d,"useParams")&&c.d(b,{useParams:function(){return d.useParams}}),c.o(d,"useRouter")&&c.d(b,{useRouter:function(){return d.useRouter}})},79774:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>D.a,__next_app__:()=>J,handler:()=>L,pages:()=>I,routeModule:()=>K,tree:()=>H});var d=c(73653),e=c(97714),f=c(85250),g=c(37587),h=c(22369),i=c(1889),j=c(96232),k=c(22841),l=c(46537),m=c(46027),n=c(78559),o=c(75928),p=c(19374),q=c(65971),r=c(261),s=c(79898),t=c(32967),u=c(26713),v=c(40139),w=c(14248),x=c(59580),y=c(57749),z=c(53123),A=c(89745),B=c(86439),C=c(58671),D=c.n(C),E=c(18283),F=c(39818),G={};for(let a in E)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(G[a]=()=>E[a]);c.d(b,G);let H={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,23930)),"C:\\Users\\<USER>\\Desktop\\mergeb HR\\apps\\personnel-frontend\\src\\app\\dashboard\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,9699))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,47570)),"C:\\Users\\<USER>\\Desktop\\mergeb HR\\apps\\personnel-frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,58671,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,17983,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,15034,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,54693,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,9699))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,I=["C:\\Users\\<USER>\\Desktop\\mergeb HR\\apps\\personnel-frontend\\src\\app\\dashboard\\page.tsx"],J={require:c,loadChunk:()=>Promise.resolve()},K=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:H},distDir:".next",relativeProjectDir:""});async function L(a,b,d){var C;let G="/dashboard/page";"/index"===G&&(G="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await K.prepare(a,b,{srcPage:G,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(G),{isOnDemandRevalidate:ah}=O,ai=K.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(C=$.routes[ag]??$.dynamicRoutes[ag])?void 0:C.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===K.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&K.isDev&&(aB=aa),K.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...E,tree:H,pages:I,GlobalError:D(),handler:L,routeModule:K,__next_app__:J};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:G,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=K.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return K.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:K,page:G,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),K.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>K.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:K.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!K.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===K.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await K.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await K.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),K.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!K.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&F.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await K.onRequestError(a,b,{routerKind:"App Router",routePath:G,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},80848:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>i});var d=c(78157),e=c(31768),f=c(71159),g=c(94496),h=c.n(g);function i(){let[a,b]=(0,e.useState)({totalEmployees:0,officers:0,ncos:0,employees:0}),[c,g]=(0,e.useState)(!0),i=(0,f.useRouter)(),j=async()=>{try{let a=localStorage.getItem("token"),b=await fetch("http://127.0.0.1:4001/backup",{method:"POST",headers:{Authorization:`Bearer ${a}`}});if(b.ok){let a=await b.blob(),c=window.URL.createObjectURL(a),d=document.createElement("a");d.href=c,d.download=`backup-${new Date().toISOString().replace(/[:.]/g,"-")}.sql`,document.body.appendChild(d),d.click(),window.URL.revokeObjectURL(c),document.body.removeChild(d),alert("تم إنشاء النسخة الاحتياطية بنجاح")}else{let a=await b.json();alert("خطأ في إنشاء النسخة الاحتياطية: "+a.error)}}catch(a){alert("خطأ في الاتصال بالخادم")}};return c?(0,d.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-gray-600",children:"جاري تحميل البيانات..."})]})}):(0,d.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50",dir:"rtl",children:[(0,d.jsx)("header",{className:"bg-white/80 backdrop-blur-md shadow-lg border-b border-blue-100",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,d.jsx)("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center",children:(0,d.jsx)("span",{className:"text-white font-bold text-lg",children:"م"})}),(0,d.jsx)("h1",{className:"text-xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent",children:"لوحة التحكم - نظام الموارد البشرية"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,d.jsx)("a",{href:"http://127.0.0.1:3002",target:"_blank",rel:"noopener noreferrer",className:"bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg",children:"نظام القوة العمومية"}),(0,d.jsx)("button",{type:"button",onClick:()=>{localStorage.removeItem("token"),i.push("/login")},className:"bg-gradient-to-r from-red-500 to-rose-600 hover:from-red-600 hover:to-rose-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg",children:"تسجيل الخروج"})]})]})})}),(0,d.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,d.jsx)("div",{className:"bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border border-blue-100 p-6 hover:shadow-xl transition-all duration-300 hover:scale-105",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"إجمالي الموظفين"}),(0,d.jsx)("p",{className:"text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent",children:a.totalEmployees})]}),(0,d.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center shadow-lg",children:(0,d.jsx)("span",{className:"text-white text-xl",children:"\uD83D\uDC65"})})]})}),(0,d.jsx)("div",{className:"bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border border-green-100 p-6 hover:shadow-xl transition-all duration-300 hover:scale-105",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"الضباط"}),(0,d.jsx)("p",{className:"text-3xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent",children:a.officers})]}),(0,d.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center shadow-lg",children:(0,d.jsx)("span",{className:"text-white text-xl",children:"⭐"})})]})}),(0,d.jsx)("div",{className:"bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border border-yellow-100 p-6 hover:shadow-xl transition-all duration-300 hover:scale-105",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"ضباط الصف"}),(0,d.jsx)("p",{className:"text-3xl font-bold bg-gradient-to-r from-yellow-600 to-orange-600 bg-clip-text text-transparent",children:a.ncos})]}),(0,d.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-600 rounded-lg flex items-center justify-center shadow-lg",children:(0,d.jsx)("span",{className:"text-white text-xl",children:"\uD83C\uDF96️"})})]})}),(0,d.jsx)("div",{className:"bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border border-purple-100 p-6 hover:shadow-xl transition-all duration-300 hover:scale-105",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"الموظفون"}),(0,d.jsx)("p",{className:"text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent",children:a.employees})]}),(0,d.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center shadow-lg",children:(0,d.jsx)("span",{className:"text-white text-xl",children:"\uD83D\uDC64"})})]})})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,d.jsxs)(h(),{href:"/employees",className:"bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border border-blue-100 p-6 hover:shadow-xl transition-all duration-300 hover:scale-105 group",children:[(0,d.jsxs)("div",{className:"flex items-center mb-4",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3 group-hover:scale-110 transition-transform",children:(0,d.jsx)("span",{className:"text-white text-xl",children:"\uD83D\uDCCB"})}),(0,d.jsx)("h3",{className:"text-lg font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent",children:"إدارة الموظفين"})]}),(0,d.jsx)("p",{className:"text-gray-600",children:"عرض وإدارة بيانات الموظفين"})]}),(0,d.jsxs)(h(),{href:"/employees/new",className:"bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border border-green-100 p-6 hover:shadow-xl transition-all duration-300 hover:scale-105 group",children:[(0,d.jsxs)("div",{className:"flex items-center mb-4",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center mr-3 group-hover:scale-110 transition-transform",children:(0,d.jsx)("span",{className:"text-white text-xl",children:"➕"})}),(0,d.jsx)("h3",{className:"text-lg font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent",children:"إضافة موظف"})]}),(0,d.jsx)("p",{className:"text-gray-600",children:"إضافة موظف جديد إلى النظام"})]}),(0,d.jsxs)(h(),{href:"/employees/import",className:"bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border border-orange-100 p-6 hover:shadow-xl transition-all duration-300 hover:scale-105 group",children:[(0,d.jsxs)("div",{className:"flex items-center mb-4",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-orange-500 to-red-600 rounded-lg flex items-center justify-center mr-3 group-hover:scale-110 transition-transform",children:(0,d.jsx)("span",{className:"text-white text-xl",children:"\uD83D\uDCCA"})}),(0,d.jsx)("h3",{className:"text-lg font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent",children:"استيراد Excel"})]}),(0,d.jsx)("p",{className:"text-gray-600",children:"استيراد بيانات الموظفين من ملف Excel"})]}),(0,d.jsxs)(h(),{href:"/reports",className:"bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border border-purple-100 p-6 hover:shadow-xl transition-all duration-300 hover:scale-105 group",children:[(0,d.jsxs)("div",{className:"flex items-center mb-4",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center mr-3 group-hover:scale-110 transition-transform",children:(0,d.jsx)("span",{className:"text-white text-xl",children:"\uD83D\uDCCA"})}),(0,d.jsx)("h3",{className:"text-lg font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent",children:"التقارير"})]}),(0,d.jsx)("p",{className:"text-gray-600",children:"عرض التقارير والإحصائيات"})]}),(0,d.jsxs)(h(),{href:"/users",className:"bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border border-indigo-100 p-6 hover:shadow-xl transition-all duration-300 hover:scale-105 group",children:[(0,d.jsxs)("div",{className:"flex items-center mb-4",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center mr-3 group-hover:scale-110 transition-transform",children:(0,d.jsx)("span",{className:"text-white text-xl",children:"\uD83D\uDC65"})}),(0,d.jsx)("h3",{className:"text-lg font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent",children:"إدارة المستخدمين"})]}),(0,d.jsx)("p",{className:"text-gray-600",children:"إدارة المستخدمين والصلاحيات"})]}),(0,d.jsxs)("div",{className:"bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border border-teal-100 p-6 hover:shadow-xl transition-all duration-300 hover:scale-105",children:[(0,d.jsxs)("div",{className:"flex items-center mb-4",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-teal-500 to-cyan-600 rounded-lg flex items-center justify-center mr-3",children:(0,d.jsx)("span",{className:"text-white text-xl",children:"\uD83D\uDCBE"})}),(0,d.jsx)("h3",{className:"text-lg font-bold bg-gradient-to-r from-teal-600 to-cyan-600 bg-clip-text text-transparent",children:"النسخ الاحتياطية"})]}),(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:"إدارة النسخ الاحتياطية للنظام"}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("button",{type:"button",onClick:j,className:"w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white py-2 px-4 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg text-sm font-medium",children:"إنشاء نسخة احتياطية"}),(0,d.jsx)("button",{type:"button",onClick:()=>{let a=document.createElement("input");a.type="file",a.accept=".sql",a.onchange=async a=>{let b=a.target.files?.[0];if(!b)return;let c=new FileReader;c.onload=async a=>{let b=a.target?.result;if(confirm("هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية."))try{let a=localStorage.getItem("token"),c=await fetch("http://127.0.0.1:4001/restore",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${a}`},body:JSON.stringify({sqlData:b})});if(c.ok)alert("تم استعادة النسخة الاحتياطية بنجاح"),window.location.reload();else{let a=await c.json();alert("خطأ في استعادة النسخة الاحتياطية: "+a.error)}}catch(a){alert("خطأ في الاتصال بالخادم")}},c.readAsText(b)},a.click()},className:"w-full bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white py-2 px-4 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg text-sm font-medium",children:"استعادة نسخة احتياطية"})]})]})]})]})]})}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},96311:(a,b,c)=>{Promise.resolve().then(c.bind(c,80848))}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[635,533,496],()=>b(b.s=79774));module.exports=c})();
'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

interface DashboardStats {
  totalEmployees: number;
  officers: number;
  ncos: number;
  employees: number;
}

export default function DashboardPage() {
  const [stats, setStats] = useState<DashboardStats>({
    totalEmployees: 0,
    officers: 0,
    ncos: 0,
    employees: 0
  });
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (!token) {
      router.push('/login');
      return;
    }

    // Fetch real stats from API
    fetchStats(token);
  }, [router]);

  const fetchStats = async (token: string) => {
    try {
      // Fetch employees to calculate stats
      const employeesResponse = await fetch('http://127.0.0.1:4001/employees', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      const ranksResponse = await fetch('http://127.0.0.1:4001/ranks', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (employeesResponse.ok && ranksResponse.ok) {
        const employees = await employeesResponse.json();
        const ranks = await ranksResponse.json();

        let officers = 0, ncos = 0, employeesCount = 0;

        employees.forEach((emp: any) => {
          const rank = ranks.find((r: any) => r.key === emp.rank_key);
          if (rank) {
            if (rank.category === 'officer') officers++;
            else if (rank.category === 'nco') ncos++;
            else if (rank.category === 'employee') employeesCount++;
          }
        });

        setStats({
          totalEmployees: employees.length,
          officers,
          ncos,
          employees: employeesCount
        });
      } else {
        // Fallback to default stats if API fails
        setStats({
          totalEmployees: 0,
          officers: 0,
          ncos: 0,
          employees: 0
        });
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
      setStats({
        totalEmployees: 0,
        officers: 0,
        ncos: 0,
        employees: 0
      });
    } finally {
      setLoading(false);
    }
  };

  const handleBackup = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://127.0.0.1:4001/backup', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `backup-${new Date().toISOString().replace(/[:.]/g, '-')}.sql`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        alert('تم إنشاء النسخة الاحتياطية بنجاح');
      } else {
        const error = await response.json();
        alert('خطأ في إنشاء النسخة الاحتياطية: ' + error.error);
      }
    } catch (error) {
      alert('خطأ في الاتصال بالخادم');
    }
  };

  const handleRestore = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.sql';
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = async (e) => {
        const sqlData = e.target?.result as string;

        if (confirm('هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.')) {
          try {
            const token = localStorage.getItem('token');
            const response = await fetch('http://127.0.0.1:4001/restore', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`,
              },
              body: JSON.stringify({ sqlData }),
            });

            if (response.ok) {
              alert('تم استعادة النسخة الاحتياطية بنجاح');
              window.location.reload();
            } else {
              const error = await response.json();
              alert('خطأ في استعادة النسخة الاحتياطية: ' + error.error);
            }
          } catch (error) {
            alert('خطأ في الاتصال بالخادم');
          }
        }
      };
      reader.readAsText(file);
    };
    input.click();
  };

  const handleLogout = () => {
    localStorage.removeItem('token');
    router.push('/login');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري تحميل البيانات...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50" dir="rtl">
      <header className="bg-white/80 backdrop-blur-md shadow-lg border-b border-blue-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3 space-x-reverse">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">م</span>
              </div>
              <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                لوحة التحكم - نظام الموارد البشرية
              </h1>
            </div>
            <div className="flex items-center space-x-3 space-x-reverse">
              <a
                href="http://127.0.0.1:3002"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg"
              >
                نظام القوة العمومية
              </a>
              <button
                type="button"
                onClick={handleLogout}
                className="bg-gradient-to-r from-red-500 to-rose-600 hover:from-red-600 hover:to-rose-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg"
              >
                تسجيل الخروج
              </button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border border-blue-100 p-6 hover:shadow-xl transition-all duration-300 hover:scale-105">
            <div className="flex items-center">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600">إجمالي الموظفين</p>
                <p className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">{stats.totalEmployees}</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center shadow-lg">
                <span className="text-white text-xl">👥</span>
              </div>
            </div>
          </div>

          <div className="bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border border-green-100 p-6 hover:shadow-xl transition-all duration-300 hover:scale-105">
            <div className="flex items-center">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600">الضباط</p>
                <p className="text-3xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">{stats.officers}</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center shadow-lg">
                <span className="text-white text-xl">⭐</span>
              </div>
            </div>
          </div>

          <div className="bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border border-yellow-100 p-6 hover:shadow-xl transition-all duration-300 hover:scale-105">
            <div className="flex items-center">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600">ضباط الصف</p>
                <p className="text-3xl font-bold bg-gradient-to-r from-yellow-600 to-orange-600 bg-clip-text text-transparent">{stats.ncos}</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-600 rounded-lg flex items-center justify-center shadow-lg">
                <span className="text-white text-xl">🎖️</span>
              </div>
            </div>
          </div>

          <div className="bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border border-purple-100 p-6 hover:shadow-xl transition-all duration-300 hover:scale-105">
            <div className="flex items-center">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600">الموظفون</p>
                <p className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">{stats.employees}</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center shadow-lg">
                <span className="text-white text-xl">👤</span>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Link
            href="/employees"
            className="bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border border-blue-100 p-6 hover:shadow-xl transition-all duration-300 hover:scale-105 group"
          >
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3 group-hover:scale-110 transition-transform">
                <span className="text-white text-xl">📋</span>
              </div>
              <h3 className="text-lg font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">إدارة الموظفين</h3>
            </div>
            <p className="text-gray-600">عرض وإدارة بيانات الموظفين</p>
          </Link>

          <Link
            href="/employees/new"
            className="bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border border-green-100 p-6 hover:shadow-xl transition-all duration-300 hover:scale-105 group"
          >
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center mr-3 group-hover:scale-110 transition-transform">
                <span className="text-white text-xl">➕</span>
              </div>
              <h3 className="text-lg font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">إضافة موظف</h3>
            </div>
            <p className="text-gray-600">إضافة موظف جديد إلى النظام</p>
          </Link>

          <Link
            href="/employees/import"
            className="bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border border-orange-100 p-6 hover:shadow-xl transition-all duration-300 hover:scale-105 group"
          >
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-600 rounded-lg flex items-center justify-center mr-3 group-hover:scale-110 transition-transform">
                <span className="text-white text-xl">📊</span>
              </div>
              <h3 className="text-lg font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">استيراد Excel</h3>
            </div>
            <p className="text-gray-600">استيراد بيانات الموظفين من ملف Excel</p>
          </Link>

          <Link
            href="/reports"
            className="bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border border-purple-100 p-6 hover:shadow-xl transition-all duration-300 hover:scale-105 group"
          >
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center mr-3 group-hover:scale-110 transition-transform">
                <span className="text-white text-xl">📊</span>
              </div>
              <h3 className="text-lg font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">التقارير</h3>
            </div>
            <p className="text-gray-600">عرض التقارير والإحصائيات</p>
          </Link>

          <Link
            href="/users"
            className="bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border border-indigo-100 p-6 hover:shadow-xl transition-all duration-300 hover:scale-105 group"
          >
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center mr-3 group-hover:scale-110 transition-transform">
                <span className="text-white text-xl">👥</span>
              </div>
              <h3 className="text-lg font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">إدارة المستخدمين</h3>
            </div>
            <p className="text-gray-600">إدارة المستخدمين والصلاحيات</p>
          </Link>

          <div className="bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border border-teal-100 p-6 hover:shadow-xl transition-all duration-300 hover:scale-105">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-gradient-to-r from-teal-500 to-cyan-600 rounded-lg flex items-center justify-center mr-3">
                <span className="text-white text-xl">💾</span>
              </div>
              <h3 className="text-lg font-bold bg-gradient-to-r from-teal-600 to-cyan-600 bg-clip-text text-transparent">النسخ الاحتياطية</h3>
            </div>
            <p className="text-gray-600 mb-4">إدارة النسخ الاحتياطية للنظام</p>
            <div className="space-y-2">
              <button
                type="button"
                onClick={handleBackup}
                className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white py-2 px-4 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg text-sm font-medium"
              >
                إنشاء نسخة احتياطية
              </button>
              <button
                type="button"
                onClick={handleRestore}
                className="w-full bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white py-2 px-4 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg text-sm font-medium"
              >
                استعادة نسخة احتياطية
              </button>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

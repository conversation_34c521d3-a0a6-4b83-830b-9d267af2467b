import type { Metadata } from "next";
import { Noto_Sans_Arabic } from "next/font/google";
import "./globals.css";

const notoSansArabic = Noto_Sans_Arabic({
  subsets: ["arabic"],
  variable: "--font-noto-arabic",
  display: "swap",
  weight: ["300", "400", "500", "600", "700"],
});

export const metadata: Metadata = {
  title: "نظام إدارة القوة العمومية - مرجب",
  description: "نظام شامل لإدارة الأسلحة والمركبات وأجهزة اللاسلكي",
  keywords: ["القوة العمومية", "الأسلحة", "المركبات", "اللاسلكي", "مرجب"],
  authors: [{ name: "Mergeb Assets System" }],
  robots: "noindex, nofollow", // For internal system
};

export const viewport = {
  width: "device-width",
  initialScale: 1,
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl">
      <body className={`${notoSansArabic.variable} font-sans antialiased bg-gradient-to-br from-secondary-50 via-white to-asset-50 min-h-screen`}>
        <div className="relative">
          {children}
        </div>
      </body>
    </html>
  )
}

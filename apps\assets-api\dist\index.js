import 'dotenv/config';
import express from 'express';
import helmet from 'helmet';
import cors from 'cors';
import jwt from 'jsonwebtoken';
const app = express();
app.use(helmet());
app.use(cors());
app.use(express.json());
const PORT = process.env.PORT || 4002;
const JWT_SECRET = process.env.JWT_SECRET || 'dev_secret';
// Auth middleware
const authRequired = (req, res, next) => {
    if (req.path === '/health' || req.path === '/auth/login')
        return next();
    const auth = req.headers.authorization;
    if (!auth?.startsWith('Bearer '))
        return res.status(401).json({ error: 'Unauthorized' });
    try {
        const token = auth.slice(7);
        const decoded = jwt.verify(token, JWT_SECRET);
        req.user = decoded;
        next();
    }
    catch (e) {
        return res.status(401).json({ error: 'Invalid token' });
    }
};
app.use(authRequired);
// Public routes
app.post('/auth/login', (req, res) => {
    const token = jwt.sign({ role: 'admin', sub: 'demo' }, JWT_SECRET, { expiresIn: '8h' });
    res.json({ token });
});
app.get('/health', (_, res) => res.json({ ok: true, service: 'assets-api' }));
// Asset types
const ASSET_TYPES = {
    WEAPON: 'weapon',
    VEHICLE: 'vehicle',
    RADIO: 'radio'
};
// Mock data for development
let mockAssets = [
    {
        id: 1,
        type: ASSET_TYPES.WEAPON,
        name: 'بندقية كلاشنكوف AK-47',
        serialNumber: 'AK47-001',
        model: 'AK-47',
        manufacturer: 'إيزماش',
        status: 'available',
        condition: 'good',
        assignedTo: null,
        assignedDate: null,
        location: 'مخزن الأسلحة الرئيسي',
        notes: 'في حالة جيدة',
        createdAt: new Date().toISOString()
    },
    {
        id: 2,
        type: ASSET_TYPES.VEHICLE,
        name: 'سيارة هيلوكس',
        serialNumber: 'VEH-001',
        model: 'Hilux 2020',
        manufacturer: 'تويوتا',
        status: 'assigned',
        condition: 'excellent',
        assignedTo: 'أحمد محمد علي',
        assignedDate: '2024-01-15',
        location: 'موقف السيارات',
        notes: 'سيارة خدمية',
        plateNumber: 'بغداد 12345',
        createdAt: new Date().toISOString()
    },
    {
        id: 3,
        type: ASSET_TYPES.RADIO,
        name: 'جهاز لاسلكي موتورولا',
        serialNumber: 'RAD-001',
        model: 'GP340',
        manufacturer: 'موتورولا',
        status: 'maintenance',
        condition: 'fair',
        assignedTo: null,
        assignedDate: null,
        location: 'ورشة الصيانة',
        notes: 'يحتاج صيانة دورية',
        frequency: '400-470 MHz',
        createdAt: new Date().toISOString()
    }
];
// Assets CRUD endpoints
app.get('/assets', (req, res) => {
    const { type, status, search } = req.query;
    let filteredAssets = mockAssets;
    // Search functionality
    if (search) {
        const searchTerm = search.toLowerCase();
        filteredAssets = filteredAssets.filter(asset => asset.name.toLowerCase().includes(searchTerm) ||
            asset.serialNumber.toLowerCase().includes(searchTerm) ||
            asset.model.toLowerCase().includes(searchTerm) ||
            (asset.assignedTo && asset.assignedTo.toLowerCase().includes(searchTerm)));
    }
    if (type) {
        filteredAssets = filteredAssets.filter(asset => asset.type === type);
    }
    if (status) {
        filteredAssets = filteredAssets.filter(asset => asset.status === status);
    }
    res.json(filteredAssets);
});
app.get('/assets/:id', (req, res) => {
    const { id } = req.params;
    const asset = mockAssets.find(a => a.id === parseInt(id));
    if (!asset) {
        return res.status(404).json({ error: 'الأصل غير موجود' });
    }
    res.json(asset);
});
app.post('/assets', (req, res) => {
    const { type, name, serialNumber, model, manufacturer, status = 'available', condition = 'good', location, notes, plateNumber, frequency } = req.body;
    // Validation
    if (!type || !name || !serialNumber) {
        return res.status(400).json({ error: 'النوع والاسم والرقم التسلسلي مطلوبة' });
    }
    // Check if serial number already exists
    if (mockAssets.some(a => a.serialNumber === serialNumber)) {
        return res.status(400).json({ error: 'الرقم التسلسلي موجود مسبقاً' });
    }
    const newAsset = {
        id: Math.max(...mockAssets.map(a => a.id)) + 1,
        type,
        name,
        serialNumber,
        model: model || '',
        manufacturer: manufacturer || '',
        status,
        condition,
        assignedTo: null,
        assignedDate: null,
        location: location || '',
        notes: notes || '',
        ...(plateNumber && { plateNumber }),
        ...(frequency && { frequency }),
        createdAt: new Date().toISOString()
    };
    mockAssets.push(newAsset);
    res.status(201).json(newAsset);
});
app.put('/assets/:id', (req, res) => {
    const { id } = req.params;
    const assetIndex = mockAssets.findIndex(a => a.id === parseInt(id));
    if (assetIndex === -1) {
        return res.status(404).json({ error: 'الأصل غير موجود' });
    }
    const updatedAsset = {
        ...mockAssets[assetIndex],
        ...req.body,
        id: parseInt(id) // Ensure ID doesn't change
    };
    mockAssets[assetIndex] = updatedAsset;
    res.json(updatedAsset);
});
app.delete('/assets/:id', (req, res) => {
    const { id } = req.params;
    const assetIndex = mockAssets.findIndex(a => a.id === parseInt(id));
    if (assetIndex === -1) {
        return res.status(404).json({ error: 'الأصل غير موجود' });
    }
    mockAssets.splice(assetIndex, 1);
    res.json({ success: true });
});
// Assignment endpoints
app.post('/assets/:id/assign', (req, res) => {
    const { id } = req.params;
    const { assignedTo } = req.body;
    const assetIndex = mockAssets.findIndex(a => a.id === parseInt(id));
    if (assetIndex === -1) {
        return res.status(404).json({ error: 'الأصل غير موجود' });
    }
    if (!assignedTo) {
        return res.status(400).json({ error: 'اسم المستلم مطلوب' });
    }
    mockAssets[assetIndex] = {
        ...mockAssets[assetIndex],
        assignedTo,
        assignedDate: new Date().toISOString().split('T')[0],
        status: 'assigned'
    };
    res.json(mockAssets[assetIndex]);
});
app.post('/assets/:id/return', (req, res) => {
    const { id } = req.params;
    const assetIndex = mockAssets.findIndex(a => a.id === parseInt(id));
    if (assetIndex === -1) {
        return res.status(404).json({ error: 'الأصل غير موجود' });
    }
    mockAssets[assetIndex] = {
        ...mockAssets[assetIndex],
        assignedTo: null,
        assignedDate: null,
        status: 'available'
    };
    res.json(mockAssets[assetIndex]);
});
// Statistics endpoint
app.get('/stats', (req, res) => {
    const stats = {
        total: mockAssets.length,
        weapons: mockAssets.filter(a => a.type === ASSET_TYPES.WEAPON).length,
        vehicles: mockAssets.filter(a => a.type === ASSET_TYPES.VEHICLE).length,
        radios: mockAssets.filter(a => a.type === ASSET_TYPES.RADIO).length,
        available: mockAssets.filter(a => a.status === 'available').length,
        assigned: mockAssets.filter(a => a.status === 'assigned').length,
        maintenance: mockAssets.filter(a => a.status === 'maintenance').length
    };
    res.json(stats);
});
app.listen(PORT, () => {
    console.log(`Assets API listening on :${PORT}`);
});

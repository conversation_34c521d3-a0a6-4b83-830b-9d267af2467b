(()=>{var a={};a.id=841,a.ids=[841],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},362:(a,b,c)=>{Promise.resolve().then(c.bind(c,11300))},1115:()=>{},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9699:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(43560);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},10722:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(25459).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mergeb HR\\\\apps\\\\personnel-frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\mergeb HR\\apps\\personnel-frontend\\src\\app\\employees\\new\\page.tsx","default")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11300:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>i});var d=c(78157),e=c(31768),f=c(71159),g=c(94496),h=c.n(g);function i(){let[a,b]=(0,e.useState)({name:"",rankKey:"",militaryNumber:"",nationalId:"",unitId:"",birthDate:"",birthPlace:"",motherName:"",maritalStatus:"",appointmentDate:"",lastPromotionDate:"",bankName:"",bankBranch:"",bankAccount:"",leaveBalance:"",leaveType:"سنوية",employeeStatus:"مستمر",qualification:"",qualificationDate:"",photo:null}),[c,g]=(0,e.useState)([]),[i,j]=(0,e.useState)([]),[k,l]=(0,e.useState)(!1),[m,n]=(0,e.useState)(""),[o,p]=(0,e.useState)(""),q=(0,f.useRouter)(),r=()=>c.find(b=>b.key===a.rankKey),s=async b=>{if(b.preventDefault(),l(!0),n(""),p(""),12!==a.nationalId.length){n("الرقم الوطني يجب أن يكون 12 رقماً"),l(!1);return}if(a.bankAccount&&15!==a.bankAccount.length){n("رقم الحساب المصرفي يجب أن يكون 15 رقماً"),l(!1);return}let c=(()=>{let b=r();if(!b)return"يجب اختيار الرتبة أولاً";switch(b.category){case"officer":if("بلا"!==a.militaryNumber)return'بالنسبة للضباط يجب أن تكون قيمة الرقم العسكري "بلا"';break;case"nco":if(!a.militaryNumber||""===a.militaryNumber.trim())return"الرقم العسكري إجباري لضباط الصف";if("بلا"===a.militaryNumber||"موظف"===a.militaryNumber)return"أدخل رقماً عسكرياً صحيحاً لضباط الصف";break;case"employee":if("موظف"!==a.militaryNumber)return'بالنسبة للموظفين يجب أن تكون قيمة الرقم العسكري "موظف"'}return""})();if(c){n(c),l(!1);return}try{let b=localStorage.getItem("token"),c=await fetch("http://127.0.0.1:4001/employees",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${b}`},body:JSON.stringify({name:a.name,rankKey:a.rankKey,militaryNumber:a.militaryNumber,nationalId:a.nationalId,unitId:a.unitId?parseInt(a.unitId):void 0,birthDate:a.birthDate||null,birthPlace:a.birthPlace||null,motherName:a.motherName||null,maritalStatus:a.maritalStatus||null,appointmentDate:a.appointmentDate||null,lastPromotionDate:a.lastPromotionDate||null,bankName:a.bankName||null,bankBranch:a.bankBranch||null,bankAccount:a.bankAccount||null,leaveBalance:a.leaveBalance?parseInt(a.leaveBalance):0,leaveType:a.leaveType||null,employeeStatus:a.employeeStatus||null,qualification:a.qualification||null,qualificationDate:a.qualificationDate||null})});if(c.ok)p("تم إضافة الموظف بنجاح"),setTimeout(()=>{q.push("/employees")},2e3);else{let a=await c.json();n(a.error||"خطأ في إضافة الموظف")}}catch(a){n("خطأ في الاتصال بالخادم")}finally{l(!1)}};return(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,d.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4 space-x-reverse",children:[(0,d.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"إضافة موظف جديد"}),(0,d.jsx)(h(),{href:"/employees",className:"text-blue-600 hover:text-blue-800 text-sm",children:"← العودة لقائمة الموظفين"})]}),(0,d.jsx)("button",{onClick:()=>{localStorage.removeItem("token"),q.push("/login")},className:"bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors text-sm",children:"تسجيل الخروج"})]})})}),(0,d.jsx)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,d.jsx)("div",{className:"lg:col-span-1 order-first lg:order-last",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6 sticky top-8",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"حالة الموظف"}),(0,d.jsx)("div",{className:"space-y-3",children:["مستمر","اجازة","موقوف","غائب عن العمل"].map(c=>(0,d.jsxs)("label",{className:"flex items-center",children:[(0,d.jsx)("input",{type:"radio",name:"employeeStatus",value:c,checked:a.employeeStatus===c,onChange:a=>b(b=>({...b,employeeStatus:a.target.value})),className:"ml-2 text-blue-600 focus:ring-blue-500"}),(0,d.jsx)("span",{className:"text-gray-700",children:c})]},c))}),(0,d.jsxs)("div",{className:"mt-6 pt-6 border-t",children:[(0,d.jsx)("h4",{className:"text-md font-semibold text-gray-900 mb-3",children:"الصورة الشخصية"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)("input",{type:"file",accept:"image/*",title:"اختر الصورة الشخصية","aria-label":"اختر الصورة الشخصية",onChange:a=>{let c=a.target.files?.[0]||null;b(a=>({...a,photo:c}))},className:"w-full text-sm text-gray-500 file:ml-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"}),a.photo&&(0,d.jsxs)("div",{className:"text-sm text-green-600",children:["تم اختيار: ",a.photo.name]})]})]})]})}),(0,d.jsx)("div",{className:"lg:col-span-3",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"إضافة موظف جديد"}),(0,d.jsxs)("form",{onSubmit:s,className:"space-y-6",children:[m&&(0,d.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg",children:m}),o&&(0,d.jsx)("div",{className:"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg",children:o}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"الاسم الكامل *"}),(0,d.jsx)("input",{type:"text",id:"name",value:a.name,onChange:a=>b(b=>({...b,name:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",required:!0})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"rankKey",className:"block text-sm font-medium text-gray-700 mb-2",children:"الرتبة *"}),(0,d.jsxs)("select",{id:"rankKey",value:a.rankKey,onChange:a=>(a=>{let d=c.find(b=>b.key===a),e="";if(d)switch(d.category){case"officer":e="بلا";break;case"employee":e="موظف";break;case"nco":e=""}b(b=>({...b,rankKey:a,militaryNumber:e}))})(a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",required:!0,children:[(0,d.jsx)("option",{value:"",children:"اختر الرتبة"}),(0,d.jsx)("optgroup",{label:"الضباط",children:c.filter(a=>"officer"===a.category).map(a=>(0,d.jsx)("option",{value:a.key,children:a.label},a.key))}),(0,d.jsx)("optgroup",{label:"ضباط الصف",children:c.filter(a=>"nco"===a.category).map(a=>(0,d.jsx)("option",{value:a.key,children:a.label},a.key))}),(0,d.jsx)("optgroup",{label:"الموظفون",children:c.filter(a=>"employee"===a.category).map(a=>(0,d.jsx)("option",{value:a.key,children:a.label},a.key))})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"militaryNumber",className:"block text-sm font-medium text-gray-700 mb-2",children:"الرقم العسكري *"}),(0,d.jsx)("input",{type:"text",id:"militaryNumber",value:a.militaryNumber,onChange:a=>b(b=>({...b,militaryNumber:a.target.value})),placeholder:(()=>{let a=r();if(!a)return"اختر الرتبة أولاً";switch(a.category){case"officer":return"بلا";case"nco":return"أدخل الرقم العسكري";case"employee":return"موظف";default:return""}})(),title:"الرقم العسكري",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",required:!0,readOnly:r()?.category==="officer"||r()?.category==="employee"}),r()&&(0,d.jsxs)("p",{className:"mt-1 text-sm text-gray-500",children:[r()?.category==="officer"&&'للضباط: القيمة "بلا" تلقائياً',r()?.category==="nco"&&"لضباط الصف: أدخل الرقم العسكري",r()?.category==="employee"&&'للموظفين: القيمة "موظف" تلقائياً']})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"nationalId",className:"block text-sm font-medium text-gray-700 mb-2",children:"الرقم الوطني * (12 رقم)"}),(0,d.jsx)("input",{type:"text",id:"nationalId",value:a.nationalId,onChange:a=>b(b=>({...b,nationalId:a.target.value.replace(/\D/g,"").slice(0,12)})),placeholder:"123456789012",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono",required:!0,maxLength:12}),(0,d.jsxs)("p",{className:"mt-1 text-sm text-gray-500",children:[a.nationalId.length,"/12 رقم"]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"unitId",className:"block text-sm font-medium text-gray-700 mb-2",children:"الوحدة"}),(0,d.jsxs)("select",{id:"unitId",value:a.unitId,onChange:a=>b(b=>({...b,unitId:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,d.jsx)("option",{value:"",children:"اختر الوحدة (اختياري)"}),i.map(a=>(0,d.jsx)("option",{value:a.id,children:a.name},a.id))]})]}),(0,d.jsxs)("div",{className:"border-t pt-6 mt-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"البيانات الشخصية"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"birthDate",className:"block text-sm font-medium text-gray-700 mb-2",children:"تاريخ الميلاد"}),(0,d.jsx)("input",{type:"date",id:"birthDate",value:a.birthDate,onChange:a=>b(b=>({...b,birthDate:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"birthPlace",className:"block text-sm font-medium text-gray-700 mb-2",children:"مكان الميلاد"}),(0,d.jsx)("input",{type:"text",id:"birthPlace",value:a.birthPlace,onChange:a=>b(b=>({...b,birthPlace:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"مثال: طرابلس"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"motherName",className:"block text-sm font-medium text-gray-700 mb-2",children:"اسم الأم"}),(0,d.jsx)("input",{type:"text",id:"motherName",value:a.motherName,onChange:a=>b(b=>({...b,motherName:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"اسم الأم الثلاثي"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"maritalStatus",className:"block text-sm font-medium text-gray-700 mb-2",children:"الحالة الاجتماعية"}),(0,d.jsxs)("select",{id:"maritalStatus",value:a.maritalStatus,onChange:a=>b(b=>({...b,maritalStatus:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,d.jsx)("option",{value:"",children:"اختر الحالة الاجتماعية"}),(0,d.jsx)("option",{value:"أعزب",children:"أعزب"}),(0,d.jsx)("option",{value:"متزوج",children:"متزوج"}),(0,d.jsx)("option",{value:"مطلق",children:"مطلق"}),(0,d.jsx)("option",{value:"أرمل",children:"أرمل"})]})]})]})]}),(0,d.jsxs)("div",{className:"border-t pt-6 mt-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"بيانات العمل"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"appointmentDate",className:"block text-sm font-medium text-gray-700 mb-2",children:"تاريخ التعيين"}),(0,d.jsx)("input",{type:"date",id:"appointmentDate",value:a.appointmentDate,onChange:a=>b(b=>({...b,appointmentDate:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"اختر تاريخ التعيين"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"lastPromotionDate",className:"block text-sm font-medium text-gray-700 mb-2",children:"تاريخ آخر ترقية"}),(0,d.jsx)("input",{type:"date",id:"lastPromotionDate",value:a.lastPromotionDate,onChange:a=>b(b=>({...b,lastPromotionDate:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]})]}),(0,d.jsxs)("div",{className:"border-t pt-6 mt-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"بيانات المصرف"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"bankName",className:"block text-sm font-medium text-gray-700 mb-2",children:"اسم المصرف"}),(0,d.jsx)("input",{type:"text",id:"bankName",value:a.bankName,onChange:a=>b(b=>({...b,bankName:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"مثال: مصرف الجمهورية"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"bankBranch",className:"block text-sm font-medium text-gray-700 mb-2",children:"فرع المصرف"}),(0,d.jsx)("input",{type:"text",id:"bankBranch",value:a.bankBranch,onChange:a=>b(b=>({...b,bankBranch:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"مثال: فرع الخمس"})]}),(0,d.jsxs)("div",{className:"md:col-span-2",children:[(0,d.jsx)("label",{htmlFor:"bankAccount",className:"block text-sm font-medium text-gray-700 mb-2",children:"رقم الحساب المصرفي (15 رقم)"}),(0,d.jsx)("input",{type:"text",id:"bankAccount",value:a.bankAccount,onChange:a=>b(b=>({...b,bankAccount:a.target.value.replace(/\D/g,"").slice(0,15)})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono",placeholder:"***************",maxLength:15}),(0,d.jsxs)("p",{className:"mt-1 text-sm text-gray-500",children:[a.bankAccount.length,"/15 رقم"]})]})]})]}),(0,d.jsxs)("div",{className:"border-t pt-6 mt-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"الإجازات"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"leaveBalance",className:"block text-sm font-medium text-gray-700 mb-2",children:"رصيد الإجازات (بالأيام)"}),(0,d.jsx)("input",{type:"number",id:"leaveBalance",value:a.leaveBalance,onChange:a=>b(b=>({...b,leaveBalance:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"30",min:"0",max:"365"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"leaveType",className:"block text-sm font-medium text-gray-700 mb-2",children:"نوع الإجازة الافتراضي"}),(0,d.jsxs)("select",{id:"leaveType",value:a.leaveType,onChange:a=>b(b=>({...b,leaveType:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,d.jsx)("option",{value:"سنوية",children:"سنوية"}),(0,d.jsx)("option",{value:"مرضية",children:"مرضية"}),(0,d.jsx)("option",{value:"بدون مرتب",children:"بدون مرتب"})]})]})]})]}),(0,d.jsxs)("div",{className:"border-t pt-6 mt-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"البيانات الدراسية"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"qualification",className:"block text-sm font-medium text-gray-700 mb-2",children:"المؤهل العلمي"}),(0,d.jsxs)("select",{id:"qualification",value:a.qualification,onChange:a=>b(b=>({...b,qualification:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,d.jsx)("option",{value:"",children:"اختر المؤهل العلمي"}),(0,d.jsx)("option",{value:"ابتدائي",children:"ابتدائي"}),(0,d.jsx)("option",{value:"إعدادي",children:"إعدادي"}),(0,d.jsx)("option",{value:"ثانوي",children:"ثانوي"}),(0,d.jsx)("option",{value:"دبلوم متوسط",children:"دبلوم متوسط"}),(0,d.jsx)("option",{value:"دبلوم عالي",children:"دبلوم عالي"}),(0,d.jsx)("option",{value:"بكالوريوس",children:"بكالوريوس"}),(0,d.jsx)("option",{value:"ماجستير",children:"ماجستير"}),(0,d.jsx)("option",{value:"دكتوراه",children:"دكتوراه"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"qualificationDate",className:"block text-sm font-medium text-gray-700 mb-2",children:"تاريخ الحصول على المؤهل"}),(0,d.jsx)("input",{type:"date",id:"qualificationDate",value:a.qualificationDate,onChange:a=>b(b=>({...b,qualificationDate:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]})]}),(0,d.jsxs)("div",{className:"flex space-x-4 space-x-reverse pt-6",children:[(0,d.jsx)("button",{type:"submit",disabled:k,className:"flex-1 bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed font-medium",children:k?"جاري الحفظ...":"حفظ الموظف"}),(0,d.jsx)(h(),{href:"/employees",className:"flex-1 bg-gray-300 text-gray-700 py-3 px-4 rounded-lg hover:bg-gray-400 transition-colors text-center font-medium",children:"إلغاء"})]})]})]})})]})})]})}},14276:()=>{},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},35459:()=>{},37355:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,48365,23)),Promise.resolve().then(c.t.bind(c,64596,23)),Promise.resolve().then(c.t.bind(c,56186,23)),Promise.resolve().then(c.t.bind(c,67805,23)),Promise.resolve().then(c.t.bind(c,27561,23)),Promise.resolve().then(c.t.bind(c,47569,23)),Promise.resolve().then(c.t.bind(c,42747,23)),Promise.resolve().then(c.t.bind(c,56676,23)),Promise.resolve().then(c.bind(c,97225))},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41666:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>D.a,__next_app__:()=>J,handler:()=>L,pages:()=>I,routeModule:()=>K,tree:()=>H});var d=c(73653),e=c(97714),f=c(85250),g=c(37587),h=c(22369),i=c(1889),j=c(96232),k=c(22841),l=c(46537),m=c(46027),n=c(78559),o=c(75928),p=c(19374),q=c(65971),r=c(261),s=c(79898),t=c(32967),u=c(26713),v=c(40139),w=c(14248),x=c(59580),y=c(57749),z=c(53123),A=c(89745),B=c(86439),C=c(58671),D=c.n(C),E=c(18283),F=c(39818),G={};for(let a in E)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(G[a]=()=>E[a]);c.d(b,G);let H={children:["",{children:["employees",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,10722)),"C:\\Users\\<USER>\\Desktop\\mergeb HR\\apps\\personnel-frontend\\src\\app\\employees\\new\\page.tsx"]}]},{}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,9699))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,47570)),"C:\\Users\\<USER>\\Desktop\\mergeb HR\\apps\\personnel-frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,58671,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,17983,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,15034,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,54693,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,9699))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,I=["C:\\Users\\<USER>\\Desktop\\mergeb HR\\apps\\personnel-frontend\\src\\app\\employees\\new\\page.tsx"],J={require:c,loadChunk:()=>Promise.resolve()},K=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/employees/new/page",pathname:"/employees/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:H},distDir:".next",relativeProjectDir:""});async function L(a,b,d){var C;let G="/employees/new/page";"/index"===G&&(G="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await K.prepare(a,b,{srcPage:G,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(G),{isOnDemandRevalidate:ah}=O,ai=K.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(C=$.routes[ag]??$.dynamicRoutes[ag])?void 0:C.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===K.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&K.isDev&&(aB=aa),K.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...E,tree:H,pages:I,GlobalError:D(),handler:L,routeModule:K,__next_app__:J};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:G,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=K.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return K.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:K,page:G,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),K.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>K.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:K.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!K.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===K.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await K.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await K.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),K.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!K.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&F.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await K.onRequestError(a,b,{routerKind:"App Router",routePath:G,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},42931:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,58671,23)),Promise.resolve().then(c.t.bind(c,56542,23)),Promise.resolve().then(c.t.bind(c,88248,23)),Promise.resolve().then(c.t.bind(c,49743,23)),Promise.resolve().then(c.t.bind(c,96231,23)),Promise.resolve().then(c.t.bind(c,10959,23)),Promise.resolve().then(c.t.bind(c,72041,23)),Promise.resolve().then(c.t.bind(c,95094,23)),Promise.resolve().then(c.t.bind(c,67487,23))},47570:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>h,metadata:()=>g});var d=c(5939),e=c(50157),f=c.n(e);c(14276);let g={title:"نظام إدارة الموارد البشرية - مرجب",description:"نظام شامل لإدارة الموارد البشرية والقوة العمومية",keywords:["الموارد البشرية","إدارة الموظفين","القوة العمومية","مرجب"],authors:[{name:"Mergeb HR System"}],viewport:"width=device-width, initial-scale=1",robots:"noindex, nofollow"};function h({children:a}){return(0,d.jsx)("html",{lang:"ar",dir:"rtl",children:(0,d.jsx)("body",{className:`${f().variable} font-sans antialiased bg-gradient-to-br from-secondary-50 via-white to-primary-50 min-h-screen`,children:(0,d.jsx)("div",{className:"relative",children:a})})})}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65858:(a,b,c)=>{Promise.resolve().then(c.bind(c,10722))},71159:(a,b,c)=>{"use strict";var d=c(30291);c.o(d,"useParams")&&c.d(b,{useParams:function(){return d.useParams}}),c.o(d,"useRouter")&&c.d(b,{useRouter:function(){return d.useRouter}})},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[635,533,496],()=>b(b.s=41666));module.exports=c})();
@echo off
setlocal enabledelayedexpansion

echo === Mergeb HR - Local Run (No Docker) ===

REM 0) Ensure we are in repo root (this file should be at root)
pushd %~dp0 >nul 2>&1

REM 1) Check Node / npm
where node >nul 2>&1
if errorlevel 1 (
  echo [ERROR] Node.js not found. Please install Node.js and try again.
  pause
  exit /b 1
)
where npm >nul 2>&1
if errorlevel 1 (
  echo [ERROR] npm not found. Please ensure Node.js installed with npm in PATH.
  pause
  exit /b 1
)

REM 2) Kill anything on ports 4001/4002 to avoid EADDRINUSE
echo Killing any process listening on ports 4001/4002 (if any)...
for /f "tokens=5" %%p in ('netstat -ano ^| findstr LISTENING ^| findstr :4001') do taskkill /PID %%p /F >nul 2>&1
for /f "tokens=5" %%p in ('netstat -ano ^| findstr LISTENING ^| findstr :4002') do taskkill /PID %%p /F >nul 2>&1

REM 3) Install deps for each app
echo.
echo [1/6] Installing dependencies...
echo Installing Personnel API dependencies...
cd apps\personnel-api && call npm install || goto :fail
cd ..\..

echo Installing Assets API dependencies...
cd apps\assets-api && call npm install || goto :fail
cd ..\..

echo Installing Personnel Frontend dependencies...
cd apps\personnel-frontend && call npm install || goto :fail
cd ..\..

echo Installing Assets Frontend dependencies...
cd apps\assets-frontend && call npm install || goto :fail
cd ..\..

REM 4) Build APIs
echo.
echo [2/6] Building APIs...
echo Building Personnel API...
cd apps\personnel-api && call npm run build || goto :fail
cd ..\..

echo Building Assets API...
cd apps\assets-api && call npm run build || goto :fail
cd ..\..

echo Building Personnel Frontend...
cd apps\personnel-frontend && call npm run build || goto :fail
cd ..\..

echo Building Assets Frontend...
cd apps\assets-frontend && call npm run build || goto :fail
cd ..\.

REM 5) Start servers (in new windows)
echo.
echo [3/6] Starting APIs and Frontends in new windows...
start "Personnel API :4001" cmd /c "cd apps\personnel-api && node dist\index.js"
start "Assets API :4002" cmd /c "cd apps\assets-api && node dist\index.js"
start "Personnel Frontend :3001" cmd /c "cd apps\personnel-frontend && npm run dev"
start "Assets Frontend :3002" cmd /c "cd apps\assets-frontend && npm run dev"

REM 6) Health checks and open browser
echo.
echo [4/6] Waiting 8 seconds for servers to boot...
timeout /t 8 >nul

echo [5/6] Health checks:
where curl >nul 2>&1
if %errorlevel%==0 (
  echo - Personnel API:  http://127.0.0.1:4001/health
  curl.exe -s http://127.0.0.1:4001/health && echo.
  echo - Assets API:     http://127.0.0.1:4002/health
  curl.exe -s http://127.0.0.1:4002/health && echo.
) else (
  echo (curl not found; open these URLs in your browser)
  echo   http://127.0.0.1:4001/health
  echo   http://127.0.0.1:4002/health
)

echo.
echo [6/6] Opening both systems in browser...
start http://127.0.0.1:3001
timeout /t 2 >nul
start http://127.0.0.1:3002

echo.
echo ========================================
echo All set! Both systems are running:
echo ========================================
echo - نظام الموارد البشرية:    http://127.0.0.1:3001
echo - نظام القوة العمومية:    http://127.0.0.1:3002
echo - Personnel API:        http://127.0.0.1:4001
echo - Assets API:           http://127.0.0.1:4002
echo.
echo Test the systems:
echo 1. نظام الموارد البشرية: http://127.0.0.1:3001
echo 2. نظام القوة العمومية: http://127.0.0.1:3002
echo 3. Login with any username/password
echo 4. Explore all features: Search, Add, Delete, Reports

pause
exit /b 0

:fail
echo.
echo [FAILED] A step failed. Check the output above.
pause
exit /b 1


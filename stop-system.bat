@echo off
chcp 65001 >nul
title نظام مرجب - إيقا<PERSON> الخدمات

REM Set colors for better visibility
color 0C

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    إيقاف نظام مرجب                          ║
echo ║              إدارة الموارد البشرية والقوة العمومية          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🛑 إيقاف جميع خدمات النظام...
echo.

REM Kill processes on specific ports
echo [1/2] 🔌 إيقاف APIs...

for /f "tokens=5" %%a in ('netstat -aon 2^>nul ^| find ":4001" ^| find "LISTENING"') do (
    echo       إيقاف Personnel API (المنفذ 4001, PID: %%a)
    taskkill /f /pid %%a >nul 2>&1
    if errorlevel 1 (
        echo          ⚠️  تعذر إيقاف العملية
    ) else (
        echo          ✅ تم إيقاف Personnel API
    )
)

for /f "tokens=5" %%a in ('netstat -aon 2^>nul ^| find ":4002" ^| find "LISTENING"') do (
    echo       إيقاف Assets API (المنفذ 4002, PID: %%a)
    taskkill /f /pid %%a >nul 2>&1
    if errorlevel 1 (
        echo          ⚠️  تعذر إيقاف العملية
    ) else (
        echo          ✅ تم إيقاف Assets API
    )
)

echo.
echo [2/2] 🎨 إيقاف واجهات المستخدم...

for /f "tokens=5" %%a in ('netstat -aon 2^>nul ^| find ":3001" ^| find "LISTENING"') do (
    echo       إيقاف Personnel Frontend (المنفذ 3001, PID: %%a)
    taskkill /f /pid %%a >nul 2>&1
    if errorlevel 1 (
        echo          ⚠️  تعذر إيقاف العملية
    ) else (
        echo          ✅ تم إيقاف Personnel Frontend
    )
)

for /f "tokens=5" %%a in ('netstat -aon 2^>nul ^| find ":3002" ^| find "LISTENING"') do (
    echo       إيقاف Assets Frontend (المنفذ 3002, PID: %%a)
    taskkill /f /pid %%a >nul 2>&1
    if errorlevel 1 (
        echo          ⚠️  تعذر إيقاف العملية
    ) else (
        echo          ✅ تم إيقاف Assets Frontend
    )
)

REM Kill any remaining Node.js processes related to our project
echo.
echo 🧹 تنظيف العمليات المتبقية...
for /f "tokens=2" %%a in ('tasklist /fi "imagename eq node.exe" /fo csv ^| find "node.exe"') do (
    set "pid=%%a"
    set "pid=!pid:"=!"
    for /f "tokens=*" %%b in ('wmic process where "ProcessId=!pid!" get CommandLine /value 2^>nul ^| find "mergeb"') do (
        echo       إيقاف عملية Node.js مرتبطة بالمشروع (PID: !pid!)
        taskkill /f /pid !pid! >nul 2>&1
    )
)

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                   ✅ تم إيقاف النظام بنجاح                  ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 📊 ملخص العمليات:
echo    • تم إيقاف جميع APIs
echo    • تم إيقاف جميع واجهات المستخدم
echo    • تم تنظيف العمليات المتبقية
echo    • تم تحرير جميع المنافذ
echo.
echo 💡 لإعادة تشغيل النظام، استخدم: start-system.bat
echo.
echo اضغط أي مفتاح للخروج...
pause >nul

echo.
echo Thanks for using Mergeb System!
timeout /t 2 >nul

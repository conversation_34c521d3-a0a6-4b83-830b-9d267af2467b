@echo off
chcp 65001 >nul
title نظام مرجب - تشغيل شامل محسن

REM Set colors for better visibility
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    نظام مرجب الشامل                        ║
echo ║              إدارة الموارد البشرية والقوة العمومية          ║
echo ║                      الإصدار 2.0                           ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM Check if Node.js is installed
echo [التحقق من المتطلبات] فحص Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Node.js غير مثبت. يرجى تثبيت Node.js أولاً.
    echo    يمكنك تحميله من: https://nodejs.org
    pause
    exit /b 1
)

REM Kill any existing processes on our ports with better error handling
echo.
echo [1/5] 🧹 تنظيف العمليات السابقة...
echo       تنظيف المنافذ: 3001, 3002, 4001, 4002

for /f "tokens=5" %%a in ('netstat -aon 2^>nul ^| find ":4001" ^| find "LISTENING"') do (
    echo       إيقاف عملية على المنفذ 4001 (PID: %%a)
    taskkill /f /pid %%a >nul 2>&1
)
for /f "tokens=5" %%a in ('netstat -aon 2^>nul ^| find ":4002" ^| find "LISTENING"') do (
    echo       إيقاف عملية على المنفذ 4002 (PID: %%a)
    taskkill /f /pid %%a >nul 2>&1
)
for /f "tokens=5" %%a in ('netstat -aon 2^>nul ^| find ":3001" ^| find "LISTENING"') do (
    echo       إيقاف عملية على المنفذ 3001 (PID: %%a)
    taskkill /f /pid %%a >nul 2>&1
)
for /f "tokens=5" %%a in ('netstat -aon 2^>nul ^| find ":3002" ^| find "LISTENING"') do (
    echo       إيقاف عملية على المنفذ 3002 (PID: %%a)
    taskkill /f /pid %%a >nul 2>&1
)
echo       ✅ تم تنظيف المنافذ بنجاح

REM Check and build system components
echo.
echo [2/5] 🔧 فحص وبناء مكونات النظام...

REM Build shared packages first
echo       📦 بناء الحزم المشتركة...
if exist "packages\shared" (
    cd packages\shared
    if not exist "node_modules" (
        echo          تثبيت dependencies للحزم المشتركة...
        call npm install >nul 2>&1
    )
    echo          بناء الحزم المشتركة...
    call npm run build >nul 2>&1
    cd ..\..
    echo          ✅ تم بناء الحزم المشتركة
) else (
    echo          ⚠️  الحزم المشتركة غير موجودة
)

REM Build APIs
echo       🔌 فحص وبناء APIs...
if not exist "apps\personnel-api\dist\index.js" (
    echo          بناء Personnel API...
    cd apps\personnel-api
    if not exist "node_modules" (
        echo             تثبيت dependencies...
        call npm install >nul 2>&1
    )
    echo             تجميع TypeScript...
    call npm run build >nul 2>&1
    if exist "dist\index.js" (
        echo             ✅ تم بناء Personnel API بنجاح
    ) else (
        echo             ❌ فشل في بناء Personnel API
    )
    cd ..\..
) else (
    echo          ✅ Personnel API جاهز
)

if not exist "apps\assets-api\dist\index.js" (
    echo          بناء Assets API...
    cd apps\assets-api
    if not exist "node_modules" (
        echo             تثبيت dependencies...
        call npm install >nul 2>&1
    )
    echo             تجميع TypeScript...
    call npm run build >nul 2>&1
    if exist "dist\index.js" (
        echo             ✅ تم بناء Assets API بنجاح
    ) else (
        echo             ❌ فشل في بناء Assets API
    )
    cd ..\..
) else (
    echo          ✅ Assets API جاهز
)

REM Check frontend dependencies
echo       🎨 فحص واجهات المستخدم...
if not exist "apps\personnel-frontend\node_modules" (
    echo          تثبيت Personnel Frontend dependencies...
    cd apps\personnel-frontend
    call npm install >nul 2>&1
    if exist "node_modules" (
        echo             ✅ تم تثبيت dependencies بنجاح
    ) else (
        echo             ❌ فشل في تثبيت dependencies
    )
    cd ..\..
) else (
    echo          ✅ Personnel Frontend dependencies جاهزة
)

if not exist "apps\assets-frontend\node_modules" (
    echo          تثبيت Assets Frontend dependencies...
    cd apps\assets-frontend
    call npm install >nul 2>&1
    if exist "node_modules" (
        echo             ✅ تم تثبيت dependencies بنجاح
    ) else (
        echo             ❌ فشل في تثبيت dependencies
    )
    cd ..\..
) else (
    echo          ✅ Assets Frontend dependencies جاهزة
)

REM Start all services with better monitoring
echo.
echo [3/5] 🚀 تشغيل الخدمات...

echo       🔌 تشغيل APIs...
echo          تشغيل Personnel API على المنفذ 4001...
start "نظام الموارد البشرية - API" /min cmd /c "cd apps\personnel-api && echo تشغيل Personnel API... && node dist\index.js"
timeout /t 3 >nul

echo          تشغيل Assets API على المنفذ 4002...
start "نظام القوة العمومية - API" /min cmd /c "cd apps\assets-api && echo تشغيل Assets API... && node dist\index.js"
timeout /t 3 >nul

echo       🎨 تشغيل واجهات المستخدم...
echo          تشغيل Personnel Frontend على المنفذ 3001...
start "نظام الموارد البشرية - واجهة المستخدم" /min cmd /c "cd apps\personnel-frontend && echo تشغيل Personnel Frontend... && npm run dev"
timeout /t 3 >nul

echo          تشغيل Assets Frontend على المنفذ 3002...
start "نظام القوة العمومية - واجهة المستخدم" /min cmd /c "cd apps\assets-frontend && echo تشغيل Assets Frontend... && npm run dev"

echo.
echo       ⏳ انتظار تشغيل جميع الخدمات...
echo          يرجى الانتظار 15 ثانية لضمان تشغيل جميع الخدمات...
timeout /t 15 >nul

REM Comprehensive health checks
echo.
echo [4/5] 🔍 فحص حالة الخدمات...

echo       فحص APIs...
powershell -Command "try { $r = Invoke-WebRequest -Uri 'http://127.0.0.1:4001/health' -UseBasicParsing -TimeoutSec 5; Write-Host '          ✅ Personnel API: متصل ومتاح' -ForegroundColor Green } catch { Write-Host '          ❌ Personnel API: غير متصل' -ForegroundColor Red }"
powershell -Command "try { $r = Invoke-WebRequest -Uri 'http://127.0.0.1:4002/health' -UseBasicParsing -TimeoutSec 5; Write-Host '          ✅ Assets API: متصل ومتاح' -ForegroundColor Green } catch { Write-Host '          ❌ Assets API: غير متصل' -ForegroundColor Red }"

echo       فحص واجهات المستخدم...
powershell -Command "try { $r = Invoke-WebRequest -Uri 'http://127.0.0.1:3001' -UseBasicParsing -TimeoutSec 5; Write-Host '          ✅ Personnel Frontend: متاح' -ForegroundColor Green } catch { Write-Host '          ⏳ Personnel Frontend: لا يزال يتم التحميل...' -ForegroundColor Yellow }"
powershell -Command "try { $r = Invoke-WebRequest -Uri 'http://127.0.0.1:3002' -UseBasicParsing -TimeoutSec 5; Write-Host '          ✅ Assets Frontend: متاح' -ForegroundColor Green } catch { Write-Host '          ⏳ Assets Frontend: لا يزال يتم التحميل...' -ForegroundColor Yellow }"

echo.
echo [5/5] 🌐 فتح المتصفحات...

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🎉 النظام جاهز للاستخدام! 🎉              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🌐 الروابط المتاحة:
echo    ┌─ نظام الموارد البشرية:     http://127.0.0.1:3001
echo    └─ نظام القوة العمومية:     http://127.0.0.1:3002
echo.
echo 🔧 APIs المتاحة:
echo    ┌─ Personnel API:           http://127.0.0.1:4001
echo    └─ Assets API:             http://127.0.0.1:4002
echo.
echo ✨ الميزات الجديدة في الإصدار 2.0:
echo    • 🎨 تصميم حديث ومتجاوب مع Tailwind CSS
echo    • 🚀 أداء محسن وسرعة فائقة
echo    • 📱 دعم كامل للأجهزة المحمولة
echo    • 🔍 بحث متقدم وذكي
echo    • 📊 تقارير تفاعلية ومرئية
echo    • 🔐 أمان محسن وحماية البيانات
echo    • 🌍 دعم كامل للغة العربية (RTL)
echo    • ♿ إمكانية الوصول المحسنة
echo.
echo 🔐 تسجيل الدخول:
echo    اسم المستخدم: أي قيمة
echo    كلمة المرور: أي قيمة
echo    (النظام يستخدم مصادقة تجريبية للتطوير)
echo.

REM Open browsers with delay
echo 🌐 فتح المتصفحات...
echo    فتح نظام الموارد البشرية...
start http://127.0.0.1:3001
timeout /t 3 >nul
echo    فتح نظام القوة العمومية...
start http://127.0.0.1:3002

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      معلومات مفيدة                          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 💡 نصائح للاستخدام:
echo    • استخدم Ctrl+C في نوافذ الخدمات لإيقافها
echo    • يمكنك إعادة تشغيل الملف في أي وقت
echo    • تأكد من إغلاق جميع الخدمات قبل إعادة التشغيل
echo.
echo 🛠️  استكشاف الأخطاء:
echo    • إذا لم تعمل الخدمة، تحقق من نافذة الخدمة للأخطاء
echo    • تأكد من أن المنافذ 3001, 3002, 4001, 4002 غير مستخدمة
echo    • في حالة المشاكل، أعد تشغيل الملف
echo.
echo 📞 الدعم الفني:
echo    • تحقق من ملفات السجل في نوافذ الخدمات
echo    • راجع ملف README.md للمزيد من المعلومات
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║  🎯 النظام يعمل الآن! يمكنك البدء في استخدام التطبيقات      ║
echo ║     لإيقاف النظام، أغلق جميع نوافذ الخدمات المفتوحة        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo اضغط أي مفتاح لإنهاء هذه النافذة (الخدمات ستبقى تعمل)...
pause >nul

REM Final message
echo.
echo شكراً لاستخدام نظام مرجب! 🙏
echo النظام يعمل في الخلفية...
timeout /t 2 >nul

@echo off
chcp 65001 >nul
title Mergeb HR System - Quick Start

echo ========================================
echo    نظام إدارة الموارد البشرية - مرجب
echo ========================================
echo.

REM Kill any existing processes on our ports
echo [1/4] تنظيف العمليات السابقة...
for /f "tokens=5" %%a in ('netstat -aon ^| find ":4001" ^| find "LISTENING"') do taskkill /f /pid %%a >nul 2>&1
for /f "tokens=5" %%a in ('netstat -aon ^| find ":4002" ^| find "LISTENING"') do taskkill /f /pid %%a >nul 2>&1
for /f "tokens=5" %%a in ('netstat -aon ^| find ":3001" ^| find "LISTENING"') do taskkill /f /pid %%a >nul 2>&1
for /f "tokens=5" %%a in ('netstat -aon ^| find ":3002" ^| find "LISTENING"') do taskkill /f /pid %%a >nul 2>&1

REM Check if builds exist, if not build them
echo.
echo [2/4] فحص وبناء النظام...

if not exist "apps\personnel-api\dist\index.js" (
    echo بناء Personnel API...
    cd apps\personnel-api
    call npm install >nul 2>&1
    call npm run build >nul 2>&1
    cd ..\..
)

if not exist "apps\assets-api\dist\index.js" (
    echo بناء Assets API...
    cd apps\assets-api
    call npm install >nul 2>&1
    call npm run build >nul 2>&1
    cd ..\..
)

REM Check frontend dependencies
if not exist "apps\personnel-frontend\node_modules" (
    echo تثبيت Personnel Frontend dependencies...
    cd apps\personnel-frontend
    call npm install >nul 2>&1
    cd ..\..
)

if not exist "apps\assets-frontend\node_modules" (
    echo تثبيت Assets Frontend dependencies...
    cd apps\assets-frontend
    call npm install >nul 2>&1
    cd ..\..
)

REM Start all services
echo.
echo [3/4] تشغيل الخدمات...
start "Personnel API :4001" /min cmd /c "cd apps\personnel-api && node dist\index.js"
timeout /t 2 >nul
start "Assets API :4002" /min cmd /c "cd apps\assets-api && node dist\index.js"
timeout /t 2 >nul
start "Personnel Frontend :3001" /min cmd /c "cd apps\personnel-frontend && npm run dev"
timeout /t 2 >nul
start "Assets Frontend :3002" /min cmd /c "cd apps\assets-frontend && npm run dev"

echo انتظار تشغيل الخدمات...
timeout /t 8 >nul

REM Health checks
echo.
echo [4/4] فحص الخدمات...
powershell -Command "try { $r = Invoke-WebRequest -Uri 'http://127.0.0.1:4001/health' -UseBasicParsing -TimeoutSec 3; Write-Host '✓ Personnel API: متصل' } catch { Write-Host '✗ Personnel API: غير متصل' }"
powershell -Command "try { $r = Invoke-WebRequest -Uri 'http://127.0.0.1:4002/health' -UseBasicParsing -TimeoutSec 3; Write-Host '✓ Assets API: متصل' } catch { Write-Host '✗ Assets API: غير متصل' }"

echo.
echo ========================================
echo           النظام جاهز للاستخدام!
echo ========================================
echo.
echo 🌐 نظام الموارد البشرية:    http://127.0.0.1:3001
echo 🌐 نظام القوة العمومية:    http://127.0.0.1:3002
echo.
echo 📋 الميزات المتاحة:
echo   • تصميم عصري وجذاب
echo   • بحث متقدم وسريع
echo   • إمكانيات حذف آمنة
echo   • أداء فائق السرعة
echo   • واجهات سهلة الاستخدام
echo.
echo 🔐 تسجيل الدخول: أي اسم مستخدم وكلمة مرور
echo.

REM Open browsers
echo فتح المتصفحات...
start http://127.0.0.1:3001
timeout /t 2 >nul
start http://127.0.0.1:3002

echo.
echo اضغط أي مفتاح للخروج...
pause >nul
